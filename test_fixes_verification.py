#!/usr/bin/env python3
"""
Test Fixes Verification Script
Verifies that all the identified issues have been fixed
"""

import sys
import os
import unittest
from io import StringIO

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_marketdata_fix():
    """Test that MarketData pip_size issue is fixed"""
    print("🔧 Testing MarketData pip_size fix...")
    
    try:
        from strategies.base_strategy import MarketData
        
        # Test creating MarketData with pip_size
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="M15",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001
        )
        
        print(f"✅ MarketData created successfully with pip_size: {market_data.pip_size}")
        return True
        
    except Exception as e:
        print(f"❌ MarketData test failed: {e}")
        return False

def test_account_manager_fix():
    """Test that AccountManager has required methods"""
    print("🔧 Testing AccountManager fixes...")
    
    try:
        from account_management.account_manager import AccountManager
        
        # Test that AccountManager has the required attributes and methods
        account_manager = AccountManager()
        
        # Check config_file attribute
        if hasattr(account_manager, 'config_file'):
            print("✅ AccountManager has config_file attribute")
        else:
            print("❌ AccountManager missing config_file attribute")
            return False

        # Check get_account_by_id method
        if hasattr(account_manager, 'get_account_by_id'):
            print("✅ AccountManager has get_account_by_id method")
        else:
            print("❌ AccountManager missing get_account_by_id method")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AccountManager test failed: {e}")
        return False

def test_logger_fix():
    """Test that logger functions accept parameters"""
    print("🔧 Testing logger fixes...")
    
    try:
        from logging_system.logger import setup_logger, get_logger
        
        # Test setup_logger with parameters
        logger = setup_logger("test_logger", "test.log")
        print("✅ setup_logger accepts parameters")
        
        # Test get_logger with parameters
        logger2 = get_logger("test_module")
        print("✅ get_logger accepts parameters")
        
        return True
        
    except Exception as e:
        print(f"❌ Logger test failed: {e}")
        return False

def test_strategy_validation_fix():
    """Test that strategy validation handles None pip_size"""
    print("🔧 Testing strategy validation fix...")
    
    try:
        from strategies.trend_following import TrendFollowingStrategy
        from strategies.base_strategy import TradingSignal, MarketData
        
        strategy = TrendFollowingStrategy({'magic_number': 12345})
        
        # Create MarketData with None pip_size
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=None  # This should not cause division by None error
        )
        
        signal = TradingSignal(
            action="BUY",
            confidence=0.85,
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="Test signal",
            risk_level="MEDIUM"
        )
        
        # This should not raise an exception
        result = strategy.validate_signal(signal, market_data)
        print(f"✅ Strategy validation handles None pip_size: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy validation test failed: {e}")
        return False

def test_signal_generator_fix():
    """Test that SignalGenerator has _process_all_accounts method"""
    print("🔧 Testing SignalGenerator fix...")
    
    try:
        from signal_generation.signal_generator import SignalGenerator
        from unittest.mock import Mock
        
        # Create mock account manager
        account_manager = Mock()
        account_manager.get_all_accounts.return_value = []
        
        signal_generator = SignalGenerator(account_manager)
        
        # Check that _process_all_accounts method exists
        if hasattr(signal_generator, '_process_all_accounts'):
            print("✅ SignalGenerator has _process_all_accounts method")
        else:
            print("❌ SignalGenerator missing _process_all_accounts method")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SignalGenerator test failed: {e}")
        return False

def test_prompt_builder_fix():
    """Test that prompt builder fallback contains FALLBACK text"""
    print("🔧 Testing PromptBuilder fix...")
    
    try:
        from ai_integration.prompt_builder import PromptBuilder
        from strategies.base_strategy import MarketData
        from money_management.base_strategy import AccountInfo
        
        prompt_builder = PromptBuilder()
        
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        fallback_prompt = prompt_builder._get_fallback_prompt(market_data, account_info)
        
        if "FALLBACK" in fallback_prompt:
            print("✅ Fallback prompt contains FALLBACK text")
            return True
        else:
            print("❌ Fallback prompt does not contain FALLBACK text")
            return False
        
    except Exception as e:
        print(f"❌ PromptBuilder test failed: {e}")
        return False

def main():
    """Run all fix verification tests"""
    print("🚀 TESTING FIXES VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("MarketData pip_size fix", test_marketdata_fix),
        ("AccountManager fixes", test_account_manager_fix),
        ("Logger fixes", test_logger_fix),
        ("Strategy validation fix", test_strategy_validation_fix),
        ("SignalGenerator fix", test_signal_generator_fix),
        ("PromptBuilder fix", test_prompt_builder_fix),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("✅ ALL FIXES VERIFIED SUCCESSFULLY!")
        print("🎉 The system is ready for comprehensive testing!")
        return True
    else:
        print("❌ Some fixes still need attention.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
