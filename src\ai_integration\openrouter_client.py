"""
OpenRouter AI Client Implementation
"""

import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_ai_provider import BaseAIProvider, AIProviderConfig
from .response_sanitizer import response_sanitizer
from logging_system.logger import get_logger

logger = get_logger(__name__)


class OpenRouterClient(BaseAIProvider):
    """OpenRouter AI API client implementation"""
    
    def __init__(self, config: AIProviderConfig):
        super().__init__(config)
        
        # Ensure API URL ends with correct endpoint
        if not self.config.api_url.endswith('/chat/completions'):
            self.config.api_url = self.config.api_url.rstrip('/') + '/chat/completions'
        
        # OpenRouter specific headers
        self.headers = {
            'Authorization': f'Bearer {self.config.api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': self.config.extra_params.get('site_url', 'https://fulltrade-ai.com'),
            'X-Title': self.config.extra_params.get('app_name', 'FullTrade AI Trading System')
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session and not self.session.closed:
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing OpenRouter session: {e}")
            finally:
                self.session = None
    
    async def generate_trading_decision(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate trading decision using OpenRouter AI"""

        async with self.semaphore:
            try:
                # Prepare the request payload
                payload = {
                    'model': self.config.model,
                    'messages': [
                        {
                            'role': 'system',
                            'content': self.get_system_prompt()
                        },
                        {
                            'role': 'user',
                            'content': prompt
                        }
                    ],
                    'temperature': self.config.temperature,
                    'max_tokens': self.config.max_tokens,
                    'top_p': self.config.top_p
                }

                # Ensure session exists and is not closed
                if not self.session or self.session.closed:
                    self.session = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                    )

                async with self.session.post(
                    self.config.api_url,
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return self._parse_ai_response(result, context)
                    else:
                        error_text = await response.text()
                        logger.error(f"OpenRouter API error {response.status}: {error_text}")
                        return self._get_error_response(f"API Error: {response.status}")

            except asyncio.TimeoutError:
                logger.error("OpenRouter API request timeout")
                return self._get_error_response("Request timeout")
            except Exception as e:
                logger.error(f"Error calling OpenRouter API: {e}")
                return self._get_error_response(str(e))
    
    async def get_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None
    ) -> str:
        """Get raw completion from OpenRouter"""

        async with self.semaphore:
            try:
                messages = []

                if system_prompt:
                    messages.append({
                        'role': 'system',
                        'content': system_prompt
                    })

                messages.append({
                    'role': 'user',
                    'content': prompt
                })

                payload = {
                    'model': self.config.model,
                    'messages': messages,
                    'temperature': self.config.temperature,
                    'max_tokens': self.config.max_tokens,
                    'top_p': self.config.top_p
                }

                # Ensure session exists and is not closed
                if not self.session or self.session.closed:
                    self.session = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                    )

                async with self.session.post(
                    self.config.api_url,
                    headers=self.headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        choices = result.get('choices', [])
                        if choices:
                            return choices[0].get('message', {}).get('content', '')
                        return ''
                    else:
                        error_text = await response.text()
                        logger.error(f"OpenRouter completion error {response.status}: {error_text}")
                        return f"Error: {response.status}"

            except Exception as e:
                logger.error(f"Error getting OpenRouter completion: {e}")
                return f"Error: {str(e)}"
    
    async def validate_connection(self) -> bool:
        """Validate connection to OpenRouter API"""
        try:
            # Ensure we have a session
            if not self.session or self.session.closed:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                )

            test_prompt = "Respond with just 'OK' if you can read this message."
            response = await self.get_completion(test_prompt)

            # Check if response contains OK (case insensitive)
            if response and isinstance(response, str):
                return 'OK' in response.upper() or 'ok' in response.lower()
            return False
        except Exception as e:
            logger.error(f"OpenRouter connection validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available OpenRouter models"""
        # Common OpenRouter models - in a real implementation, 
        # this could fetch from OpenRouter's models endpoint
        return [
            'anthropic/claude-3.5-sonnet',
            'anthropic/claude-3-haiku',
            'anthropic/claude-3-opus',
            'openai/gpt-4o',
            'openai/gpt-4o-mini',
            'openai/gpt-4-turbo',
            'openai/gpt-3.5-turbo',
            'meta-llama/llama-3.1-405b-instruct',
            'meta-llama/llama-3.1-70b-instruct',
            'meta-llama/llama-3.1-8b-instruct',
            'google/gemini-pro-1.5',
            'google/gemini-flash-1.5',
            'mistralai/mistral-large',
            'mistralai/mistral-medium',
            'cohere/command-r-plus'
        ]
    
    def _parse_ai_response(self, response: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Parse OpenRouter API response and extract trading signals with robust error handling"""
        try:
            # Extract the AI's response text (OpenAI-compatible format)
            choices = response.get('choices', [])
            if not choices:
                return self._get_error_response("No choices in response")

            message = choices[0].get('message', {})
            text = message.get('content', '')

            if not text:
                return self._get_error_response("Empty response from AI")

            logger.debug(f"🔍 Raw AI response length: {len(text)} characters")

            # Use robust JSON extraction with sanitization
            parsed_response = response_sanitizer.extract_json_from_text(text)

            if parsed_response:
                # Validate required fields
                required_fields = ['action', 'confidence']
                if all(field in parsed_response for field in required_fields):
                    # Add metadata
                    parsed_response['timestamp'] = datetime.now().isoformat()
                    parsed_response['model'] = self.config.model
                    parsed_response['provider'] = self.config.provider_type.value
                    parsed_response['raw_response'] = text

                    if context:
                        parsed_response['context'] = context

                    # Add usage information if available
                    usage = response.get('usage', {})
                    if usage:
                        parsed_response['usage'] = usage

                    # Ensure all required fields have default values
                    parsed_response.setdefault('reasoning', 'AI provided trading decision')
                    parsed_response.setdefault('risk_level', 'MEDIUM')
                    parsed_response.setdefault('entry_price', None)
                    parsed_response.setdefault('stop_loss', None)
                    parsed_response.setdefault('take_profit', None)
                    parsed_response.setdefault('take_profit_levels', None)
                    parsed_response.setdefault('market_analysis', 'Market analysis provided')
                    parsed_response.setdefault('strategy_alignment', 'Strategy aligned')
                    parsed_response.setdefault('risk_assessment', 'Risk assessed')
                    parsed_response.setdefault('additional_signals', [])

                    logger.info(f"✅ Successfully parsed AI trading decision: {parsed_response['action']} (confidence: {parsed_response['confidence']:.2%})")
                    return parsed_response
                else:
                    logger.warning(f"⚠️ Missing required fields in AI response, using intelligent fallback")
                    return response_sanitizer.create_intelligent_fallback(text, "Missing required fields")
            else:
                logger.warning("⚠️ Could not extract JSON from AI response, creating intelligent fallback")
                return response_sanitizer.create_intelligent_fallback(text, "JSON extraction failed")

        except Exception as e:
            logger.error(f"❌ Critical error parsing OpenRouter response: {e}")
            return self._get_error_response(f"Response parsing error: {str(e)}")
    
    async def get_models_from_api(self) -> List[Dict[str, Any]]:
        """Fetch available models from OpenRouter API"""
        try:
            models_url = self.config.api_url.replace('/chat/completions', '/models')
            
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                )
            
            async with self.session.get(models_url, headers=self.headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', [])
                else:
                    logger.error(f"Failed to fetch models: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error fetching OpenRouter models: {e}")
            return []
