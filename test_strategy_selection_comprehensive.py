#!/usr/bin/env python3
"""
Comprehensive test to verify dynamic strategy selection is working correctly
"""

import sys
import os
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def test_strategy_selector_with_different_conditions():
    """Test strategy selector with various market conditions"""
    print("🔍 Testing Strategy Selector with Different Market Conditions...")
    
    try:
        from strategy_selection.dynamic_strategy_selector import DynamicStrategySelector
        from strategies.base_strategy import MarketData
        from money_management.base_strategy import AccountInfo
        
        selector = DynamicStrategySelector()
        
        # Account configuration (similar to actual config)
        account_config = {
            'account_id': 'test_account',
            'strategy': 'trend_following',  # Default strategy
            'strategy_selection': {
                'mode': 'dynamic',
                'available_strategies': ['trend_following', 'mean_reversion', 'breakout'],
                'selection_criteria': {
                    'volatility_threshold_trending': 0.0012,
                    'volatility_threshold_volatile': 0.002,
                    'volume_threshold': 800
                }
            }
        }
        
        account_info = AccountInfo(
            balance=1000.0, equity=950.0, margin=100.0,
            free_margin=850.0, margin_level=950.0,
            currency="USD", leverage=100
        )
        
        # Test scenarios with different market conditions
        test_scenarios = [
            {
                'name': 'Low Volatility (Ranging)',
                'volatility': 0.0005,
                'volume': 500,
                'expected_preference': 'mean_reversion'
            },
            {
                'name': 'Medium Volatility (Trending)', 
                'volatility': 0.0015,
                'volume': 1000,
                'expected_preference': 'trend_following'
            },
            {
                'name': 'High Volatility (Breakout)',
                'volatility': 0.0025,
                'volume': 2000,
                'expected_preference': 'breakout'
            },
            {
                'name': 'Very Low Volatility',
                'volatility': 0.0003,
                'volume': 300,
                'expected_preference': 'mean_reversion'
            },
            {
                'name': 'Very High Volatility',
                'volatility': 0.0035,
                'volume': 3000,
                'expected_preference': 'breakout'
            }
        ]
        
        results = {}
        
        print("   📊 Testing Different Market Conditions:")
        for scenario in test_scenarios:
            market_data = MarketData(
                symbol='EURUSD',
                timeframe='H1',
                candles=[],
                current_price=1.1000,
                spread=1.5,
                volume=scenario['volume'],
                volatility=scenario['volatility'],
                pip_size=0.0001,
                pip_value=1.0,
                min_volume=0.01,
                max_volume=100.0
            )
            
            selected_strategy = selector.select_strategy(
                account_config, market_data, account_info
            )
            
            results[scenario['name']] = selected_strategy
            
            print(f"      {scenario['name']}: {selected_strategy}")
            print(f"         Volatility: {scenario['volatility']:.4f}, Volume: {scenario['volume']}")
            print(f"         Expected: {scenario['expected_preference']}")
        
        # Check if we get different strategies for different conditions
        unique_strategies = set(results.values())
        
        print(f"\n   📈 Results Summary:")
        print(f"      Unique strategies selected: {len(unique_strategies)}")
        print(f"      Strategies used: {list(unique_strategies)}")
        
        if len(unique_strategies) > 1:
            print("   ✅ Dynamic strategy selection is working - different strategies for different conditions!")
            return True
        else:
            print("   ⚠️  Only one strategy selected across all conditions - may need tuning")
            return False
        
    except Exception as e:
        print(f"   ❌ Strategy selector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_static_vs_dynamic_mode():
    """Test the difference between static and dynamic mode"""
    print("\n🔍 Testing Static vs Dynamic Mode...")
    
    try:
        from strategy_selection.dynamic_strategy_selector import DynamicStrategySelector
        from strategies.base_strategy import MarketData
        from money_management.base_strategy import AccountInfo
        
        selector = DynamicStrategySelector()
        
        # Test market data
        market_data = MarketData(
            symbol='EURUSD', timeframe='H1', candles=[], current_price=1.1000,
            spread=1.5, volume=1500, volatility=0.0020,  # High volatility
            pip_size=0.0001, pip_value=1.0, min_volume=0.01, max_volume=100.0
        )
        
        account_info = AccountInfo(
            balance=1000.0, equity=950.0, margin=100.0,
            free_margin=850.0, margin_level=950.0,
            currency="USD", leverage=100
        )
        
        # Static mode configuration
        static_config = {
            'strategy': 'trend_following',
            'strategy_selection': {
                'mode': 'static'  # Not dynamic
            }
        }
        
        # Dynamic mode configuration
        dynamic_config = {
            'strategy': 'trend_following',
            'strategy_selection': {
                'mode': 'dynamic',
                'available_strategies': ['trend_following', 'mean_reversion', 'breakout']
            }
        }
        
        static_result = selector.select_strategy(static_config, market_data, account_info)
        dynamic_result = selector.select_strategy(dynamic_config, market_data, account_info)
        
        print(f"   📊 Static Mode Result: {static_result}")
        print(f"   📊 Dynamic Mode Result: {dynamic_result}")
        
        if static_result == 'trend_following' and dynamic_result != static_result:
            print("   ✅ Mode switching works correctly!")
            return True
        elif static_result == 'trend_following' and dynamic_result == static_result:
            print("   ⚠️  Dynamic mode selected same strategy as static - may be correct for conditions")
            return True
        else:
            print("   ❌ Unexpected behavior in mode switching")
            return False
        
    except Exception as e:
        print(f"   ❌ Static vs dynamic test failed: {e}")
        return False

def analyze_current_account_configurations():
    """Analyze current account configurations for dynamic strategy selection"""
    print("\n🔍 Analyzing Current Account Configurations...")
    
    try:
        config_path = Path("config/accounts.json")
        if not config_path.exists():
            print("   ❌ accounts.json not found")
            return False
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        accounts = config.get('accounts', [])
        
        dynamic_accounts = []
        static_accounts = []
        
        for account in accounts:
            account_id = account.get('account_id', 'unknown')
            strategy_selection = account.get('strategy_selection', {})
            mode = strategy_selection.get('mode', 'static')
            
            if mode == 'dynamic':
                available_strategies = strategy_selection.get('available_strategies', [])
                dynamic_accounts.append({
                    'id': account_id,
                    'default_strategy': account.get('strategy', 'unknown'),
                    'available_strategies': available_strategies
                })
            else:
                static_accounts.append({
                    'id': account_id,
                    'strategy': account.get('strategy', 'unknown')
                })
        
        print(f"   📊 Total Accounts: {len(accounts)}")
        print(f"   📊 Dynamic Strategy Accounts: {len(dynamic_accounts)}")
        print(f"   📊 Static Strategy Accounts: {len(static_accounts)}")
        
        if dynamic_accounts:
            print("\n   🎯 Dynamic Strategy Accounts:")
            for acc in dynamic_accounts:
                print(f"      {acc['id']}: default={acc['default_strategy']}, available={acc['available_strategies']}")
        
        if static_accounts:
            print("\n   📌 Static Strategy Accounts:")
            for acc in static_accounts:
                print(f"      {acc['id']}: {acc['strategy']}")
        
        return len(dynamic_accounts) > 0
        
    except Exception as e:
        print(f"   ❌ Configuration analysis failed: {e}")
        return False

def check_strategy_selection_logging():
    """Check if strategy selection logging is comprehensive enough"""
    print("\n🔍 Checking Strategy Selection Logging...")
    
    try:
        # Check the signal generator code to see logging behavior
        signal_gen_path = Path("src/signal_generation/signal_generator.py")
        
        with open(signal_gen_path, 'r') as f:
            content = f.read()
        
        # Check for strategy selection logging
        has_dynamic_logging = "DYNAMIC_STRATEGY" in content
        has_selection_logging = "select_strategy" in content
        logs_all_selections = "selected_strategy_name" in content
        
        print(f"   📊 Has dynamic strategy logging: {has_dynamic_logging}")
        print(f"   📊 Has strategy selection calls: {has_selection_logging}")
        print(f"   📊 Logs strategy selections: {logs_all_selections}")
        
        # The issue: only logs when strategy CHANGES, not when it's selected
        if "if selected_strategy_name != strategy_name:" in content:
            print("   ⚠️  ISSUE FOUND: Only logs when strategy changes from default!")
            print("   💡 This means if dynamic selector chooses the same strategy as default,")
            print("      no log message is generated, making it appear like static selection.")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Logging check failed: {e}")
        return False

def main():
    """Run comprehensive strategy selection tests"""
    print("🚀 COMPREHENSIVE DYNAMIC STRATEGY SELECTION TEST")
    print("=" * 60)
    
    tests = [
        test_strategy_selector_with_different_conditions,
        test_static_vs_dynamic_mode,
        analyze_current_account_configurations,
        check_strategy_selection_logging,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed >= 3:
        print("✅ DYNAMIC STRATEGY SELECTION IS WORKING!")
        print("\n🎯 Key Findings:")
        print("   • Accounts are configured for dynamic strategy selection")
        print("   • Strategy selector responds to different market conditions")
        print("   • Multiple strategies are available and being selected")
        print("\n⚠️  LOGGING ISSUE IDENTIFIED:")
        print("   • System only logs when strategy CHANGES from default")
        print("   • If dynamic selector chooses same strategy as default, no log appears")
        print("   • This makes it look like static selection when it's actually dynamic")
        print("\n💡 RECOMMENDATION:")
        print("   • Enhance logging to show ALL strategy selections, not just changes")
        print("   • Add market condition analysis to logs")
        print("   • Show strategy selection reasoning")
        return True
    else:
        print("❌ DYNAMIC STRATEGY SELECTION NEEDS ATTENTION")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
