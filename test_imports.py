#!/usr/bin/env python3
"""
Test script to check imports and identify issues
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

print("🔍 Testing imports...")
print(f"Python path: {sys.path[0]}")
print(f"Source directory: {src_dir}")
print(f"Source directory exists: {src_dir.exists()}")

try:
    print("Testing basic imports...")
    from logging_system.logger import setup_logger, get_logger
    print("✅ Logging system import successful")
    
    from account_management.account_manager import AccountManager
    print("✅ Account manager import successful")
    
    from signal_generation.signal_generator import SignalGenerator
    print("✅ Signal generator import successful")
    
    from trade_management.trade_manager import TradeManager
    print("✅ Trade manager import successful")
    
    from scheduling.scheduler_coordinator import scheduler_coordinator
    print("✅ Scheduler coordinator import successful")
    
    # Test enhanced components
    from error_recovery.recovery_manager import error_recovery_manager
    print("✅ Error recovery manager import successful")
    
    from performance.cache_manager import cache_manager
    print("✅ Cache manager import successful")
    
    from logging_system.logging_standards import create_logger
    print("✅ Standardized logging import successful")
    
    print("\n🎉 All imports successful!")
    
    # Test basic functionality
    print("\n🔧 Testing basic functionality...")
    
    logger = setup_logger()
    print("✅ Logger setup successful")
    
    account_manager = AccountManager()
    print("✅ Account manager creation successful")
    
    print("\n✅ Basic functionality test completed successfully!")
    
except Exception as e:
    print(f"❌ Import/functionality test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
