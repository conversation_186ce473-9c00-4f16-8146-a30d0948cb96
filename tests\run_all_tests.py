#!/usr/bin/env python3
"""
Comprehensive Test Runner for Trading System
Runs all tests and generates detailed reports for pre-production validation
"""

import unittest
import sys
import os
import time
import json
from pathlib import Path
from datetime import datetime
import traceback

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Import test modules
from test_trading_system import run_tests as run_basic_tests
from test_comprehensive_trading_system import *
from test_real_world_integration import *
from test_enhanced_system_components import run_enhanced_tests

class TestResult:
    """Custom test result class for detailed reporting"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.failures = []
        self.errors = []
        self.test_details = []
    
    def add_test_result(self, test_name, status, duration, error_msg=None):
        """Add individual test result"""
        self.test_details.append({
            'test_name': test_name,
            'status': status,
            'duration': duration,
            'error_msg': error_msg,
            'timestamp': datetime.now().isoformat()
        })
        
        self.total_tests += 1
        if status == 'PASS':
            self.passed_tests += 1
        elif status == 'FAIL':
            self.failed_tests += 1
            if error_msg:
                self.failures.append(f"{test_name}: {error_msg}")
        elif status == 'ERROR':
            self.error_tests += 1
            if error_msg:
                self.errors.append(f"{test_name}: {error_msg}")
        elif status == 'SKIP':
            self.skipped_tests += 1
    
    def get_summary(self):
        """Get test summary"""
        duration = (self.end_time - self.start_time).total_seconds() if self.end_time and self.start_time else 0
        
        return {
            'total_tests': self.total_tests,
            'passed': self.passed_tests,
            'failed': self.failed_tests,
            'errors': self.error_tests,
            'skipped': self.skipped_tests,
            'success_rate': (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0,
            'total_duration': duration,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None
        }

class ComprehensiveTestRunner:
    """Comprehensive test runner with detailed reporting"""
    
    def __init__(self):
        self.result = TestResult()
        self.test_suites = []
        self.setup_test_suites()
    
    def setup_test_suites(self):
        """Setup all test suites"""
        
        # Basic functionality tests
        self.test_suites.append({
            'name': 'Account Management Tests',
            'test_class': TestAccountManagement,
            'description': 'Tests for account loading, configuration, and management'
        })
        
        self.test_suites.append({
            'name': 'Money Management Tests',
            'test_class': TestMoneyManagementStrategies,
            'description': 'Tests for all money management strategies and calculations'
        })
        
        self.test_suites.append({
            'name': 'Trading Strategy Tests',
            'test_class': TestTradingStrategies,
            'description': 'Tests for all trading strategies and AI prompt generation'
        })
        
        self.test_suites.append({
            'name': 'AI Integration Tests',
            'test_class': TestAIIntegration,
            'description': 'Tests for AI client, prompt building, and response handling'
        })
        
        self.test_suites.append({
            'name': 'MT5 Integration Tests',
            'test_class': TestMT5Integration,
            'description': 'Tests for MT5 client, market data, and order execution'
        })
        
        # Real-world integration tests
        self.test_suites.append({
            'name': 'Real-World Signal Execution Tests',
            'test_class': TestRealWorldSignalExecution,
            'description': 'Tests for complete signal generation and execution lifecycle'
        })

        # Enhanced system component tests
        self.test_suites.append({
            'name': 'Enhanced System Components Tests',
            'test_function': run_enhanced_tests,
            'description': 'Tests for error recovery, performance optimization, logging, and config validation'
        })
    
    def run_test_suite(self, suite_info):
        """Run a single test suite"""
        print(f"\n{'='*60}")
        print(f"Running: {suite_info['name']}")
        print(f"Description: {suite_info['description']}")
        print(f"{'='*60}")

        start_time = time.time()

        # Handle both test classes and test functions
        if 'test_class' in suite_info:
            # Traditional unittest class
            suite = unittest.TestLoader().loadTestsFromTestCase(suite_info['test_class'])
            runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
            result = runner.run(suite)
            duration = time.time() - start_time

            # Process unittest results
            for test, error in result.failures:
                self.result.add_test_result(
                    str(test), 'FAIL', duration / result.testsRun, str(error)
                )

            for test, error in result.errors:
                self.result.add_test_result(
                    str(test), 'ERROR', duration / result.testsRun, str(error)
                )

            # Add successful tests
            successful_tests = result.testsRun - len(result.failures) - len(result.errors)
            for i in range(successful_tests):
                self.result.add_test_result(
                    f"{suite_info['name']}_test_{i}", 'PASS', duration / result.testsRun
                )

        elif 'test_function' in suite_info:
            # Custom test function
            try:
                test_results = suite_info['test_function']()
                duration = time.time() - start_time

                if test_results.get('success', False):
                    self.result.add_test_result(
                        suite_info['name'], 'PASS', duration
                    )
                else:
                    self.result.add_test_result(
                        suite_info['name'], 'FAIL', duration,
                        f"Failures: {test_results.get('failures', 0)}, Errors: {test_results.get('errors', 0)}"
                    )
            except Exception as e:
                duration = time.time() - start_time
                self.result.add_test_result(
                    suite_info['name'], 'ERROR', duration, str(e)
                )

        return True
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Comprehensive Trading System Tests")
        print("=" * 80)
        print(f"Test execution started at: {datetime.now()}")
        print("=" * 80)
        
        self.result.start_time = datetime.now()
        
        all_successful = True
        
        for suite_info in self.test_suites:
            try:
                success = self.run_test_suite(suite_info)
                if not success:
                    all_successful = False
            except Exception as e:
                print(f"❌ Error running test suite {suite_info['name']}: {e}")
                traceback.print_exc()
                all_successful = False
                self.result.add_test_result(
                    suite_info['name'], 'ERROR', 0, str(e)
                )
        
        self.result.end_time = datetime.now()
        
        # Generate final report
        self.generate_report(all_successful)
        
        return all_successful
    
    def generate_report(self, all_successful):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("="*80)
        
        summary = self.result.get_summary()
        
        # Print summary
        print(f"Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"🔥 Errors: {summary['errors']}")
        print(f"⏭️  Skipped: {summary['skipped']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Total Duration: {summary['total_duration']:.2f} seconds")
        
        # Overall status
        if all_successful and summary['failed'] == 0 and summary['errors'] == 0:
            print("\n🎉 ALL TESTS PASSED - SYSTEM READY FOR PRODUCTION!")
            status = "READY_FOR_PRODUCTION"
        elif summary['success_rate'] >= 90:
            print("\n⚠️  MOSTLY SUCCESSFUL - REVIEW FAILURES BEFORE PRODUCTION")
            status = "REVIEW_REQUIRED"
        else:
            print("\n🚨 SIGNIFICANT ISSUES FOUND - DO NOT DEPLOY TO PRODUCTION")
            status = "NOT_READY"
        
        # Print failures and errors
        if self.result.failures:
            print("\n❌ FAILURES:")
            for failure in self.result.failures:
                print(f"  - {failure}")
        
        if self.result.errors:
            print("\n🔥 ERRORS:")
            for error in self.result.errors:
                print(f"  - {error}")
        
        # Save detailed report to file
        report_data = {
            'summary': summary,
            'status': status,
            'test_details': self.result.test_details,
            'failures': self.result.failures,
            'errors': self.result.errors,
            'recommendations': self.get_recommendations(summary, status)
        }
        
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return status
    
    def get_recommendations(self, summary, status):
        """Get recommendations based on test results"""
        recommendations = []
        
        if status == "READY_FOR_PRODUCTION":
            recommendations.extend([
                "✅ All tests passed successfully",
                "✅ System is ready for production deployment",
                "✅ Continue with live trading on real accounts",
                "📝 Monitor system closely during initial live trading",
                "📝 Keep logging at INFO level for first week"
            ])
        elif status == "REVIEW_REQUIRED":
            recommendations.extend([
                "⚠️ Review and fix failed tests before production",
                "⚠️ Consider additional testing on demo accounts",
                "📝 Increase logging level to DEBUG for problematic components",
                "📝 Implement additional monitoring for identified issues"
            ])
        else:
            recommendations.extend([
                "🚨 DO NOT deploy to production with current issues",
                "🚨 Fix all critical errors before proceeding",
                "🚨 Re-run full test suite after fixes",
                "📝 Consider code review for failing components",
                "📝 Test on demo accounts extensively before retry"
            ])
        
        if summary['success_rate'] < 100:
            recommendations.append(f"📊 Current success rate: {summary['success_rate']:.1f}% - Target: 100%")
        
        return recommendations

def main():
    """Main test execution function"""
    print("🔧 Pre-Production Trading System Validation")
    print("🎯 Testing system for real account deployment")
    print()
    
    # Set up environment
    os.environ.setdefault('LOG_LEVEL', 'INFO')
    
    # Run tests
    runner = ComprehensiveTestRunner()
    success = runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
