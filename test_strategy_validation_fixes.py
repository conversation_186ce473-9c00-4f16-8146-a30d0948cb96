#!/usr/bin/env python3
"""
Test all strategy validation fixes to ensure logical and flexible validation
"""

import sys
from pathlib import Path

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def test_mean_reversion_validation():
    """Test mean reversion validation with various scenarios"""
    print("🔍 Testing Mean Reversion Validation...")
    
    try:
        from strategies.mean_reversion import MeanReversionStrategy
        from strategies.base_strategy import TradingSignal, MarketData
        
        strategy = MeanReversionStrategy({})
        
        # Test market data
        market_data = MarketData(
            symbol='EURUSD',
            timeframe='M15',
            candles=[],
            current_price=1.1000,
            spread=2.0,
            volume=1000,
            volatility=0.0010,
            pip_size=0.0001,
            pip_value=1.0,
            min_volume=0.01,
            max_volume=100.0
        )
        
        test_cases = [
            {
                'name': 'Previously Rejected 3.0 Pips (Should Now Pass)',
                'signal': TradingSignal(
                    action="SELL",
                    entry_price=1.1000,
                    stop_loss=1.1015,  # 15 pips risk
                    take_profit=1.0970,  # 30 pips reward (exactly 3.0 pips per unit)
                    confidence=0.75,
                    reasoning="Test 3.0 pip reward",
                    risk_level="MEDIUM"
                ),
                'should_pass': True
            },
            {
                'name': 'Small 2.5 Pips Reward (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0990,  # 10 pips risk
                    take_profit=1.1025,  # 25 pips reward
                    confidence=0.60,
                    reasoning="Small reward test",
                    risk_level="LOW"
                ),
                'should_pass': True
            },
            {
                'name': 'Large 80 Pips Reward (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0980,  # 20 pips risk
                    take_profit=1.1080,  # 80 pips reward
                    confidence=0.70,
                    reasoning="Large reward test",
                    risk_level="MEDIUM"
                ),
                'should_pass': True
            },
            {
                'name': 'Low Confidence 40% (Should Fail)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0990,
                    take_profit=1.1020,
                    confidence=0.40,  # Below 50% threshold
                    reasoning="Low confidence test",
                    risk_level="HIGH"
                ),
                'should_pass': False
            }
        ]
        
        passed = 0
        for case in test_cases:
            result = strategy.validate_signal(case['signal'], market_data)
            expected = case['should_pass']
            
            if result == expected:
                print(f"   ✅ {case['name']}: {'PASSED' if result else 'REJECTED'} (as expected)")
                passed += 1
            else:
                print(f"   ❌ {case['name']}: {'PASSED' if result else 'REJECTED'} (expected {'PASS' if expected else 'REJECT'})")
        
        print(f"   📊 Mean Reversion: {passed}/{len(test_cases)} tests passed")
        return passed == len(test_cases)
        
    except Exception as e:
        print(f"   ❌ Mean reversion test failed: {e}")
        return False

def test_trend_following_validation():
    """Test trend following validation with various scenarios"""
    print("\n🔍 Testing Trend Following Validation...")
    
    try:
        from strategies.trend_following import TrendFollowingStrategy
        from strategies.base_strategy import TradingSignal, MarketData
        
        strategy = TrendFollowingStrategy({})
        
        market_data = MarketData(
            symbol='EURUSD', timeframe='H1', candles=[], current_price=1.1000,
            spread=2.5, volume=1200, volatility=0.0015,
            pip_size=0.0001, pip_value=1.0, min_volume=0.01, max_volume=100.0
        )
        
        test_cases = [
            {
                'name': 'Moderate Confidence 45% (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0980,  # 20 pips risk
                    take_profit=1.1040,  # 40 pips reward (2:1 RR)
                    confidence=0.45,
                    reasoning="Moderate confidence test",
                    risk_level="MEDIUM"
                ),
                'should_pass': True
            },
            {
                'name': 'Large 150 Pips Reward (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0950,  # 50 pips risk
                    take_profit=1.1150,  # 150 pips reward
                    confidence=0.65,
                    reasoning="Large reward test",
                    risk_level="LOW"
                ),
                'should_pass': True
            },
            {
                'name': 'No Take Profit (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0980,
                    take_profit=None,  # No TP - should be allowed
                    confidence=0.60,
                    reasoning="No TP test",
                    risk_level="MEDIUM"
                ),
                'should_pass': True
            }
        ]
        
        passed = 0
        for case in test_cases:
            result = strategy.validate_signal(case['signal'], market_data)
            expected = case['should_pass']
            
            if result == expected:
                print(f"   ✅ {case['name']}: {'PASSED' if result else 'REJECTED'} (as expected)")
                passed += 1
            else:
                print(f"   ❌ {case['name']}: {'PASSED' if result else 'REJECTED'} (expected {'PASS' if expected else 'REJECT'})")
        
        print(f"   📊 Trend Following: {passed}/{len(test_cases)} tests passed")
        return passed == len(test_cases)
        
    except Exception as e:
        print(f"   ❌ Trend following test failed: {e}")
        return False

def test_breakout_validation():
    """Test breakout validation with various scenarios"""
    print("\n🔍 Testing Breakout Validation...")
    
    try:
        from strategies.breakout import BreakoutStrategy
        from strategies.base_strategy import TradingSignal, MarketData
        
        strategy = BreakoutStrategy({'average_volume': 1000})
        
        market_data = MarketData(
            symbol='EURUSD', timeframe='H1', candles=[], current_price=1.1000,
            spread=3.0, volume=1300, volatility=0.0020,  # High volume for breakout
            pip_size=0.0001, pip_value=1.0, min_volume=0.01, max_volume=100.0
        )
        
        test_cases = [
            {
                'name': 'Good Breakout Signal (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0970,  # 30 pips risk
                    take_profit=1.1100,  # 100 pips reward
                    confidence=0.70,
                    reasoning="Good breakout test",
                    risk_level="MEDIUM"
                ),
                'should_pass': True
            },
            {
                'name': 'Large Breakout Move (Should Pass)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0950,  # 50 pips risk
                    take_profit=1.1250,  # 250 pips reward
                    confidence=0.75,
                    reasoning="Large breakout test",
                    risk_level="LOW"
                ),
                'should_pass': True
            },
            {
                'name': 'Low Volume (Should Fail)',
                'signal': TradingSignal(
                    action="BUY",
                    entry_price=1.1000,
                    stop_loss=1.0980,
                    take_profit=1.1050,
                    confidence=0.80,
                    reasoning="Low volume test",
                    risk_level="MEDIUM"
                ),
                'should_pass': False,
                'market_data': MarketData(
                    symbol='EURUSD', timeframe='H1', candles=[], current_price=1.1000,
                    spread=3.0, volume=800, volatility=0.0020,  # Low volume
                    pip_size=0.0001, pip_value=1.0, min_volume=0.01, max_volume=100.0
                )
            }
        ]
        
        passed = 0
        for case in test_cases:
            test_market_data = case.get('market_data', market_data)
            result = strategy.validate_signal(case['signal'], test_market_data)
            expected = case['should_pass']
            
            if result == expected:
                print(f"   ✅ {case['name']}: {'PASSED' if result else 'REJECTED'} (as expected)")
                passed += 1
            else:
                print(f"   ❌ {case['name']}: {'PASSED' if result else 'REJECTED'} (expected {'PASS' if expected else 'REJECT'})")
        
        print(f"   📊 Breakout: {passed}/{len(test_cases)} tests passed")
        return passed == len(test_cases)
        
    except Exception as e:
        print(f"   ❌ Breakout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all strategy validation tests"""
    print("🚀 STRATEGY VALIDATION FIXES VERIFICATION")
    print("=" * 50)
    
    tests = [
        test_mean_reversion_validation,
        test_trend_following_validation,
        test_breakout_validation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 OVERALL RESULTS: {passed}/{total} strategy tests passed")
    
    if passed == total:
        print("✅ ALL STRATEGY VALIDATION FIXES ARE WORKING!")
        print("\n🎯 Key Improvements Made:")
        print("   • Fixed the 3.0 pips rejection bug (now uses >= instead of >)")
        print("   • More flexible pip ranges for all strategies")
        print("   • Reduced confidence thresholds to realistic levels")
        print("   • Increased spread tolerance for different market conditions")
        print("   • Better risk-reward ratio validation")
        print("   • Optional take profit handling")
        print("\n💡 Strategy Validation Summary:")
        print("   • Mean Reversion: 2-100 pips reward, 50%+ confidence, 0.5:1+ RR")
        print("   • Trend Following: 3-200 pips reward, 40%+ confidence, 1:1+ RR")
        print("   • Breakout: 5-300 pips reward, 60%+ confidence, 1.5:1+ RR")
        return True
    else:
        print("❌ SOME STRATEGY VALIDATION ISSUES REMAIN")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
