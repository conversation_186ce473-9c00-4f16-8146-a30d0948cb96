#!/usr/bin/env python3
"""
Test Symbol Mapping System
Verifies that symbol mapping works correctly for different brokers
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_symbol_mapping():
    """Test symbol mapping functionality"""
    print("🔧 Testing Symbol Mapping System...")
    
    try:
        from utils.symbol_mapper import get_symbol_mapper
        
        mapper = get_symbol_mapper()
        
        # Test RoboForex-ECN (standard format)
        print("\n📋 Testing RoboForex-ECN mappings:")
        roboforex_server = "RoboForex-ECN"
        
        test_cases_roboforex = [
            ("EURUSD", "EURUSD"),
            ("GBPUSD", "GBPUSD"),
            ("XAUUSD", "XAUUSD")
        ]
        
        for standard, expected_broker in test_cases_roboforex:
            broker_symbol = mapper.get_broker_symbol(standard, roboforex_server)
            print(f"  {standard} -> {broker_symbol} (expected: {expected_broker})")
            if broker_symbol == expected_broker:
                print("  ✅ PASS")
            else:
                print("  ❌ FAIL")
                return False
        
        # Test CapitalxtendLLC-MU (with exclamation marks)
        print("\n📋 Testing CapitalxtendLLC-MU mappings:")
        capitalxtend_server = "CapitalxtendLLC-MU"
        
        test_cases_capitalxtend = [
            ("EURUSD", "EURUSD!"),
            ("GBPUSD", "GBPUSD!"),
            ("XAUUSD", "XAUUSD!")
        ]
        
        for standard, expected_broker in test_cases_capitalxtend:
            broker_symbol = mapper.get_broker_symbol(standard, capitalxtend_server)
            print(f"  {standard} -> {broker_symbol} (expected: {expected_broker})")
            if broker_symbol == expected_broker:
                print("  ✅ PASS")
            else:
                print("  ❌ FAIL")
                return False
        
        # Test reverse mapping
        print("\n📋 Testing reverse mappings:")
        reverse_test_cases = [
            ("EURUSD!", capitalxtend_server, "EURUSD"),
            ("EURUSD", roboforex_server, "EURUSD"),
            ("GBPUSD!", capitalxtend_server, "GBPUSD")
        ]
        
        for broker_symbol, server, expected_standard in reverse_test_cases:
            standard_symbol = mapper.get_standard_symbol(broker_symbol, server)
            print(f"  {broker_symbol} ({server}) -> {standard_symbol} (expected: {expected_standard})")
            if standard_symbol == expected_standard:
                print("  ✅ PASS")
            else:
                print("  ❌ FAIL")
                return False
        
        # Test normalization for grouping
        print("\n📋 Testing symbol normalization for grouping:")
        normalization_test_cases = [
            ("EURUSD!", capitalxtend_server, "EURUSD"),
            ("EURUSD", roboforex_server, "EURUSD"),
            ("GBPUSD!", capitalxtend_server, "GBPUSD"),
            ("GBPUSD", roboforex_server, "GBPUSD")
        ]
        
        for symbol, server, expected_normalized in normalization_test_cases:
            normalized = mapper.normalize_symbol_for_grouping(symbol, server)
            print(f"  {symbol} ({server}) -> {normalized} (expected: {expected_normalized})")
            if normalized == expected_normalized:
                print("  ✅ PASS")
            else:
                print("  ❌ FAIL")
                return False
        
        print("\n🎉 ALL SYMBOL MAPPING TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Symbol mapping test failed: {e}")
        return False

def test_account_grouping():
    """Test account grouping with symbol mapping"""
    print("\n🔧 Testing Account Grouping with Symbol Mapping...")
    
    try:
        from utils.symbol_mapper import get_symbol_mapper
        
        mapper = get_symbol_mapper()
        
        # Simulate accounts from your config
        test_accounts = [
            {
                'account_id': 'Mine',
                'server': 'RoboForex-ECN',
                'symbols': [{'symbol': 'EURUSD', 'timeframe': 'M15'}]
            },
            {
                'account_id': 'Customer 1',
                'server': 'CapitalxtendLLC-MU',
                'symbols': [{'symbol': 'EURUSD!', 'timeframe': 'M15'}]
            }
        ]
        
        symbol_groups = mapper.get_symbol_groups(test_accounts)
        
        print(f"Created {len(symbol_groups)} symbol groups:")
        for group_key, accounts in symbol_groups.items():
            print(f"  Group: {group_key}")
            for account in accounts:
                symbol_config = account['symbol_config']
                print(f"    Account: {account['account_id']}")
                print(f"      Original: {symbol_config['original_symbol']}")
                print(f"      Standard: {symbol_config['standard_symbol']}")
                print(f"      Broker: {symbol_config['broker_symbol']}")
                print(f"      Server: {account['server']}")
        
        # Verify that both accounts are grouped together under EURUSD_M15
        if 'EURUSD_M15' in symbol_groups:
            group = symbol_groups['EURUSD_M15']
            if len(group) == 2:
                print("  ✅ Both accounts correctly grouped under EURUSD_M15")
                
                # Verify each account has correct symbol mappings
                mine_account = next((acc for acc in group if acc['account_id'] == 'Mine'), None)
                customer_account = next((acc for acc in group if acc['account_id'] == 'Customer 1'), None)
                
                if mine_account and customer_account:
                    mine_config = mine_account['symbol_config']
                    customer_config = customer_account['symbol_config']
                    
                    # Mine account should use EURUSD (no exclamation)
                    if (mine_config['original_symbol'] == 'EURUSD' and 
                        mine_config['broker_symbol'] == 'EURUSD' and
                        mine_config['standard_symbol'] == 'EURUSD'):
                        print("  ✅ Mine account symbol mapping correct")
                    else:
                        print("  ❌ Mine account symbol mapping incorrect")
                        return False
                    
                    # Customer account should use EURUSD! (with exclamation)
                    if (customer_config['original_symbol'] == 'EURUSD!' and 
                        customer_config['broker_symbol'] == 'EURUSD!' and
                        customer_config['standard_symbol'] == 'EURUSD'):
                        print("  ✅ Customer account symbol mapping correct")
                    else:
                        print("  ❌ Customer account symbol mapping incorrect")
                        return False
                    
                    print("\n🎉 ACCOUNT GROUPING TEST PASSED!")
                    return True
                else:
                    print("  ❌ Could not find both accounts in group")
                    return False
            else:
                print(f"  ❌ Expected 2 accounts in group, found {len(group)}")
                return False
        else:
            print("  ❌ EURUSD_M15 group not found")
            return False
        
    except Exception as e:
        print(f"❌ Account grouping test failed: {e}")
        return False

def main():
    """Run all symbol mapping tests"""
    print("🚀 SYMBOL MAPPING SYSTEM TESTS")
    print("=" * 50)
    
    test1_passed = test_symbol_mapping()
    test2_passed = test_account_grouping()
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ Symbol mapping system is working correctly!")
        print("✅ Your accounts should now work with their respective symbol formats!")
        print("\n🎯 EXPECTED BEHAVIOR:")
        print("  • Mine account (RoboForex-ECN) will use: EURUSD")
        print("  • Customer accounts (CapitalxtendLLC-MU) will use: EURUSD!")
        print("  • Both will be grouped together for signal generation")
        print("  • Each will trade with their broker-specific symbol format")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        print("❌ Please review the errors above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
