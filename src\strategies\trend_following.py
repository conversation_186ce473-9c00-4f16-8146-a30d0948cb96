"""
Trend Following Trading Strategy
"""

from typing import Dict, Any, List
from strategies.base_strategy import BaseStrategy, StrategyType, MarketData, TradingSignal
from logging_system.logger import logger

class TrendFollowingStrategy(BaseStrategy):
    """
    Trend Following Strategy
    Identifies and follows market trends using multiple timeframe analysis
    """
    
    def get_strategy_type(self) -> StrategyType:
        return StrategyType.TREND_FOLLOWING
    
    def get_strategy_name(self) -> str:
        return "Advanced Trend Following"
    
    def get_ai_prompt(
        self, 
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: Dict[str, Any]
    ) -> str:
        """Get AI prompt for trend following strategy"""
        
        recent_trades = trade_history[-10:] if len(trade_history) > 10 else trade_history
        win_rate = self._calculate_win_rate(recent_trades)
        avg_hold_time = self._calculate_avg_hold_time(recent_trades)
        
        prompt = f"""
You are executing a TREND FOLLOWING trading strategy for {market_data.symbol} on {market_data.timeframe} timeframe.
*** YOU ARE AN EXPERIENCED PROFITABLE TRADER MANAGING A REAL ACCOUNT - BE CONFIDENT IN CLEAR SETUPS ***

CRITICAL SUCCESS FACTORS:
- IDENTIFY clear trend patterns and be CONFIDENT in your analysis (aim for 60%+ confidence)
- ACCEPT good setups with 30%+ confidence if risk-reward is excellent (1:2 or better)
- WAIT for clear directional moves - patience is key to profitability
- NEVER trade against the trend or in choppy markets
- ALWAYS use proper risk management with tight stops
- Focus on 1:2 or better risk-reward ratios (realistic for M15 timeframe)
- BE DECISIVE - if you see a clear pattern, take it with appropriate confidence

STRATEGY OVERVIEW:
- Strategy Type: Trend Following
- Magic Number: {self.magic_number}
- Focus: Identify and ride strong directional moves with HIGH CONFIDENCE
- Timeframe: {market_data.timeframe} (Short-term trend following)
- Symbol: {market_data.symbol}

MARKET DATA ANALYSIS:
- Current Price: {market_data.current_price}
- Spread: {market_data.spread} pips
- Volume: {market_data.volume}
- Volatility: {market_data.volatility:.5f}

RECENT PERFORMANCE:
- Win Rate: {win_rate:.1f}%
- Average Hold Time: {avg_hold_time:.1f} hours
- Total Recent Trades: {len(recent_trades)}

TREND FOLLOWING PRINCIPLES:
1. "The trend is your friend" - trade in direction of established trends
2. Use multiple timeframe confirmation (higher TF for trend, lower TF for entry)
3. Enter on pullbacks in trending markets
4. Let profits run, cut losses short
5. Avoid choppy, sideways markets

TECHNICAL ANALYSIS FOCUS:
- Moving averages (20, 50, 200 EMA) for trend direction
- Price action patterns (higher highs/lows for uptrend, lower highs/lows for downtrend)
- Support and resistance levels
- Volume confirmation
- Momentum indicators (RSI, MACD) for entry timing

HIGH-CONFIDENCE ENTRY CRITERIA (LOOK FOR THESE PATTERNS):
1. STRONG trend established (clear direction for 15+ candles on M15)
2. Price pullback to 20 EMA or 50 EMA (perfect entry point)
3. RSI showing momentum in trend direction (30-70 range, not overbought/oversold)
4. MACD histogram confirming trend direction
5. Risk-reward ratio MINIMUM 1:2 (realistic for M15 timeframe)
6. Clear support/resistance levels for tight stop placement
7. Recent candles showing trend continuation (higher highs for uptrend, lower lows for downtrend)

CONFIDENCE BOOSTERS (Increase confidence to 70-85%):
- Multiple timeframe alignment (H1 trend same as M15)
- Volume spike on trend continuation
- Break of recent resistance/support
- Momentum divergence resolved in trend direction

CONSERVATIVE EXIT CRITERIA:
1. Take profit at 75% of target when 1:2 RR achieved (secure profits)
2. Trail stop loss to breakeven when 1:1 RR achieved
3. Exit immediately on trend reversal signals
4. Exit on momentum divergence or volume decline
5. NEVER hold through major news events

RISK MANAGEMENT:
- Stop loss below/above recent swing point (consider spread)
- Position size based on volatility (ATR)
- Maximum 3 correlated positions
- Avoid trading during major news events

MARKET CONDITIONS TO AVOID:
- Choppy, sideways markets (low ADX)
- High impact news events
- Low volume periods
- Excessive spread conditions

Based on the 200 candles of market data provided, analyze:
1. Overall trend direction and strength
2. Current market structure (trending vs ranging)
3. Key support and resistance levels
4. Entry opportunities with specific prices
5. Stop loss and take profit levels
6. Position sizing recommendations
7. Market timing and session considerations

CONFIDENCE LEVEL GUIDELINES:
- 85-95%: Perfect setup with all criteria met + multiple confirmations
- 70-84%: Strong setup with most criteria met + good confirmations
- 60-69%: Good setup with basic criteria met (PREFERRED RANGE)
- 30-59%: Acceptable setup with excellent risk-reward (1:2+ ratio required)
- Below 30%: HOLD - setup not clear enough, wait for better opportunity

ACTION DEFINITIONS:
- BUY/SELL: Take position when confidence >= 30% AND risk-reward >= 1:2
- HOLD: Wait for better setup (market unclear, very low confidence, or poor risk-reward)
- CLOSE: Exit existing positions (not applicable for signal generation)

TRADING APPROACH:
- AIM for 60%+ confidence when possible
- ACCEPT 30-59% confidence if risk-reward is excellent (1:2 or better)
- BE DECISIVE when you see clear patterns - don't overthink obvious setups
- TRUST your analysis - if trend is clear, be confident in your assessment

Provide specific trading signals with:
- Action: BUY/SELL/HOLD with detailed reasoning
- Entry price and timing (if BUY/SELL)
- Stop loss level (mandatory for BUY/SELL)
- Take profit targets (realistic for M15 timeframe, ensure 1:2+ risk-reward)
- Confidence level (30-95% for trades, below 30% for HOLD)
- Risk assessment (LOW/MEDIUM/HIGH)

Remember: BE CONFIDENT in clear setups. If you see a trend, trust your analysis and provide appropriate confidence.
"""
        return prompt
    
    def validate_signal(self, signal: TradingSignal, market_data: MarketData) -> bool:
        """Validate trend following signal with realistic algorithmic trading criteria"""

        # Must have stop loss for risk management
        if not signal.stop_loss:
            logger.warning(f"Trend following signal rejected: missing stop_loss")
            return False

        # Take profit is optional - trend following can use trailing stops
        if not signal.take_profit:
            logger.info(f"Trend following signal: no take_profit specified, will use trailing stop or dynamic exit")

        # Confidence threshold - trend following should accept moderate confidence
        # Trends develop over time, initial signals may have moderate confidence
        min_confidence = 0.40  # Increased from 0.30 to be more selective but still reasonable
        if signal.confidence < min_confidence:
            logger.warning(f"Trend following signal rejected: confidence {signal.confidence:.2f} < {min_confidence}")
            return False

        # Spread conditions - be more flexible for different market conditions
        max_spread = self.config.get('max_spread', 3.5)  # Increased from 2.0 to 3.5
        if market_data.spread > max_spread:
            logger.warning(f"Trend following signal rejected: spread {market_data.spread:.1f} > {max_spread}")
            return False

        # Validate stop loss distance - allow reasonable range
        if signal.entry_price and signal.stop_loss and market_data.pip_size:
            risk_pips = abs(signal.entry_price - signal.stop_loss) / market_data.pip_size
            logger.info(f"Trend following risk: {risk_pips:.1f} pips")

            # Allow 2-100 pips risk for trend following
            min_risk = 2.0  # Minimum 2 pips
            max_risk = 100.0  # Maximum 100 pips

            if risk_pips < min_risk:
                logger.warning(f"Trend following signal rejected: risk {risk_pips:.1f} pips < {min_risk} pips (too tight)")
                return False
            elif risk_pips > max_risk:
                logger.warning(f"Trend following signal rejected: risk {risk_pips:.1f} pips > {max_risk} pips (too wide)")
                return False

        # Risk-reward ratio validation - only if take profit is specified
        if signal.entry_price and signal.stop_loss and signal.take_profit:
            risk = abs(signal.entry_price - signal.stop_loss)
            reward = abs(signal.take_profit - signal.entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            logger.info(f"Trend following risk-reward ratio: {risk_reward_ratio:.2f}")

            # Minimum 1:1 risk-reward ratio for trend following
            min_rr = 1.0
            if risk_reward_ratio < min_rr:
                logger.warning(f"Trend following signal rejected: risk-reward ratio {risk_reward_ratio:.2f} < {min_rr}")
                return False

        # Validate take profit distance if specified
        if signal.entry_price and signal.take_profit and market_data.pip_size:
            reward_pips = abs(signal.take_profit - signal.entry_price) / market_data.pip_size
            logger.info(f"Trend following reward: {reward_pips:.1f} pips")

            # Allow 3-200 pips reward for trend following
            min_reward = 3.0  # Minimum 3 pips
            max_reward = 200.0  # Maximum 200 pips

            if reward_pips < min_reward:
                logger.warning(f"Trend following signal rejected: reward {reward_pips:.1f} pips < {min_reward} pips (too small)")
                return False
            elif reward_pips > max_reward:
                logger.warning(f"Trend following signal rejected: reward {reward_pips:.1f} pips > {max_reward} pips (unrealistic)")
                return False

        logger.info(f"Trend following signal validation PASSED")
        return True
    
    def _calculate_win_rate(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate win rate from trade history"""
        if not trades:
            return 0.0
        
        winning_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
        return (winning_trades / len(trades)) * 100
    
    def _calculate_avg_hold_time(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate average hold time in hours"""
        if not trades:
            return 0.0
        
        total_time = sum(trade.get('hold_time_hours', 0) for trade in trades)
        return total_time / len(trades)
