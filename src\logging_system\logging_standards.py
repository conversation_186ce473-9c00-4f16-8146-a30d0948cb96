#!/usr/bin/env python3
"""
Logging Standards and Utilities
Provides standardized logging patterns and utilities for consistent logging across the system
"""

from typing import Dict, Any, Optional, List
from enum import Enum
from datetime import datetime

from logging_system.logger import get_logger, trading_logger

class LogLevel(Enum):
    """Standardized log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    """Standardized log categories"""
    SYSTEM = "SYSTEM"
    TRADE = "TRADE"
    AI = "AI"
    MARKET_DATA = "MARKET_DATA"
    RISK = "RISK"
    VALIDATION = "VALIDATION"
    PERFORMANCE = "PERFORMANCE"
    ERROR_RECOVERY = "ERROR_RECOVERY"
    SESSION = "SESSION"
    ACCOUNT = "ACCOUNT"

class StandardizedLogger:
    """Standardized logger with consistent formatting and categorization"""
    
    def __init__(self, module_name: str):
        self.module_name = module_name
        self.logger = get_logger(module_name)
    
    def _format_message(
        self,
        category: LogCategory,
        operation: str,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        account_id: Optional[str] = None,
        symbol: Optional[str] = None
    ) -> str:
        """Format message with standardized structure"""
        
        # Base format: [CATEGORY] OPERATION: message
        formatted = f"[{category.value}] {operation}: {message}"
        
        # Add context information
        context_parts = []
        
        if account_id:
            context_parts.append(f"Account={account_id}")
        
        if symbol:
            context_parts.append(f"Symbol={symbol}")
        
        if context:
            for key, value in context.items():
                if key not in ['account_id', 'symbol']:  # Avoid duplicates
                    context_parts.append(f"{key}={value}")
        
        if context_parts:
            formatted += f" | {' | '.join(context_parts)}"
        
        return formatted
    
    def log_system_operation(
        self,
        operation: str,
        message: str,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log system operations"""
        formatted_message = self._format_message(
            LogCategory.SYSTEM, operation, message, context
        )
        
        getattr(self.logger, level.value.lower())(formatted_message)
    
    def log_trade_operation(
        self,
        operation: str,
        message: str,
        account_id: str,
        symbol: Optional[str] = None,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log trade-related operations"""
        formatted_message = self._format_message(
            LogCategory.TRADE, operation, message, context, account_id, symbol
        )
        
        # Use trading logger for trade operations
        if level == LogLevel.ERROR:
            trading_logger.logger.bind(TRADE=True).error(formatted_message)
        elif level == LogLevel.WARNING:
            trading_logger.logger.bind(TRADE=True).warning(formatted_message)
        else:
            trading_logger.logger.bind(TRADE=True).info(formatted_message)
    
    def log_ai_operation(
        self,
        operation: str,
        message: str,
        account_id: str,
        symbol: Optional[str] = None,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log AI-related operations"""
        formatted_message = self._format_message(
            LogCategory.AI, operation, message, context, account_id, symbol
        )
        
        # Use trading logger for AI operations
        if level == LogLevel.ERROR:
            trading_logger.logger.bind(AI=True).error(formatted_message)
        elif level == LogLevel.WARNING:
            trading_logger.logger.bind(AI=True).warning(formatted_message)
        else:
            trading_logger.logger.bind(AI=True).info(formatted_message)
    
    def log_market_data_operation(
        self,
        operation: str,
        message: str,
        symbol: str,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log market data operations"""
        formatted_message = self._format_message(
            LogCategory.MARKET_DATA, operation, message, context, symbol=symbol
        )
        
        getattr(self.logger, level.value.lower())(formatted_message)
    
    def log_risk_operation(
        self,
        operation: str,
        message: str,
        account_id: str,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log risk management operations"""
        formatted_message = self._format_message(
            LogCategory.RISK, operation, message, context, account_id
        )
        
        getattr(self.logger, level.value.lower())(formatted_message)
    
    def log_validation_operation(
        self,
        operation: str,
        message: str,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None,
        account_id: Optional[str] = None,
        symbol: Optional[str] = None
    ):
        """Log validation operations"""
        formatted_message = self._format_message(
            LogCategory.VALIDATION, operation, message, context, account_id, symbol
        )
        
        getattr(self.logger, level.value.lower())(formatted_message)
    
    def log_performance_metric(
        self,
        metric_name: str,
        value: Any,
        unit: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        account_id: Optional[str] = None
    ):
        """Log performance metrics"""
        message = f"{metric_name}={value}"
        if unit:
            message += f" {unit}"
        
        formatted_message = self._format_message(
            LogCategory.PERFORMANCE, "METRIC", message, context, account_id
        )
        
        self.logger.info(formatted_message)
    
    def log_error_recovery(
        self,
        operation: str,
        error: str,
        recovery_action: str,
        success: bool,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log error recovery operations"""
        status = "SUCCESS" if success else "FAILED"
        message = f"Error: {error} | Recovery: {recovery_action} | Status: {status}"
        
        formatted_message = self._format_message(
            LogCategory.ERROR_RECOVERY, operation, message, context
        )
        
        if success:
            self.logger.info(formatted_message)
        else:
            self.logger.error(formatted_message)
    
    def log_session_operation(
        self,
        operation: str,
        message: str,
        account_id: str,
        level: LogLevel = LogLevel.INFO,
        context: Optional[Dict[str, Any]] = None
    ):
        """Log session management operations"""
        formatted_message = self._format_message(
            LogCategory.SESSION, operation, message, context, account_id
        )
        
        getattr(self.logger, level.value.lower())(formatted_message)

class LoggingMixin:
    """Mixin class to add standardized logging to any class"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = StandardizedLogger(self.__class__.__name__)
    
    @property
    def logger(self) -> StandardizedLogger:
        """Get the standardized logger"""
        return self._logger

def create_logger(module_name: str) -> StandardizedLogger:
    """Create a standardized logger for a module"""
    return StandardizedLogger(module_name)

# Common logging patterns
def log_function_entry(logger: StandardizedLogger, function_name: str, **kwargs):
    """Log function entry with parameters"""
    context = {k: v for k, v in kwargs.items() if v is not None}
    logger.log_system_operation(
        "FUNCTION_ENTRY",
        f"Entering {function_name}",
        LogLevel.DEBUG,
        context
    )

def log_function_exit(logger: StandardizedLogger, function_name: str, result: Any = None, duration: float = None):
    """Log function exit with result and duration"""
    context = {}
    if result is not None:
        context['result'] = str(result)[:100]  # Truncate long results
    if duration is not None:
        context['duration_ms'] = f"{duration * 1000:.2f}"
    
    logger.log_system_operation(
        "FUNCTION_EXIT",
        f"Exiting {function_name}",
        LogLevel.DEBUG,
        context
    )

def log_exception(logger: StandardizedLogger, operation: str, exception: Exception, context: Optional[Dict[str, Any]] = None):
    """Log exceptions with standardized format"""
    error_context = context or {}
    error_context.update({
        'exception_type': type(exception).__name__,
        'exception_message': str(exception)
    })
    
    logger.log_system_operation(
        operation,
        f"Exception occurred: {exception}",
        LogLevel.ERROR,
        error_context
    )

# Decorators for automatic logging
def log_function_calls(logger: StandardizedLogger):
    """Decorator to automatically log function entry and exit"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            
            log_function_entry(logger, func.__name__, **kwargs)
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                log_function_exit(logger, func.__name__, result, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                log_exception(logger, f"FUNCTION_ERROR_{func.__name__}", e, {'duration_ms': f"{duration * 1000:.2f}"})
                raise
        
        return wrapper
    return decorator

# Performance logging utilities
class PerformanceTimer:
    """Context manager for timing operations"""
    
    def __init__(self, logger: StandardizedLogger, operation: str, context: Optional[Dict[str, Any]] = None):
        self.logger = logger
        self.operation = operation
        self.context = context or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            self.logger.log_performance_metric(
                f"{self.operation}_duration",
                f"{duration:.3f}",
                "seconds",
                self.context
            )

# Global standardized loggers for common modules
system_logger = create_logger("System")
trade_logger = create_logger("Trade")
ai_logger = create_logger("AI")
market_data_logger = create_logger("MarketData")
risk_logger = create_logger("Risk")
session_logger = create_logger("Session")

class LoggingValidator:
    """Validates logging consistency across the system"""

    def __init__(self):
        self.issues = []
        self.recommendations = []

    def validate_log_message_format(self, message: str, expected_category: LogCategory) -> bool:
        """Validate that log message follows standardized format"""
        # Expected format: [CATEGORY] OPERATION: message | context
        if not message.startswith(f"[{expected_category.value}]"):
            self.issues.append(f"Message doesn't start with expected category [{expected_category.value}]: {message[:50]}...")
            return False

        if "] " not in message or ": " not in message:
            self.issues.append(f"Message doesn't follow [CATEGORY] OPERATION: format: {message[:50]}...")
            return False

        return True

    def validate_context_information(self, context: Dict[str, Any]) -> bool:
        """Validate context information completeness"""
        required_fields = ['timestamp', 'module']
        missing_fields = [field for field in required_fields if field not in context]

        if missing_fields:
            self.issues.append(f"Missing required context fields: {missing_fields}")
            return False

        return True

    def check_log_level_consistency(self, operation_type: str, level: LogLevel) -> bool:
        """Check if log level is appropriate for operation type"""
        # Define expected log levels for different operation types
        level_guidelines = {
            'ERROR': [LogLevel.ERROR, LogLevel.CRITICAL],
            'WARNING': [LogLevel.WARNING, LogLevel.ERROR],
            'SUCCESS': [LogLevel.INFO, LogLevel.DEBUG],
            'DEBUG': [LogLevel.DEBUG],
            'PERFORMANCE': [LogLevel.INFO, LogLevel.DEBUG]
        }

        for op_pattern, expected_levels in level_guidelines.items():
            if op_pattern.lower() in operation_type.lower():
                if level not in expected_levels:
                    self.issues.append(f"Inappropriate log level {level.value} for operation {operation_type}")
                    return False

        return True

    def generate_logging_report(self) -> Dict[str, Any]:
        """Generate a comprehensive logging validation report"""
        return {
            'total_issues': len(self.issues),
            'issues': self.issues,
            'recommendations': self.recommendations,
            'validation_passed': len(self.issues) == 0
        }

    def add_recommendation(self, recommendation: str):
        """Add a logging improvement recommendation"""
        self.recommendations.append(recommendation)

# Global logging validator
logging_validator = LoggingValidator()

def validate_logging_setup():
    """Validate the overall logging setup"""
    validator = LoggingValidator()

    # Check if all required loggers are properly configured
    required_loggers = [
        "System", "Trade", "AI", "MarketData", "Risk", "Session",
        "SignalGenerator", "TradeManager", "ErrorRecovery"
    ]

    for logger_name in required_loggers:
        try:
            test_logger = create_logger(logger_name)
            test_logger.log_system_operation("VALIDATION_TEST", "Testing logger configuration", LogLevel.DEBUG)
        except Exception as e:
            validator.issues.append(f"Failed to create logger {logger_name}: {e}")

    # Add recommendations for logging best practices
    validator.add_recommendation("Use standardized log categories for consistent filtering")
    validator.add_recommendation("Include relevant context information in all log messages")
    validator.add_recommendation("Use appropriate log levels based on operation severity")
    validator.add_recommendation("Implement performance logging for critical operations")
    validator.add_recommendation("Use structured logging for better analysis and monitoring")

    return validator.generate_logging_report()
