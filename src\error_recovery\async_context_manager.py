#!/usr/bin/env python3
"""
Enhanced Async Context Manager
Provides robust async context management with automatic cleanup and error recovery
"""

import asyncio
import weakref
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional, Callable, AsyncGenerator
from datetime import datetime

from logging_system.logger import get_logger
from error_recovery.recovery_manager import error_recovery_manager, FailureType

logger = get_logger(__name__)

class ResourceTracker:
    """Tracks and manages async resources to prevent leaks"""
    
    def __init__(self):
        self.active_resources: Dict[str, Any] = {}
        self.cleanup_callbacks: Dict[str, Callable] = {}
        self.resource_timestamps: Dict[str, datetime] = {}
    
    def register_resource(self, resource_id: str, resource: Any, cleanup_callback: Optional[Callable] = None):
        """Register a resource for tracking"""
        self.active_resources[resource_id] = resource
        self.resource_timestamps[resource_id] = datetime.now()
        if cleanup_callback:
            self.cleanup_callbacks[resource_id] = cleanup_callback
        logger.debug(f"📝 Registered resource: {resource_id}")
    
    def unregister_resource(self, resource_id: str):
        """Unregister a resource"""
        if resource_id in self.active_resources:
            del self.active_resources[resource_id]
        if resource_id in self.cleanup_callbacks:
            del self.cleanup_callbacks[resource_id]
        if resource_id in self.resource_timestamps:
            del self.resource_timestamps[resource_id]
        logger.debug(f"🗑️ Unregistered resource: {resource_id}")
    
    async def cleanup_resource(self, resource_id: str):
        """Clean up a specific resource"""
        try:
            if resource_id in self.cleanup_callbacks:
                cleanup_func = self.cleanup_callbacks[resource_id]
                if asyncio.iscoroutinefunction(cleanup_func):
                    await cleanup_func()
                else:
                    cleanup_func()
                logger.debug(f"🧹 Cleaned up resource: {resource_id}")
        except Exception as e:
            logger.error(f"❌ Error cleaning up resource {resource_id}: {e}")
        finally:
            self.unregister_resource(resource_id)
    
    async def cleanup_all_resources(self):
        """Clean up all tracked resources"""
        logger.info(f"🧹 Cleaning up {len(self.active_resources)} tracked resources")
        
        cleanup_tasks = []
        for resource_id in list(self.active_resources.keys()):
            cleanup_tasks.append(self.cleanup_resource(resource_id))
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """Get statistics about tracked resources"""
        now = datetime.now()
        stats = {
            'total_resources': len(self.active_resources),
            'resource_ages': {},
            'oldest_resource': None,
            'newest_resource': None
        }
        
        if self.resource_timestamps:
            for resource_id, timestamp in self.resource_timestamps.items():
                age = (now - timestamp).total_seconds()
                stats['resource_ages'][resource_id] = age
            
            oldest_id = min(self.resource_timestamps, key=self.resource_timestamps.get)
            newest_id = max(self.resource_timestamps, key=self.resource_timestamps.get)
            
            stats['oldest_resource'] = {
                'id': oldest_id,
                'age': stats['resource_ages'][oldest_id]
            }
            stats['newest_resource'] = {
                'id': newest_id,
                'age': stats['resource_ages'][newest_id]
            }
        
        return stats

# Global resource tracker
resource_tracker = ResourceTracker()

@asynccontextmanager
async def managed_resource(
    resource_id: str,
    acquire_func: Callable,
    cleanup_func: Optional[Callable] = None,
    failure_type: FailureType = FailureType.UNKNOWN,
    context: Dict[str, Any] = None
) -> AsyncGenerator[Any, None]:
    """
    Enhanced async context manager with automatic resource tracking and error recovery
    
    Args:
        resource_id: Unique identifier for the resource
        acquire_func: Function to acquire the resource
        cleanup_func: Function to clean up the resource
        failure_type: Type of failure for recovery strategy
        context: Additional context for error recovery
    """
    resource = None
    context = context or {}
    
    try:
        # Acquire resource with error recovery
        logger.debug(f"🔄 Acquiring resource: {resource_id}")
        resource = await error_recovery_manager.execute_with_recovery(
            acquire_func,
            f"acquire_{resource_id}",
            failure_type,
            context
        )
        
        # Register resource for tracking
        resource_tracker.register_resource(resource_id, resource, cleanup_func)
        
        logger.debug(f"✅ Resource acquired: {resource_id}")
        yield resource
        
    except Exception as e:
        logger.error(f"❌ Error with resource {resource_id}: {e}")
        raise
    finally:
        # Clean up resource
        if resource is not None:
            try:
                if cleanup_func:
                    logger.debug(f"🧹 Cleaning up resource: {resource_id}")
                    if asyncio.iscoroutinefunction(cleanup_func):
                        await cleanup_func(resource)
                    else:
                        cleanup_func(resource)
                    logger.debug(f"✅ Resource cleaned up: {resource_id}")
            except Exception as cleanup_error:
                logger.error(f"❌ Error cleaning up resource {resource_id}: {cleanup_error}")
            finally:
                resource_tracker.unregister_resource(resource_id)

@asynccontextmanager
async def managed_mt5_session(account, mt5_client) -> AsyncGenerator[Any, None]:
    """
    Specialized context manager for MT5 sessions with enhanced error recovery
    """
    session_id = f"mt5_session_{account.account_id}_{account.account_number}"
    
    async def acquire_session():
        """Acquire MT5 session"""
        from mt5_integration.session_manager import session_manager
        
        # Ensure session is established
        success = await session_manager.ensure_account_session(account, mt5_client)
        if not success:
            raise Exception(f"Failed to establish MT5 session for account {account.account_number}")
        
        return mt5_client
    
    async def cleanup_session(client):
        """Clean up MT5 session"""
        try:
            # Session cleanup is handled by session manager
            logger.debug(f"🔓 MT5 session cleanup for account {account.account_number}")
        except Exception as e:
            logger.error(f"❌ Error cleaning up MT5 session: {e}")
    
    async with managed_resource(
        session_id,
        acquire_session,
        cleanup_session,
        FailureType.MT5_CONNECTION,
        {'account_id': account.account_id, 'account_number': account.account_number}
    ) as session:
        yield session

@asynccontextmanager
async def managed_ai_client() -> AsyncGenerator[Any, None]:
    """
    Specialized context manager for AI client with enhanced error recovery
    """
    client_id = f"ai_client_{datetime.now().timestamp()}"
    
    async def acquire_ai_client():
        """Acquire AI client"""
        from ai_integration.ai_provider_factory import get_ai_provider_with_fallback

        client = await get_ai_provider_with_fallback()
        await client.__aenter__()
        return client
    
    async def cleanup_ai_client(client):
        """Clean up AI client"""
        try:
            await client.__aexit__(None, None, None)
        except Exception as e:
            logger.error(f"❌ Error cleaning up AI client: {e}")
    
    async with managed_resource(
        client_id,
        acquire_ai_client,
        cleanup_ai_client,
        FailureType.AI_REQUEST,
        {'client_type': 'qwen'}
    ) as client:
        yield client

class AsyncOperationManager:
    """Manages async operations with proper cleanup and error handling"""
    
    def __init__(self):
        self.active_operations: Dict[str, asyncio.Task] = {}
        self.operation_results: Dict[str, Any] = {}
    
    async def execute_operation(
        self,
        operation_id: str,
        operation_func: Callable,
        timeout: Optional[float] = None,
        *args,
        **kwargs
    ) -> Any:
        """Execute an async operation with proper management"""
        
        if operation_id in self.active_operations:
            logger.warning(f"⚠️ Operation {operation_id} already running")
            return await self.active_operations[operation_id]
        
        try:
            # Create and track the task
            if asyncio.iscoroutinefunction(operation_func):
                task = asyncio.create_task(operation_func(*args, **kwargs))
            else:
                task = asyncio.create_task(asyncio.to_thread(operation_func, *args, **kwargs))
            
            self.active_operations[operation_id] = task
            
            # Execute with timeout if specified
            if timeout:
                result = await asyncio.wait_for(task, timeout=timeout)
            else:
                result = await task
            
            self.operation_results[operation_id] = result
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"⏰ Operation {operation_id} timed out after {timeout} seconds")
            raise
        except Exception as e:
            logger.error(f"❌ Operation {operation_id} failed: {e}")
            raise
        finally:
            # Clean up
            if operation_id in self.active_operations:
                task = self.active_operations[operation_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                del self.active_operations[operation_id]
    
    async def cancel_operation(self, operation_id: str):
        """Cancel a running operation"""
        if operation_id in self.active_operations:
            task = self.active_operations[operation_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"🚫 Operation {operation_id} cancelled")
            finally:
                del self.active_operations[operation_id]
    
    async def cancel_all_operations(self):
        """Cancel all running operations"""
        logger.info(f"🚫 Cancelling {len(self.active_operations)} active operations")
        
        cancel_tasks = []
        for operation_id in list(self.active_operations.keys()):
            cancel_tasks.append(self.cancel_operation(operation_id))
        
        if cancel_tasks:
            await asyncio.gather(*cancel_tasks, return_exceptions=True)
    
    def get_operation_status(self) -> Dict[str, Any]:
        """Get status of all operations"""
        return {
            'active_operations': len(self.active_operations),
            'completed_operations': len(self.operation_results),
            'operation_ids': list(self.active_operations.keys())
        }

# Global operation manager
async_operation_manager = AsyncOperationManager()

# Cleanup function for graceful shutdown
async def cleanup_all_async_resources():
    """Clean up all async resources during shutdown"""
    logger.info("🧹 Starting comprehensive async resource cleanup")
    
    try:
        # Cancel all operations
        await async_operation_manager.cancel_all_operations()
        
        # Clean up all tracked resources
        await resource_tracker.cleanup_all_resources()
        
        logger.info("✅ Async resource cleanup completed")
    except Exception as e:
        logger.error(f"❌ Error during async resource cleanup: {e}")
