{"summary": {"total_tests": 27, "passed": 18, "failed": 9, "errors": 0, "skipped": 0, "success_rate": 66.**************, "total_duration": 22.268038, "start_time": "2025-08-07T00:39:27.150480", "end_time": "2025-08-07T00:39:49.418518"}, "status": "NOT_READY", "test_details": [{"test_name": "Account Management Tests_test_0", "status": "PASS", "duration": 0.****************, "error_msg": null, "timestamp": "2025-08-07T00:39:28.706271"}, {"test_name": "Account Management Tests_test_1", "status": "PASS", "duration": 0.****************, "error_msg": null, "timestamp": "2025-08-07T00:39:28.706271"}, {"test_name": "Account Management Tests_test_2", "status": "PASS", "duration": 0.****************, "error_msg": null, "timestamp": "2025-08-07T00:39:28.706271"}, {"test_name": "Account Management Tests_test_3", "status": "PASS", "duration": 0.****************, "error_msg": null, "timestamp": "2025-08-07T00:39:28.706271"}, {"test_name": "test_percent_risk_strategy_comprehensive (test_comprehensive_trading_system.TestMoneyManagementStrategies.test_percent_risk_strategy_comprehensive)", "status": "FAIL", "duration": 0.*****************, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_comprehensive_trading_system.py\", line 213, in test_percent_risk_strategy_comprehensive\n    self.assertEqual(trade_params.risk_amount, 200.0)  # 2% of 10000\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: 5.**************** != 200.0\n", "timestamp": "2025-08-07T00:39:29.278403"}, {"test_name": "Money Management Tests_test_0", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-07T00:39:29.278403"}, {"test_name": "Money Management Tests_test_1", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-07T00:39:29.278403"}, {"test_name": "Money Management Tests_test_2", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-07T00:39:29.278403"}, {"test_name": "Money Management Tests_test_3", "status": "PASS", "duration": 0.*****************, "error_msg": null, "timestamp": "2025-08-07T00:39:29.278403"}, {"test_name": "Trading Strategy Tests_test_0", "status": "PASS", "duration": 0.025257428487141926, "error_msg": null, "timestamp": "2025-08-07T00:39:29.385382"}, {"test_name": "Trading Strategy Tests_test_1", "status": "PASS", "duration": 0.025257428487141926, "error_msg": null, "timestamp": "2025-08-07T00:39:29.385382"}, {"test_name": "Trading Strategy Tests_test_2", "status": "PASS", "duration": 0.025257428487141926, "error_msg": null, "timestamp": "2025-08-07T00:39:29.385382"}, {"test_name": "AI Integration Tests_test_0", "status": "PASS", "duration": 0.021412134170532227, "error_msg": null, "timestamp": "2025-08-07T00:39:29.522834"}, {"test_name": "AI Integration Tests_test_1", "status": "PASS", "duration": 0.021412134170532227, "error_msg": null, "timestamp": "2025-08-07T00:39:29.522834"}, {"test_name": "AI Integration Tests_test_2", "status": "PASS", "duration": 0.021412134170532227, "error_msg": null, "timestamp": "2025-08-07T00:39:29.522834"}, {"test_name": "AI Integration Tests_test_3", "status": "PASS", "duration": 0.021412134170532227, "error_msg": null, "timestamp": "2025-08-07T00:39:29.522834"}, {"test_name": "AI Integration Tests_test_4", "status": "PASS", "duration": 0.021412134170532227, "error_msg": null, "timestamp": "2025-08-07T00:39:29.522834"}, {"test_name": "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration.test_market_data_retrieval)", "status": "FAIL", "duration": 0.11749513943990071, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1393, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_comprehensive_trading_system.py\", line 589, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "timestamp": "2025-08-07T00:39:29.917187"}, {"test_name": "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration.test_mt5_login_comprehensive)", "status": "FAIL", "duration": 0.11749513943990071, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1393, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_comprehensive_trading_system.py\", line 566, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "timestamp": "2025-08-07T00:39:29.917187"}, {"test_name": "MT5 Integration Tests_test_0", "status": "PASS", "duration": 0.11749513943990071, "error_msg": null, "timestamp": "2025-08-07T00:39:29.917187"}, {"test_name": "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution.test_closed_trade_retrieval)", "status": "FAIL", "duration": 2.7738678455352783, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 302, in test_closed_trade_retrieval\n    self.assertEqual(len(trade_history), 3)\nAssertionError: 0 != 3\n", "timestamp": "2025-08-07T00:39:49.390891"}, {"test_name": "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution.test_complete_signal_lifecycle)", "status": "FAIL", "duration": 2.7738678455352783, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1862, in _inner\n    return await f(*args, **kw)\n           ^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 146, in test_complete_signal_lifecycle\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 916, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "timestamp": "2025-08-07T00:39:49.390891"}, {"test_name": "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution.test_magic_number_tracking)", "status": "FAIL", "duration": 2.7738678455352783, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 117, in _callMaybeAsync\n    return self._asyncioTestContext.run(func, *args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 405, in test_magic_number_tracking\n    self.assertEqual(len(magic_numbers), len(strategy_types))\nAssertionError: 1 != 3\n", "timestamp": "2025-08-07T00:39:49.390891"}, {"test_name": "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution.test_multiple_take_profit_execution)", "status": "FAIL", "duration": 2.7738678455352783, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 199, in test_multiple_take_profit_execution\n    self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)\nAssertionError: 0 not greater than or equal to 3\n", "timestamp": "2025-08-07T00:39:49.390891"}, {"test_name": "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution.test_pending_order_management)", "status": "FAIL", "duration": 2.7738678455352783, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 241, in test_pending_order_management\n    self.assertEqual(len(pending_orders), 2)\nAssertionError: 0 != 2\n", "timestamp": "2025-08-07T00:39:49.390891"}, {"test_name": "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution.test_trade_lifecycle_with_ai_management)", "status": "FAIL", "duration": 2.7738678455352783, "error_msg": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 389, in test_trade_lifecycle_with_ai_management\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 916, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "timestamp": "2025-08-07T00:39:49.390891"}, {"test_name": "Real-World Signal Execution Tests_test_0", "status": "PASS", "duration": 2.7738678455352783, "error_msg": null, "timestamp": "2025-08-07T00:39:49.390891"}], "failures": ["test_percent_risk_strategy_comprehensive (test_comprehensive_trading_system.TestMoneyManagementStrategies.test_percent_risk_strategy_comprehensive): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_comprehensive_trading_system.py\", line 213, in test_percent_risk_strategy_comprehensive\n    self.assertEqual(trade_params.risk_amount, 200.0)  # 2% of 10000\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: 5.**************** != 200.0\n", "test_market_data_retrieval (test_comprehensive_trading_system.TestMT5Integration.test_market_data_retrieval): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1393, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_comprehensive_trading_system.py\", line 589, in test_market_data_retrieval\n    self.assertIsNotNone(market_data)\nAssertionError: unexpectedly None\n", "test_mt5_login_comprehensive (test_comprehensive_trading_system.TestMT5Integration.test_mt5_login_comprehensive): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1393, in patched\n    return func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_comprehensive_trading_system.py\", line 566, in test_mt5_login_comprehensive\n    self.assertTrue(result)\nAssertionError: False is not true\n", "test_closed_trade_retrieval (test_real_world_integration.TestRealWorldSignalExecution.test_closed_trade_retrieval): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 302, in test_closed_trade_retrieval\n    self.assertEqual(len(trade_history), 3)\nAssertionError: 0 != 3\n", "test_complete_signal_lifecycle (test_real_world_integration.TestRealWorldSignalExecution.test_complete_signal_lifecycle): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1862, in _inner\n    return await f(*args, **kw)\n           ^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 146, in test_complete_signal_lifecycle\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 916, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n", "test_magic_number_tracking (test_real_world_integration.TestRealWorldSignalExecution.test_magic_number_tracking): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 117, in _callMaybeAsync\n    return self._asyncioTestContext.run(func, *args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 405, in test_magic_number_tracking\n    self.assertEqual(len(magic_numbers), len(strategy_types))\nAssertionError: 1 != 3\n", "test_multiple_take_profit_execution (test_real_world_integration.TestRealWorldSignalExecution.test_multiple_take_profit_execution): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 199, in test_multiple_take_profit_execution\n    self.assertGreaterEqual(mock_mt5.order_send.call_count, 3)\nAssertionError: 0 not greater than or equal to 3\n", "test_pending_order_management (test_real_world_integration.TestRealWorldSignalExecution.test_pending_order_management): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 1410, in patched\n    return await func(*newargs, **newkeywargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 241, in test_pending_order_management\n    self.assertEqual(len(pending_orders), 2)\nAssertionError: 0 != 2\n", "test_trade_lifecycle_with_ai_management (test_real_world_integration.TestRealWorldSignalExecution.test_trade_lifecycle_with_ai_management): Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 90, in _callTestMethod\n    if self._callMaybeAsync(method) is not None:\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py\", line 112, in _callMaybeAsync\n    return self._asyncioRunner.run(\n           ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\n    return self._loop.run_until_complete(task)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\fulltrade\\tests\\test_real_world_integration.py\", line 389, in test_trade_lifecycle_with_ai_management\n    mock_mt5.order_send.assert_called()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py\", line 916, in assert_called\n    raise AssertionError(msg)\nAssertionError: Expected 'order_send' to have been called.\n"], "errors": [], "recommendations": ["🚨 DO NOT deploy to production with current issues", "🚨 Fix all critical errors before proceeding", "🚨 Re-run full test suite after fixes", "📝 Consider code review for failing components", "📝 Test on demo accounts extensively before retry", "📊 Current success rate: 66.7% - Target: 100%"]}