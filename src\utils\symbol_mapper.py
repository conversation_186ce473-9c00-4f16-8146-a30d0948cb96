#!/usr/bin/env python3
"""
Symbol Mapper - Handles different broker symbol naming conventions
"""

from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from loguru import logger


@dataclass
class BrokerSymbolMapping:
    """Mapping for broker-specific symbol formats"""
    broker_name: str
    server: str
    symbol_mappings: Dict[str, str]  # standard_symbol -> broker_symbol
    reverse_mappings: Dict[str, str]  # broker_symbol -> standard_symbol


class SymbolMapper:
    """Handles symbol mapping between different broker formats"""
    
    def __init__(self):
        self.broker_mappings: Dict[str, BrokerSymbolMapping] = {}
        self._initialize_default_mappings()
    
    def _initialize_default_mappings(self):
        """Initialize default broker symbol mappings"""
        
        # RoboForex-ECN mappings (standard format)
        roboforex_mapping = BrokerSymbolMapping(
            broker_name="RoboForex",
            server="RoboForex-ECN",
            symbol_mappings={
                "EURUSD": "EURUSD",
                "GBPUSD": "GBPUSD",
                "USDJPY": "USDJPY",
                "AUDUSD": "AUDUSD",
                "USDCAD": "USDCAD",
                "USDCHF": "USDCHF",
                "NZDUSD": "NZDUSD",
                "EURGBP": "EURGBP",
                "EURJPY": "EURJPY",
                "GBPJPY": "GBPJPY",
                "XAUUSD": "XAUUSD",
                "XAGUSD": "XAGUSD",
                "US30": "US30",
                "US500": "US500",
                "NAS100": "NAS100"
            },
            reverse_mappings={}
        )
        # Create reverse mappings
        roboforex_mapping.reverse_mappings = {v: k for k, v in roboforex_mapping.symbol_mappings.items()}
        self.broker_mappings["RoboForex-ECN"] = roboforex_mapping
        
        # CapitalxtendLLC-MU mappings (with exclamation marks)
        capitalxtend_mapping = BrokerSymbolMapping(
            broker_name="CapitalxtendLLC",
            server="CapitalxtendLLC-MU",
            symbol_mappings={
                "EURUSD": "EURUSD!",
                "GBPUSD": "GBPUSD!",
                "USDJPY": "USDJPY!",
                "AUDUSD": "AUDUSD!",
                "USDCAD": "USDCAD!",
                "USDCHF": "USDCHF!",
                "NZDUSD": "NZDUSD!",
                "EURGBP": "EURGBP!",
                "EURJPY": "EURJPY!",
                "GBPJPY": "GBPJPY!",
                "XAUUSD": "XAUUSD!",
                "XAGUSD": "XAGUSD!",
                "US30": "US30!",
                "US500": "US500!",
                "NAS100": "NAS100!"
            },
            reverse_mappings={}
        )
        # Create reverse mappings
        capitalxtend_mapping.reverse_mappings = {v: k for k, v in capitalxtend_mapping.symbol_mappings.items()}
        self.broker_mappings["CapitalxtendLLC-MU"] = capitalxtend_mapping
        
        logger.info(f"Initialized symbol mappings for {len(self.broker_mappings)} brokers")
    
    def get_broker_symbol(self, standard_symbol: str, server: str) -> str:
        """Convert standard symbol to broker-specific format"""
        # Remove any existing broker-specific suffixes first
        clean_symbol = self.get_standard_symbol(standard_symbol, server)
        
        if server in self.broker_mappings:
            mapping = self.broker_mappings[server]
            broker_symbol = mapping.symbol_mappings.get(clean_symbol, clean_symbol)
            logger.debug(f"Symbol mapping: {clean_symbol} -> {broker_symbol} for {server}")
            return broker_symbol
        else:
            logger.warning(f"No symbol mapping found for server {server}, using standard symbol: {clean_symbol}")
            return clean_symbol
    
    def get_standard_symbol(self, broker_symbol: str, server: str) -> str:
        """Convert broker-specific symbol to standard format"""
        if server in self.broker_mappings:
            mapping = self.broker_mappings[server]
            standard_symbol = mapping.reverse_mappings.get(broker_symbol, broker_symbol)
            
            # If not found in reverse mappings, try to clean common suffixes
            if standard_symbol == broker_symbol:
                # Remove common broker suffixes
                cleaned = broker_symbol.rstrip('!').rstrip('.').rstrip('_')
                if cleaned != broker_symbol:
                    logger.debug(f"Cleaned broker symbol: {broker_symbol} -> {cleaned}")
                    return cleaned
            
            return standard_symbol
        else:
            # Try to clean common suffixes for unknown brokers
            cleaned = broker_symbol.rstrip('!').rstrip('.').rstrip('_')
            if cleaned != broker_symbol:
                logger.debug(f"Cleaned unknown broker symbol: {broker_symbol} -> {cleaned}")
            return cleaned
    
    def normalize_symbol_for_grouping(self, symbol: str, server: str = None) -> str:
        """Normalize symbol for grouping purposes (always returns standard format)"""
        return self.get_standard_symbol(symbol, server or "")
    
    def validate_symbol_for_server(self, symbol: str, server: str) -> bool:
        """Validate if a symbol is available on a specific server"""
        if server in self.broker_mappings:
            mapping = self.broker_mappings[server]
            # Check if it's a valid broker symbol or can be mapped from standard
            return (symbol in mapping.reverse_mappings or 
                    symbol in mapping.symbol_mappings or
                    self.get_standard_symbol(symbol, server) in mapping.symbol_mappings)
        return True  # Assume valid for unknown servers
    
    def get_available_symbols_for_server(self, server: str) -> List[str]:
        """Get list of available symbols for a server"""
        if server in self.broker_mappings:
            return list(self.broker_mappings[server].symbol_mappings.values())
        return []
    
    def add_custom_mapping(self, server: str, standard_symbol: str, broker_symbol: str):
        """Add custom symbol mapping for a server"""
        if server not in self.broker_mappings:
            self.broker_mappings[server] = BrokerSymbolMapping(
                broker_name=server.split('-')[0] if '-' in server else server,
                server=server,
                symbol_mappings={},
                reverse_mappings={}
            )
        
        mapping = self.broker_mappings[server]
        mapping.symbol_mappings[standard_symbol] = broker_symbol
        mapping.reverse_mappings[broker_symbol] = standard_symbol
        
        logger.info(f"Added custom mapping: {standard_symbol} -> {broker_symbol} for {server}")
    
    def get_symbol_groups(self, accounts: List[Dict]) -> Dict[str, List[Dict]]:
        """Group accounts by normalized symbols for signal generation"""
        symbol_groups = {}
        
        for account in accounts:
            server = account.get('server', '')
            
            for symbol_config in account.get('symbols', []):
                original_symbol = symbol_config['symbol']
                timeframe = symbol_config['timeframe']
                
                # Normalize symbol for grouping
                standard_symbol = self.normalize_symbol_for_grouping(original_symbol, server)
                group_key = f"{standard_symbol}_{timeframe}"
                
                if group_key not in symbol_groups:
                    symbol_groups[group_key] = []
                
                # Add account with its specific symbol format
                account_copy = account.copy()
                account_copy['symbol_config'] = {
                    'original_symbol': original_symbol,
                    'standard_symbol': standard_symbol,
                    'broker_symbol': self.get_broker_symbol(standard_symbol, server),
                    'timeframe': timeframe
                }
                
                symbol_groups[group_key].append(account_copy)
                
                logger.debug(f"Grouped account {account.get('account_id')} with symbol {original_symbol} -> {standard_symbol} in group {group_key}")
        
        return symbol_groups


# Global symbol mapper instance
symbol_mapper = SymbolMapper()


def get_symbol_mapper() -> SymbolMapper:
    """Get the global symbol mapper instance"""
    return symbol_mapper
