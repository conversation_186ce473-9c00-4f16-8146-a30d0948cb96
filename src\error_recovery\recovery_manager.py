#!/usr/bin/env python3
"""
Comprehensive Error Recovery Manager
Provides retry mechanisms, circuit breakers, and recovery strategies for all system operations
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum
import random

from logging_system.logger import get_logger
from logging_system.logging_standards import create_logger, LogLevel

logger = get_logger(__name__)
standardized_logger = create_logger("ErrorRecovery")

class RecoveryAction(Enum):
    """Types of recovery actions"""
    RETRY = "retry"
    RETRY_WITH_DELAY = "retry_with_delay"
    RETRY_WITH_BACKOFF = "retry_with_backoff"
    CIRCUIT_BREAK = "circuit_break"
    FALLBACK = "fallback"
    ABORT = "abort"

class FailureType(Enum):
    """Types of failures"""
    NETWORK = "network"
    MT5_CONNECTION = "mt5_connection"
    MT5_LOGIN = "mt5_login"
    MT5_ORDER = "mt5_order"
    AI_REQUEST = "ai_request"
    VALIDATION = "validation"
    TIMEOUT = "timeout"
    UNKNOWN = "unknown"

@dataclass
class RecoveryStrategy:
    """Recovery strategy configuration"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_multiplier: float = 2.0
    jitter: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 300  # 5 minutes
    fallback_function: Optional[Callable] = None

@dataclass
class FailureRecord:
    """Record of a failure"""
    timestamp: datetime
    failure_type: FailureType
    operation: str
    error_message: str
    context: Dict[str, Any]

class CircuitBreaker:
    """Circuit breaker for preventing repeated failures"""
    
    def __init__(self, threshold: int = 5, timeout: int = 300):
        self.threshold = threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def record_failure(self):
        """Record a failure"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.threshold:
            self.state = "OPEN"
            standardized_logger.log_error_recovery(
                "CIRCUIT_BREAKER_OPEN",
                f"Threshold exceeded: {self.failure_count} failures",
                "Circuit breaker opened",
                False,
                {'threshold': self.threshold, 'failure_count': self.failure_count}
            )
    
    def record_success(self):
        """Record a success"""
        self.failure_count = 0
        self.last_failure_time = None
        if self.state != "CLOSED":
            self.state = "CLOSED"
            logger.info("🟢 Circuit breaker CLOSED after successful operation")
    
    def can_execute(self) -> bool:
        """Check if operation can be executed"""
        if self.state == "CLOSED":
            return True
        
        if self.state == "OPEN":
            if self.last_failure_time and \
               (datetime.now() - self.last_failure_time).total_seconds() > self.timeout:
                self.state = "HALF_OPEN"
                logger.info("🟡 Circuit breaker HALF_OPEN - allowing test operation")
                return True
            return False
        
        # HALF_OPEN state - allow one test operation
        return True

class ErrorRecoveryManager:
    """Comprehensive error recovery manager"""
    
    def __init__(self):
        self.failure_history: List[FailureRecord] = []
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.recovery_strategies: Dict[FailureType, RecoveryStrategy] = {}
        self._initialize_default_strategies()
    
    def _initialize_default_strategies(self):
        """Initialize default recovery strategies for different failure types"""
        
        # Network failures - aggressive retry with backoff
        self.recovery_strategies[FailureType.NETWORK] = RecoveryStrategy(
            max_retries=5,
            base_delay=2.0,
            max_delay=30.0,
            backoff_multiplier=2.0,
            circuit_breaker_threshold=10,
            circuit_breaker_timeout=600  # 10 minutes
        )
        
        # MT5 connection failures - moderate retry
        self.recovery_strategies[FailureType.MT5_CONNECTION] = RecoveryStrategy(
            max_retries=3,
            base_delay=5.0,
            max_delay=60.0,
            backoff_multiplier=2.0,
            circuit_breaker_threshold=5,
            circuit_breaker_timeout=300  # 5 minutes
        )
        
        # MT5 login failures - conservative retry
        self.recovery_strategies[FailureType.MT5_LOGIN] = RecoveryStrategy(
            max_retries=2,
            base_delay=10.0,
            max_delay=120.0,
            backoff_multiplier=3.0,
            circuit_breaker_threshold=3,
            circuit_breaker_timeout=900  # 15 minutes
        )
        
        # MT5 order failures - quick retry with AI fallback
        self.recovery_strategies[FailureType.MT5_ORDER] = RecoveryStrategy(
            max_retries=3,
            base_delay=1.0,
            max_delay=10.0,
            backoff_multiplier=2.0,
            circuit_breaker_threshold=5,
            circuit_breaker_timeout=300
        )
        
        # AI request failures - moderate retry
        self.recovery_strategies[FailureType.AI_REQUEST] = RecoveryStrategy(
            max_retries=3,
            base_delay=3.0,
            max_delay=30.0,
            backoff_multiplier=2.0,
            circuit_breaker_threshold=5,
            circuit_breaker_timeout=600
        )
        
        # Validation failures - no retry (fix the issue first)
        self.recovery_strategies[FailureType.VALIDATION] = RecoveryStrategy(
            max_retries=0,
            circuit_breaker_threshold=10,
            circuit_breaker_timeout=300
        )
        
        # Timeout failures - quick retry
        self.recovery_strategies[FailureType.TIMEOUT] = RecoveryStrategy(
            max_retries=2,
            base_delay=1.0,
            max_delay=15.0,
            backoff_multiplier=2.0,
            circuit_breaker_threshold=5,
            circuit_breaker_timeout=300
        )
    
    def get_circuit_breaker(self, operation_key: str) -> CircuitBreaker:
        """Get or create circuit breaker for operation"""
        if operation_key not in self.circuit_breakers:
            self.circuit_breakers[operation_key] = CircuitBreaker()
        return self.circuit_breakers[operation_key]
    
    async def execute_with_recovery(
        self,
        operation: Callable,
        operation_name: str,
        failure_type: FailureType = FailureType.UNKNOWN,
        context: Dict[str, Any] = None,
        *args,
        **kwargs
    ) -> Any:
        """Execute operation with comprehensive error recovery"""
        
        strategy = self.recovery_strategies.get(failure_type, RecoveryStrategy())
        circuit_breaker = self.get_circuit_breaker(f"{failure_type.value}_{operation_name}")
        context = context or {}
        
        # Check circuit breaker
        if not circuit_breaker.can_execute():
            logger.error(f"🔴 Circuit breaker OPEN for {operation_name} - operation blocked")
            raise Exception(f"Circuit breaker open for {operation_name}")
        
        last_exception = None
        
        for attempt in range(strategy.max_retries + 1):
            try:
                logger.debug(f"🔄 Executing {operation_name} (attempt {attempt + 1}/{strategy.max_retries + 1})")
                
                # Execute the operation
                if asyncio.iscoroutinefunction(operation):
                    result = await operation(*args, **kwargs)
                else:
                    result = operation(*args, **kwargs)
                
                # Success - record it and return
                circuit_breaker.record_success()
                if attempt > 0:
                    logger.info(f"✅ {operation_name} succeeded after {attempt + 1} attempts")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Record the failure
                failure_record = FailureRecord(
                    timestamp=datetime.now(),
                    failure_type=failure_type,
                    operation=operation_name,
                    error_message=str(e),
                    context=context
                )
                self.failure_history.append(failure_record)
                circuit_breaker.record_failure()
                
                logger.warning(f"⚠️ {operation_name} failed (attempt {attempt + 1}): {e}")
                
                # If this was the last attempt, don't wait
                if attempt >= strategy.max_retries:
                    break
                
                # Calculate delay with exponential backoff and jitter
                delay = min(
                    strategy.base_delay * (strategy.backoff_multiplier ** attempt),
                    strategy.max_delay
                )
                
                if strategy.jitter:
                    delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
                
                logger.info(f"⏳ Retrying {operation_name} in {delay:.1f} seconds...")
                await asyncio.sleep(delay)
        
        # All retries failed
        logger.error(f"❌ {operation_name} failed after {strategy.max_retries + 1} attempts")
        
        # Try fallback if available
        if strategy.fallback_function:
            try:
                logger.info(f"🔄 Attempting fallback for {operation_name}")
                return await strategy.fallback_function(*args, **kwargs)
            except Exception as fallback_error:
                logger.error(f"❌ Fallback also failed for {operation_name}: {fallback_error}")
        
        # Re-raise the last exception
        raise last_exception
    
    def get_failure_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Get failure statistics for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_failures = [f for f in self.failure_history if f.timestamp > cutoff_time]
        
        stats = {
            'total_failures': len(recent_failures),
            'failures_by_type': {},
            'failures_by_operation': {},
            'circuit_breaker_states': {}
        }
        
        for failure in recent_failures:
            # Count by type
            failure_type = failure.failure_type.value
            stats['failures_by_type'][failure_type] = stats['failures_by_type'].get(failure_type, 0) + 1
            
            # Count by operation
            operation = failure.operation
            stats['failures_by_operation'][operation] = stats['failures_by_operation'].get(operation, 0) + 1
        
        # Circuit breaker states
        for key, cb in self.circuit_breakers.items():
            stats['circuit_breaker_states'][key] = {
                'state': cb.state,
                'failure_count': cb.failure_count,
                'last_failure': cb.last_failure_time.isoformat() if cb.last_failure_time else None
            }
        
        return stats
    
    def reset_circuit_breaker(self, operation_key: str):
        """Manually reset a circuit breaker"""
        if operation_key in self.circuit_breakers:
            cb = self.circuit_breakers[operation_key]
            cb.failure_count = 0
            cb.last_failure_time = None
            cb.state = "CLOSED"
            logger.info(f"🔄 Circuit breaker reset for {operation_key}")


# Global error recovery manager instance
error_recovery_manager = ErrorRecoveryManager()
