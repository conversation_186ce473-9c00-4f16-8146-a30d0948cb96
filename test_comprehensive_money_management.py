#!/usr/bin/env python3
"""
Comprehensive test for all money management and risk controls
"""

import sys
import json
import asyncio
from datetime import datetime, timedelta
sys.path.append('src')

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import Account<PERSON>anager
from money_management.base_strategy import AccountInfo

async def test_daily_loss_limit():
    """Test if daily loss limit is properly enforced"""
    print("=" * 60)
    print("TESTING DAILY LOSS LIMIT ENFORCEMENT")
    print("=" * 60)
    
    # Create signal generator
    signal_gen = SignalGenerator()
    
    # Mock account with 20% loss (should trigger daily loss limit)
    account_id = "demo1"
    
    # Simulate daily PnL tracking
    today_key = datetime.now().strftime('%Y-%m-%d')
    signal_gen.daily_pnl[f"{account_id}_{today_key}"] = -15.0  # $15 loss
    
    # Test risk settings
    risk_settings = {
        'max_daily_loss': 3.0,  # $3 limit
        'max_daily_trades': 5,
        'max_open_positions': 2
    }
    
    # Test if daily loss limit blocks trading
    can_trade = signal_gen._check_risk_limits(account_id, risk_settings)
    
    print(f"Account: {account_id}")
    print(f"Daily Loss: $15.00")
    print(f"Daily Loss Limit: $3.00")
    print(f"Can Trade: {'YES' if can_trade else 'NO'}")
    print(f"Expected: NO")
    print(f"Result: {'✅ PASS' if not can_trade else '❌ FAIL - Daily loss limit not working!'}")

async def test_account_specific_settings():
    """Test if account-specific settings are enforced"""
    print("\n" + "=" * 60)
    print("TESTING ACCOUNT-SPECIFIC SETTINGS ENFORCEMENT")
    print("=" * 60)
    
    # Load actual account config
    with open('config/accounts.json', 'r') as f:
        config = json.load(f)
    
    account = config['accounts'][0]
    mm_settings = account['money_management_settings']
    
    print("Account Settings:")
    for key, value in mm_settings.items():
        print(f"  {key}: {value}")
    
    # Test risk calculation with account settings
    from money_management.percent_risk import PercentRiskStrategy
    
    strategy = PercentRiskStrategy(mm_settings)
    account_info = AccountInfo(74.40, 74.40, 0, 37.20, 1000, "USD", 500)
    
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    # Test with different stop loss scenarios
    scenarios = [
        {"name": "10 pips", "entry": 1.1000, "sl": 1.0990},
        {"name": "20 pips", "entry": 1.1000, "sl": 1.0980},
        {"name": "30 pips", "entry": 1.1000, "sl": 1.0970},
    ]
    
    print(f"\nRisk Calculations (Risk %: {mm_settings['risk_percent']}%):")
    for scenario in scenarios:
        trade_params = strategy.calculate_position_size(
            account_info, "EURUSD", scenario['entry'], scenario['sl'], [], market_data
        )
        
        expected_risk = account_info.balance * (mm_settings['risk_percent'] / 100)
        risk_multiplier = trade_params.risk_amount / expected_risk
        
        print(f"  {scenario['name']}: Volume {trade_params.volume:.2f}, Risk ${trade_params.risk_amount:.2f}, Multiplier {risk_multiplier:.1f}x")

async def test_multiple_tp_risk_control():
    """Test multiple TP risk control"""
    print("\n" + "=" * 60)
    print("TESTING MULTIPLE TP RISK CONTROL")
    print("=" * 60)
    
    # Simulate multiple TP scenario
    balance = 74.40
    risk_percent = 2.0
    expected_risk = balance * (risk_percent / 100)
    
    # Simulate 3 TP levels with minimum volume
    tp_levels = [
        {"volume_percent": 50, "volume": 0.01},
        {"volume_percent": 30, "volume": 0.01},
        {"volume_percent": 20, "volume": 0.01}
    ]
    
    total_volume = sum(tp['volume'] for tp in tp_levels)
    total_risk = total_volume * 30 * 10.0  # 30 pips * $10 per pip
    risk_multiplier = total_risk / expected_risk
    
    print(f"Account Balance: ${balance}")
    print(f"Risk Percent: {risk_percent}%")
    print(f"Expected Risk: ${expected_risk:.2f}")
    print(f"Multiple TP Levels: {len(tp_levels)}")
    print(f"Total Volume: {total_volume:.2f} lots")
    print(f"Total Risk: ${total_risk:.2f}")
    print(f"Risk Multiplier: {risk_multiplier:.1f}x")
    
    # Check if this would be acceptable
    max_multiplier = 10.0  # From account settings
    acceptable = risk_multiplier <= max_multiplier
    
    print(f"Max Allowed Multiplier: {max_multiplier}x")
    print(f"Result: {'✅ ACCEPTABLE' if acceptable else '❌ EXCESSIVE RISK'}")

async def test_ai_decision_integration():
    """Test AI decision integration"""
    print("\n" + "=" * 60)
    print("TESTING AI DECISION INTEGRATION")
    print("=" * 60)
    
    # Check if AI controls key decisions
    ai_controlled_decisions = [
        "Stop Loss Placement",
        "Take Profit Levels", 
        "Single vs Multiple TP",
        "Position Management",
        "Trade Entry/Exit"
    ]
    
    hardcoded_decisions = [
        "Default Risk Percent (2.0%)",
        "Default Volume (0.01)",
        "Magic Numbers (1001, 2001, etc.)",
        "Spread Limits (2.0, 1.5 pips)",
        "Volume Constraints"
    ]
    
    print("AI-Controlled Decisions:")
    for decision in ai_controlled_decisions:
        print(f"  ✅ {decision}")
    
    print("\nStill Hardcoded (Should be configurable):")
    for decision in hardcoded_decisions:
        print(f"  ⚠️ {decision}")

async def test_risk_validation_chain():
    """Test the complete risk validation chain"""
    print("\n" + "=" * 60)
    print("TESTING COMPLETE RISK VALIDATION CHAIN")
    print("=" * 60)
    
    # Simulate a high-risk trade scenario
    account_info = AccountInfo(74.40, 74.40, 0, 37.20, 1000, "USD", 500)
    
    # Mock account settings
    account = {
        'account_id': 'demo1',
        'money_management_settings': {
            'risk_percent': 2.0,
            'max_risk_per_trade': 10.0,
            'max_daily_loss': 3.0,
            'max_risk_multiplier': 10.0
        }
    }
    
    # Mock trade parameters with excessive risk
    class MockTradeParams:
        def __init__(self, volume, risk_amount):
            self.volume = volume
            self.risk_amount = risk_amount
    
    # Test scenarios
    test_cases = [
        {"name": "Normal Risk", "volume": 0.01, "risk": 1.50, "should_pass": True},
        {"name": "High Risk", "volume": 0.05, "risk": 7.50, "should_pass": True},  # Within 10% limit
        {"name": "Excessive Risk", "volume": 0.20, "risk": 30.00, "should_pass": False},  # Above 10% limit
    ]
    
    signal_gen = SignalGenerator()
    
    for case in test_cases:
        trade_params = MockTradeParams(case['volume'], case['risk'])
        
        # Test risk validation
        passes = signal_gen._check_trade_risk_limits(account, trade_params, account_info)
        
        print(f"{case['name']}:")
        print(f"  Volume: {case['volume']:.2f} lots")
        print(f"  Risk: ${case['risk']:.2f}")
        print(f"  Risk %: {(case['risk']/account_info.balance)*100:.1f}%")
        print(f"  Validation: {'PASS' if passes else 'FAIL'}")
        print(f"  Expected: {'PASS' if case['should_pass'] else 'FAIL'}")
        print(f"  Result: {'✅ CORRECT' if passes == case['should_pass'] else '❌ INCORRECT'}")
        print()

async def main():
    """Run all tests"""
    print("COMPREHENSIVE MONEY MANAGEMENT TESTING")
    print("=" * 60)
    
    await test_daily_loss_limit()
    await test_account_specific_settings()
    await test_multiple_tp_risk_control()
    await test_ai_decision_integration()
    await test_risk_validation_chain()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETED")
    print("=" * 60)
    print("\nNEXT STEPS:")
    print("1. Run the actual trading system")
    print("2. Monitor logs for risk enforcement")
    print("3. Verify daily loss limits work in practice")
    print("4. Check AI decision quality")

if __name__ == "__main__":
    asyncio.run(main())
