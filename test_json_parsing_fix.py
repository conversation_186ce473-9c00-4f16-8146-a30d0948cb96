#!/usr/bin/env python3
"""
Test JSON Parsing Fix for AI Trading Decisions
Critical test to ensure AI trading decisions are never lost due to JSON formatting issues
"""

import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_integration.response_sanitizer import response_sanitizer


def test_control_character_sanitization():
    """Test sanitization of control characters that break JSON parsing"""
    print("🧪 TESTING CONTROL CHARACTER SANITIZATION")
    print("=" * 50)
    
    # Test case 1: Control characters in JSON string
    malformed_json = '{\n"action": "BUY",\n"confidence": 0.85,\n"reasoning": "Strong\x0bbullish\x0csignal\x0dwith\x0egood\x0fmomentum",\n"risk_level": "MEDIUM"\n}'
    
    print(f"Original text length: {len(malformed_json)}")
    print(f"Contains control chars: {any(ord(c) < 32 for c in malformed_json if c not in ['\n', '\r', '\t'])}")
    
    # Test sanitization
    sanitized, was_sanitized = response_sanitizer.sanitize_response(malformed_json)
    print(f"Was sanitized: {was_sanitized}")
    print(f"Sanitized length: {len(sanitized)}")
    
    # Test JSON extraction
    extracted = response_sanitizer.extract_json_from_text(malformed_json)
    
    if extracted:
        print("✅ Successfully extracted trading decision:")
        print(f"   Action: {extracted.get('action')}")
        print(f"   Confidence: {extracted.get('confidence')}")
        print(f"   Risk Level: {extracted.get('risk_level')}")
        return True
    else:
        print("❌ Failed to extract trading decision")
        return False


def test_malformed_json_recovery():
    """Test recovery from completely malformed JSON"""
    print("\n🧪 TESTING MALFORMED JSON RECOVERY")
    print("=" * 50)
    
    # Test case: Completely broken JSON but with trading info
    broken_response = '''
    Based on the market analysis, I recommend the following trading decision:
    
    "action": "SELL",
    "confidence": 0.78,
    "reasoning": "The market shows bearish momentum with strong resistance at current levels. Technical indicators suggest a downward move is likely.",
    "risk_level": "MEDIUM",
    "entry_price": 1.0850,
    "stop_loss": 1.0880,
    "take_profit": 1.0800
    
    This decision is based on comprehensive analysis of market conditions.
    '''
    
    print("Testing extraction from malformed response...")
    
    extracted = response_sanitizer.extract_json_from_text(broken_response)
    
    if extracted:
        print("✅ Successfully recovered trading decision from malformed JSON:")
        print(f"   Action: {extracted.get('action')}")
        print(f"   Confidence: {extracted.get('confidence')}")
        print(f"   Entry Price: {extracted.get('entry_price')}")
        print(f"   Stop Loss: {extracted.get('stop_loss')}")
        print(f"   Take Profit: {extracted.get('take_profit')}")
        return True
    else:
        print("❌ Failed to recover trading decision")
        return False


def test_intelligent_fallback():
    """Test intelligent fallback when all parsing fails"""
    print("\n🧪 TESTING INTELLIGENT FALLBACK")
    print("=" * 50)
    
    # Test case: No structured data but sentiment indicators
    sentiment_response = '''
    The market is showing strong bullish signals with high volatility.
    I recommend taking a long position as the upward momentum is likely to continue.
    However, this is a high risk trade due to current market conditions.
    '''
    
    print("Testing intelligent fallback with sentiment analysis...")
    
    fallback = response_sanitizer.create_intelligent_fallback(sentiment_response, "Test parsing failure")
    
    print("✅ Intelligent fallback created:")
    print(f"   Action: {fallback.get('action')}")
    print(f"   Confidence: {fallback.get('confidence')}")
    print(f"   Risk Level: {fallback.get('risk_level')}")
    print(f"   Reasoning: {fallback.get('reasoning')[:100]}...")
    
    return fallback.get('action') != 'HOLD'  # Should detect bullish sentiment


def test_valid_json_preservation():
    """Test that valid JSON responses are preserved correctly"""
    print("\n🧪 TESTING VALID JSON PRESERVATION")
    print("=" * 50)
    
    # Test case: Perfect JSON response
    valid_json = '''
    {
        "action": "BUY",
        "confidence": 0.92,
        "reasoning": "Strong technical breakout with volume confirmation",
        "risk_level": "LOW",
        "entry_price": 1.0850,
        "stop_loss": 1.0820,
        "take_profit": 1.0920,
        "market_analysis": "Bullish trend continuation",
        "strategy_alignment": "Perfect alignment with trend following strategy"
    }
    '''
    
    print("Testing preservation of valid JSON...")
    
    extracted = response_sanitizer.extract_json_from_text(valid_json)
    
    if extracted and extracted.get('action') == 'BUY' and extracted.get('confidence') == 0.92:
        print("✅ Valid JSON preserved correctly:")
        print(f"   Action: {extracted.get('action')}")
        print(f"   Confidence: {extracted.get('confidence')}")
        print(f"   Market Analysis: {extracted.get('market_analysis')}")
        return True
    else:
        print("❌ Valid JSON was corrupted during processing")
        return False


def test_edge_cases():
    """Test various edge cases"""
    print("\n🧪 TESTING EDGE CASES")
    print("=" * 50)
    
    test_cases = [
        ("Empty string", ""),
        ("Only whitespace", "   \n\t  "),
        ("No trading data", "This is just random text with no trading information."),
        ("Multiple JSON blocks", '{"action": "HOLD"} some text {"action": "BUY", "confidence": 0.8}'),
        ("JSON with trailing comma", '{"action": "SELL", "confidence": 0.7,}'),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for name, test_input in test_cases:
        print(f"\nTesting: {name}")
        try:
            result = response_sanitizer.extract_json_from_text(test_input)
            if result:
                print(f"   ✅ Extracted: {result.get('action', 'N/A')}")
                passed += 1
            else:
                fallback = response_sanitizer.create_intelligent_fallback(test_input, "Edge case test")
                print(f"   🔧 Fallback: {fallback.get('action', 'N/A')}")
                passed += 1
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\nEdge cases handled: {passed}/{total}")
    return passed == total


def run_comprehensive_test():
    """Run all JSON parsing fix tests"""
    print("🚀 COMPREHENSIVE JSON PARSING FIX TEST")
    print("=" * 60)
    
    tests = [
        ("Control Character Sanitization", test_control_character_sanitization),
        ("Malformed JSON Recovery", test_malformed_json_recovery),
        ("Intelligent Fallback", test_intelligent_fallback),
        ("Valid JSON Preservation", test_valid_json_preservation),
        ("Edge Cases", test_edge_cases),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! JSON parsing fix is working correctly.")
        print("✅ AI trading decisions will no longer be lost due to JSON formatting issues.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
