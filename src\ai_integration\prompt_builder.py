"""
AI Prompt Builder for Trading Decisions
"""

from typing import Dict, Any, List
from datetime import datetime

from strategies.base_strategy import BaseStrategy, MarketData
from money_management.base_strategy import BaseMoneyManagement, AccountInfo
from logging_system.logger import get_logger

logger = get_logger(__name__)

class PromptBuilder:
    """Builds comprehensive prompts for AI trading decisions"""
    
    def __init__(self):
        pass
    
    def build_trading_prompt(
        self,
        strategy: BaseStrategy,
        money_management: BaseMoneyManagement,
        market_data: MarketData,
        trade_history: List[Dict[str, Any]],
        account_info: AccountInfo,
        additional_context: Dict[str, Any] = None
    ) -> str:
        """Build comprehensive trading prompt combining strategy and money management"""
        
        try:
            # Get base prompts from strategy and money management
            strategy_prompt = strategy.get_ai_prompt(market_data, trade_history, account_info.__dict__)
            money_management_prompt = money_management.get_ai_prompt(account_info, trade_history)
            
            # Build market data section
            market_data_section = self._build_market_data_section(market_data)
            
            # Build trade history section
            trade_history_section = self._build_trade_history_section(trade_history, strategy.magic_number)
            
            # Build account risk information section
            account_risk_section = self._build_account_risk_section(account_info, additional_context)

            # Build comprehensive prompt
            comprehensive_prompt = f"""
{strategy_prompt}

{money_management_prompt}

{account_risk_section}

{market_data_section}

{trade_history_section}

INTEGRATION INSTRUCTIONS:
You must consider BOTH the trading strategy requirements AND the money management constraints when making decisions.

The money management system will calculate the exact position size based on your stop loss placement, so focus on:
1. High-quality trade setups that match the strategy criteria
2. Appropriate stop loss placement for risk management
3. Take profit levels that align with strategy expectations
4. Overall market assessment and timing
5. Account-specific risk limits and constraints

RESPONSE REQUIREMENTS:
Provide your analysis and trading decision in the following JSON format:

{{
    "action": "BUY|SELL|HOLD|CLOSE",
    "confidence": 0.0-1.0,
    "entry_price": number or null,
    "stop_loss": number or null,
    "take_profit": number or null,
    "take_profit_levels": [
        {{
            "price": number,
            "volume_percent": number (0-100, percentage of total position)
        }}
    ] or null,
    "reasoning": "detailed explanation combining strategy and money management factors",
    "risk_level": "LOW|MEDIUM|HIGH",
    "market_analysis": "current market condition assessment",
    "strategy_alignment": "how this trade aligns with the strategy",
    "risk_assessment": "money management and risk considerations",
    "additional_signals": [
        // Additional trading opportunities if any
    ]
}}

STOP LOSS AND TAKE PROFIT GUIDELINES:

STOP LOSS PLACEMENT (MANDATORY):
- CRITICAL: Every trade MUST have a stop loss - trades without stop loss will be REJECTED
- Stop loss is required for position sizing calculations in percent risk money management
- Place stop loss based on technical levels (support/resistance, trend lines, etc.)
- Consider volatility: wider stops for volatile markets, tighter for stable markets
- Account for spread and slippage in stop loss placement
- Analyze current market volatility and adjust stop distance accordingly
- Use technical analysis to determine optimal stop loss placement
- Consider timeframe: shorter timeframes may require tighter stops, longer timeframes may allow wider stops
- VALIDATION: System will reject any signal without a valid stop loss price

TAKE PROFIT STRATEGY DECISION:
You must choose between single TP or multiple TP based on:

SINGLE TP ("take_profit") - Use when:
- Clear single target level exists
- Market conditions favor quick exits
- Account size is small (risk management concerns)
- Low confidence setups
- Scalping or quick profit strategies

MULTIPLE TP ("take_profit_levels") - Use when:
- Strong trend continuation expected
- Multiple resistance/support levels identified
- High confidence setups with good risk/reward
- Larger account sizes that can handle position scaling
- Swing trading or position trading approaches

MULTIPLE TP GUIDELINES:
- Specify volume_percent for each level (must sum to 100%)
- Typical allocation: 50% at TP1, 30% at TP2, 20% at TP3
- TP1: Conservative target (1:1 to 1:2 risk/reward)
- TP2: Moderate target (1:2 to 1:3 risk/reward)
- TP3: Aggressive target (1:3+ risk/reward)
- Example multiple TPs:
  "take_profit_levels": [
    {{"price": 1.2050, "volume_percent": 50}},
    {{"price": 1.2100, "volume_percent": 30}},
    {{"price": 1.2150, "volume_percent": 20}}
  ]
- This allows partial profit taking at different levels
- Consider market volatility and strategy requirements when setting multiple TPs

Remember: Quality over quantity. Only recommend trades that meet both strategy criteria and money management constraints.
"""
            
            if additional_context:
                comprehensive_prompt += f"\n\nADDITIONAL CONTEXT:\n{self._format_additional_context(additional_context)}"
            
            return comprehensive_prompt
            
        except Exception as e:
            logger.error(f"Error building trading prompt: {e}")
            return self._get_fallback_prompt(market_data, account_info)
    
    def _build_market_data_section(self, market_data: MarketData) -> str:
        """Build market data section of the prompt"""
        
        # Format recent candles for analysis
        recent_candles = market_data.candles[-50:] if len(market_data.candles) > 50 else market_data.candles
        
        candles_text = "RECENT MARKET DATA (Last 50 candles):\n"
        candles_text += "Time, Open, High, Low, Close, Volume\n"
        
        for candle in recent_candles:
            candles_text += f"{candle.get('time', 'N/A')}, {candle.get('open', 0)}, {candle.get('high', 0)}, {candle.get('low', 0)}, {candle.get('close', 0)}, {candle.get('volume', 0)}\n"
        
        return f"""
CURRENT MARKET CONDITIONS:
- Symbol: {market_data.symbol}
- Timeframe: {market_data.timeframe}
- Current Price: {market_data.current_price}
- Spread: {market_data.spread} pips
- Volume: {market_data.volume}
- Volatility: {market_data.volatility:.5f}
- Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{candles_text}

TECHNICAL ANALYSIS REQUIREMENTS:
Based on the above market data, analyze:
1. Trend direction and strength
2. Support and resistance levels
3. Momentum indicators
4. Volume patterns
5. Price action signals
6. Market structure
"""
    
    def _build_trade_history_section(self, trade_history: List[Dict[str, Any]], magic_number: int) -> str:
        """Build trade history section of the prompt"""
        
        # Filter trades by magic number (strategy-specific)
        strategy_trades = [trade for trade in trade_history if trade.get('magic_number') == magic_number]
        recent_trades = strategy_trades[-20:] if len(strategy_trades) > 20 else strategy_trades
        
        if not recent_trades:
            return """
TRADE HISTORY:
No previous trades found for this strategy. This will be a fresh start.
Focus on high-probability setups and proper risk management.
"""
        
        # Calculate performance metrics
        total_trades = len(recent_trades)
        winning_trades = sum(1 for trade in recent_trades if trade.get('profit', 0) > 0)
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        total_profit = sum(trade.get('profit', 0) for trade in recent_trades)
        
        trade_history_text = f"""
STRATEGY TRADE HISTORY (Last {len(recent_trades)} trades):
- Total Trades: {total_trades}
- Winning Trades: {winning_trades}
- Win Rate: {win_rate:.1f}%
- Total Profit: ${total_profit:.2f}

RECENT TRADES DETAILS:
"""
        
        for i, trade in enumerate(recent_trades[-10:], 1):  # Show last 10 trades
            trade_history_text += f"""
Trade {i}:
- Symbol: {trade.get('symbol', 'N/A')}
- Type: {trade.get('type', 'N/A')}
- Volume: {trade.get('volume', 0)}
- Entry: {trade.get('entry_price', 0)}
- Exit: {trade.get('exit_price', 0)}
- Profit: ${trade.get('profit', 0):.2f}
- Duration: {trade.get('duration_hours', 0):.1f} hours
- Exit Reason: {trade.get('exit_reason', 'N/A')}
"""
        
        return trade_history_text
    
    def _format_additional_context(self, context: Dict[str, Any]) -> str:
        """Format additional context information"""
        formatted = ""
        
        for key, value in context.items():
            formatted += f"- {key.replace('_', ' ').title()}: {value}\n"
        
        return formatted
    
    def _get_fallback_prompt(self, market_data: MarketData, account_info: AccountInfo) -> str:
        """Get fallback prompt when main prompt building fails - NO HARDCODED DECISIONS"""
        return f"""
EMERGENCY TRADING ANALYSIS

System encountered an error in detailed prompt building. Please analyze the available data and make your own trading decision.

MARKET DATA:
- Symbol: {market_data.symbol}
- Timeframe: {market_data.timeframe}
- Current Price: {market_data.current_price}
- Volatility: {getattr(market_data, 'volatility', 'Unknown')}

ACCOUNT INFO:
- Balance: ${account_info.balance:.2f}
- Equity: ${account_info.equity:.2f}
- Free Margin: ${account_info.free_margin:.2f}

IMPORTANT: Despite the system error, you must still make an independent trading decision based on:
1. Current market conditions
2. Price action analysis
3. Risk management principles
4. Account size considerations

Do NOT default to HOLD just because of the system error. Analyze the available data and provide your best trading recommendation.

Respond in JSON format:
{{
    "action": "BUY|SELL|HOLD",
    "confidence": 0.0-1.0,
    "entry_price": number or null,
    "stop_loss": number or null,
    "take_profit": number or null,
    "reasoning": "your analysis despite limited data",
    "risk_level": "LOW|MEDIUM|HIGH"
}}
"""

    def _build_account_risk_section(self, account_info: AccountInfo, additional_context: Dict[str, Any] = None) -> str:
        """Build account-specific risk information section"""
        try:
            # Get account settings from additional context
            account_settings = additional_context.get('account_settings', {}) if additional_context else {}
            mm_settings = account_settings.get('money_management_settings', {})

            # Extract risk settings
            risk_percent = mm_settings.get('risk_percent', 2.0)
            max_daily_loss = mm_settings.get('max_daily_loss', 5.0)
            max_daily_trades = mm_settings.get('max_daily_trades', 5)
            max_open_positions = mm_settings.get('max_open_positions', 3)
            max_risk_per_trade = mm_settings.get('max_risk_per_trade', 15.0)

            # Calculate risk amounts
            risk_amount_per_trade = account_info.balance * (risk_percent / 100)
            max_risk_amount = account_info.balance * (max_risk_per_trade / 100)

            return f"""
ACCOUNT RISK MANAGEMENT SETTINGS:
Balance: ${account_info.balance:.2f} | Equity: ${account_info.equity:.2f} | Free Margin: ${account_info.free_margin:.2f}
Leverage: {account_info.leverage}:1 | Currency: {account_info.currency}

RISK LIMITS (STRICTLY ENFORCED):
- Risk per trade: {risk_percent}% (${risk_amount_per_trade:.2f})
- Maximum risk per trade: {max_risk_per_trade}% (${max_risk_amount:.2f})
- Daily loss limit: ${max_daily_loss:.2f}
- Daily trade limit: {max_daily_trades} trades
- Maximum open positions: {max_open_positions}

RISK MANAGEMENT REQUIREMENTS:
- Every trade MUST have a stop loss
- Position size will be calculated automatically based on your stop loss
- Consider account size when choosing between single vs multiple TP
- Smaller accounts ({account_info.balance:.0f} < $500): Prefer single TP for better risk control
- Larger accounts ({account_info.balance:.0f} >= $500): Multiple TP acceptable for scaling

CURRENT ACCOUNT STATUS:
- Account size category: {'SMALL' if account_info.balance < 500 else 'MEDIUM' if account_info.balance < 2000 else 'LARGE'}
- Recommended TP strategy: {'Single TP (better risk control)' if account_info.balance < 500 else 'Single or Multiple TP (your choice)'}
- Risk tolerance: {'Conservative' if account_info.balance < 500 else 'Moderate' if account_info.balance < 2000 else 'Flexible'}
"""
        except Exception as e:
            logger.error(f"Error building account risk section: {e}")
            return f"""
ACCOUNT RISK MANAGEMENT:
Balance: ${account_info.balance:.2f}
Please use conservative risk management with mandatory stop losses.
"""
