#!/usr/bin/env python3
"""
Test Critical Bug Fix
Verify that the money_management_settings key fix resolves the position limits issue
"""

import sys
import json
import asyncio
from unittest.mock import Mock, patch
sys.path.append('src')

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import AccountManager

class MockMT5Client:
    """Mock MT5 client for testing"""
    def __init__(self, existing_pending_orders=2):
        self.current_account = True
        self.existing_pending_orders = existing_pending_orders
        
    def get_account_info(self):
        class MockBalance:
            balance = 75.84  # Same as in your logs
        return MockBalance()
    
    def get_positions(self):
        # Return 2 existing positions as shown in logs
        return [
            {'symbol': 'EURUSD', 'magic': 0, 'volume': 0.01},
            {'symbol': 'EURUSD', 'magic': 0, 'volume': 0.01}
        ]
    
    def get_pending_orders(self):
        # Return existing pending orders
        orders = []
        for i in range(self.existing_pending_orders):
            orders.append({'symbol': 'EURUSD', 'magic': 0, 'volume': 0.01})
        return orders

async def test_account_dict_creation():
    """Test that account dict is created with correct key names"""
    print("🔍 TESTING ACCOUNT DICT CREATION")
    print("=" * 60)
    
    # Load account manager
    account_manager = AccountManager()
    account_manager.load_accounts()
    
    # Create signal generator
    signal_gen = SignalGenerator(account_manager)
    
    # Create virtual groups (this is where the bug was)
    groups = signal_gen._create_virtual_groups()
    
    print(f"Created {len(groups)} groups")
    
    if groups:
        group = groups[0]
        accounts = group['accounts']
        
        if accounts:
            account_dict = accounts[0]
            
            print("📋 ACCOUNT DICT KEYS:")
            for key in account_dict.keys():
                print(f"  {key}")
            
            # Check for the correct key
            has_correct_key = 'money_management_settings' in account_dict
            has_wrong_key = 'money_management_config' in account_dict
            
            print(f"\n🔍 KEY ANALYSIS:")
            print(f"  Has 'money_management_settings': {'✅' if has_correct_key else '❌'}")
            print(f"  Has 'money_management_config': {'✅' if has_wrong_key else '❌'}")
            
            if has_correct_key:
                mm_settings = account_dict['money_management_settings']
                print(f"\n💰 MONEY MANAGEMENT SETTINGS:")
                for key, value in mm_settings.items():
                    if not key.startswith('_'):
                        print(f"  {key}: {value}")
                
                # Check specific values
                max_pending = mm_settings.get('max_pending_orders', 'NOT FOUND')
                max_positions = mm_settings.get('max_open_positions', 'NOT FOUND')
                
                print(f"\n🎯 CRITICAL VALUES:")
                print(f"  max_pending_orders: {max_pending}")
                print(f"  max_open_positions: {max_positions}")
                
                return has_correct_key and max_pending == 4 and max_positions == 2
            else:
                print("❌ money_management_settings not found!")
                return False
    
    return False

async def test_risk_settings_with_fix():
    """Test that risk settings now use account values instead of environment defaults"""
    print("\n🔍 TESTING RISK SETTINGS WITH FIX")
    print("=" * 60)
    
    # Load account manager
    account_manager = AccountManager()
    account_manager.load_accounts()
    
    # Create signal generator
    signal_gen = SignalGenerator(account_manager)
    
    # Create virtual groups
    groups = signal_gen._create_virtual_groups()
    
    if groups and groups[0]['accounts']:
        account_dict = groups[0]['accounts'][0]
        
        print("📋 TESTING RISK SETTINGS RESOLUTION:")
        
        # Get risk settings
        risk_settings = signal_gen._get_account_risk_settings(account_dict)
        
        print(f"  Final max_pending_orders: {risk_settings['max_pending_orders']}")
        print(f"  Final max_open_positions: {risk_settings['max_open_positions']}")
        
        # Check environment defaults
        print(f"  Environment max_pending_orders: {signal_gen.default_max_pending_orders}")
        print(f"  Environment max_open_positions: {signal_gen.default_max_open_positions}")
        
        # Check account settings
        mm_settings = account_dict.get('money_management_settings', {})
        account_pending = mm_settings.get('max_pending_orders', 'NOT FOUND')
        account_positions = mm_settings.get('max_open_positions', 'NOT FOUND')
        
        print(f"  Account max_pending_orders: {account_pending}")
        print(f"  Account max_open_positions: {account_positions}")
        
        # Verify the fix
        using_account_settings = (
            risk_settings['max_pending_orders'] == account_pending and
            risk_settings['max_open_positions'] == account_positions
        )
        
        using_env_defaults = (
            risk_settings['max_pending_orders'] == signal_gen.default_max_pending_orders and
            risk_settings['max_open_positions'] == signal_gen.default_max_open_positions
        )
        
        print(f"\n🎯 ANALYSIS:")
        print(f"  Using account settings: {'✅' if using_account_settings else '❌'}")
        print(f"  Using environment defaults: {'✅' if using_env_defaults else '❌'}")
        
        if using_account_settings:
            print("✅ FIX SUCCESSFUL: Account settings are being used!")
            return True
        elif using_env_defaults:
            print("❌ FIX FAILED: Still using environment defaults!")
            return False
        else:
            print("❓ UNKNOWN: Using neither account nor environment values")
            return False
    
    return False

async def test_position_limits_with_fix():
    """Test position limits enforcement with the fix"""
    print("\n🔍 TESTING POSITION LIMITS WITH FIX")
    print("=" * 60)
    
    # Load account manager
    account_manager = AccountManager()
    account_manager.load_accounts()
    
    # Create signal generator
    signal_gen = SignalGenerator(account_manager)
    signal_gen.mt5_client = MockMT5Client(existing_pending_orders=2)
    
    # Create virtual groups
    groups = signal_gen._create_virtual_groups()
    
    if groups and groups[0]['accounts']:
        account_dict = groups[0]['accounts'][0]
        
        print("📊 TESTING POSITION LIMITS ENFORCEMENT:")
        print("  Existing pending orders: 2")
        print("  Attempting: Multiple TP signal (3 new pending orders)")
        print("  Total after: 2 + 3 = 5")
        
        # Get risk settings
        risk_settings = signal_gen._get_account_risk_settings(account_dict)
        print(f"  Max allowed: {risk_settings['max_pending_orders']}")
        
        # Test position limits
        can_trade = signal_gen._check_position_limits(account_dict, risk_settings, is_multiple_tp=True)
        
        should_block = 5 > risk_settings['max_pending_orders']
        
        print(f"  Should block: {'YES' if should_block else 'NO'}")
        print(f"  System blocks: {'YES' if not can_trade else 'NO'}")
        
        if should_block and not can_trade:
            print("✅ CORRECT: System properly blocks trade that exceeds limits")
            return True
        elif not should_block and can_trade:
            print("✅ CORRECT: System allows valid trade")
            return True
        else:
            print("❌ INCORRECT: System behavior doesn't match expected")
            return False
    
    return False

async def main():
    """Run all tests for the critical bug fix"""
    print("🚀 TESTING CRITICAL BUG FIX")
    print("=" * 70)
    print("Bug: money_management_config vs money_management_settings key mismatch")
    print("=" * 70)
    
    # Run all tests
    results = []
    
    results.append(await test_account_dict_creation())
    results.append(await test_risk_settings_with_fix())
    results.append(await test_position_limits_with_fix())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 70)
    print("🎯 CRITICAL BUG FIX SUMMARY")
    print("=" * 70)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 CRITICAL BUG FIXED!")
        print("✅ Account settings are now properly used instead of environment defaults")
        print("✅ Position limits will now be enforced correctly")
        print("✅ Your trading system is now safe for live trading")
    else:
        print("\n⚠️ BUG FIX INCOMPLETE")
        print("❌ Additional fixes may be required")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
