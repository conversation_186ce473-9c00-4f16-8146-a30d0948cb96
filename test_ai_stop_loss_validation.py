#!/usr/bin/env python3
"""
Comprehensive AI Stop Loss Decision Validation Test
Tests that AI makes dynamic stop loss decisions instead of using hardcoded values
"""

import sys
import os
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add src to path
sys.path.append('src')

from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import Prompt<PERSON>uilder
from strategies.trend_following import TrendFollowingStrategy
from money_management.percent_risk import PercentRiskStrategy
from money_management.base_strategy import AccountInfo
from strategies.base_strategy import MarketData
from account_management.models import TradingAccount

class AIStopLossValidator:
    """Validates AI stop loss decision making"""
    
    def __init__(self):
        self.prompt_builder = PromptBuilder()
        self.test_results = []
        
    async def run_comprehensive_validation(self):
        """Run comprehensive AI stop loss validation"""
        print("🤖 AI STOP LOSS DECISION VALIDATION")
        print("=" * 60)
        
        # Test scenarios with different market conditions
        test_scenarios = [
            {
                "name": "High Volatility EURUSD",
                "symbol": "EURUSD",
                "timeframe": "M15",
                "current_price": 1.0850,
                "volatility": 0.0025,  # High volatility
                "spread": 1.5,
                "market_condition": "volatile"
            },
            {
                "name": "Low Volatility EURUSD",
                "symbol": "EURUSD", 
                "timeframe": "H1",
                "current_price": 1.0850,
                "volatility": 0.0008,  # Low volatility
                "spread": 1.2,
                "market_condition": "stable"
            },
            {
                "name": "Trending GBPUSD",
                "symbol": "GBPUSD",
                "timeframe": "M15",
                "current_price": 1.2650,
                "volatility": 0.0018,
                "spread": 2.1,
                "market_condition": "trending"
            },
            {
                "name": "Ranging USDJPY",
                "symbol": "USDJPY",
                "timeframe": "M30",
                "current_price": 149.85,
                "volatility": 0.0012,
                "spread": 1.8,
                "market_condition": "ranging"
            }
        ]
        
        async with QwenClient() as qwen_client:
            for scenario in test_scenarios:
                await self._test_scenario(scenario, qwen_client)
        
        # Analyze results
        self._analyze_results()
        
    async def _test_scenario(self, scenario: Dict[str, Any], qwen_client: QwenClient):
        """Test AI stop loss decision for a specific scenario"""
        print(f"\n📊 Testing: {scenario['name']}")
        print("-" * 40)
        
        # Create test data
        market_data = MarketData(
            symbol=scenario['symbol'],
            timeframe=scenario['timeframe'],
            current_price=scenario['current_price'],
            bid=scenario['current_price'] - scenario['spread'] * 0.00001,
            ask=scenario['current_price'] + scenario['spread'] * 0.00001,
            spread=scenario['spread'],
            volume=1000,
            timestamp=datetime.now()
        )
        
        account_info = AccountInfo(
            balance=74.40,  # Current demo account balance
            equity=74.40,
            margin=0.0,
            free_margin=74.40,
            margin_level=0.0,
            currency="USD",
            leverage=500
        )
        
        # Create strategy and money management
        strategy = TrendFollowingStrategy({})
        money_management = PercentRiskStrategy({'risk_percent': 2.0})
        
        # Create account settings for context
        account_settings = {
            'money_management_settings': {
                'risk_percent': 2.0,
                'max_daily_loss': 3.0,
                'max_daily_trades': 5,
                'max_open_positions': 2
            }
        }
        
        additional_context = {
            'account_settings': account_settings,
            'market_condition': scenario['market_condition'],
            'volatility_level': scenario['volatility']
        }
        
        # Build AI prompt
        prompt = self.prompt_builder.build_trading_prompt(
            strategy=strategy,
            money_management=money_management,
            market_data=market_data,
            trade_history=[],
            account_info=account_info,
            additional_context=additional_context
        )
        
        # Test multiple AI calls to check consistency
        ai_responses = []
        for i in range(3):  # Test 3 times for consistency
            try:
                response = await qwen_client.generate_trading_decision(prompt)
                ai_responses.append(response)
                print(f"  Response {i+1}: {response.get('action', 'UNKNOWN')}")
                
                if response.get('stop_loss'):
                    stop_loss = response['stop_loss']
                    entry_price = response.get('entry_price', scenario['current_price'])
                    
                    # Calculate pip distance
                    pip_size = 0.0001 if 'JPY' not in scenario['symbol'] else 0.01
                    pip_distance = abs(entry_price - stop_loss) / pip_size
                    
                    print(f"    Entry: {entry_price:.5f}, SL: {stop_loss:.5f}")
                    print(f"    Pip Distance: {pip_distance:.1f} pips")
                    print(f"    Reasoning: {response.get('reasoning', 'No reasoning')[:100]}...")
                    
            except Exception as e:
                print(f"  ❌ Error in response {i+1}: {e}")
                ai_responses.append({"error": str(e)})
        
        # Store results for analysis
        self.test_results.append({
            'scenario': scenario,
            'responses': ai_responses,
            'timestamp': datetime.now()
        })
        
    def _analyze_results(self):
        """Analyze AI stop loss decision results"""
        print("\n🔍 AI STOP LOSS ANALYSIS")
        print("=" * 60)
        
        total_scenarios = len(self.test_results)
        scenarios_with_sl = 0
        dynamic_sl_decisions = 0
        consistent_decisions = 0
        
        for result in self.test_results:
            scenario_name = result['scenario']['name']
            responses = result['responses']
            
            print(f"\n📈 {scenario_name}:")
            
            # Check if AI provided stop loss
            sl_values = []
            for response in responses:
                if isinstance(response, dict) and response.get('stop_loss'):
                    sl_values.append(response['stop_loss'])
            
            if sl_values:
                scenarios_with_sl += 1
                
                # Check for dynamic decisions (different SL values indicate AI is thinking)
                unique_sl_values = len(set(sl_values))
                if unique_sl_values > 1:
                    dynamic_sl_decisions += 1
                    print(f"  ✅ Dynamic SL decisions: {unique_sl_values} different values")
                else:
                    print(f"  ⚠️  Consistent SL: {sl_values[0]:.5f} (may indicate fixed logic)")
                
                # Check consistency in reasoning
                reasoning_keywords = []
                for response in responses:
                    if isinstance(response, dict) and response.get('reasoning'):
                        reasoning = response['reasoning'].lower()
                        if any(keyword in reasoning for keyword in ['technical', 'support', 'resistance', 'volatility']):
                            reasoning_keywords.append(True)
                        else:
                            reasoning_keywords.append(False)
                
                if any(reasoning_keywords):
                    consistent_decisions += 1
                    print(f"  ✅ Technical reasoning provided")
                else:
                    print(f"  ❌ No technical reasoning found")
                    
                # Show SL values
                print(f"  SL Values: {[f'{sl:.5f}' for sl in sl_values]}")
            else:
                print(f"  ❌ No stop loss provided in any response")
        
        # Summary
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"Total Scenarios: {total_scenarios}")
        print(f"Scenarios with SL: {scenarios_with_sl}/{total_scenarios}")
        print(f"Dynamic SL Decisions: {dynamic_sl_decisions}/{scenarios_with_sl}")
        print(f"Technical Reasoning: {consistent_decisions}/{scenarios_with_sl}")
        
        # Pass/Fail criteria
        sl_coverage = scenarios_with_sl / total_scenarios if total_scenarios > 0 else 0
        dynamic_ratio = dynamic_sl_decisions / scenarios_with_sl if scenarios_with_sl > 0 else 0
        reasoning_ratio = consistent_decisions / scenarios_with_sl if scenarios_with_sl > 0 else 0
        
        print(f"\n🎯 VALIDATION RESULTS:")
        print(f"SL Coverage: {sl_coverage:.1%} (Target: 100%)")
        print(f"Dynamic Decisions: {dynamic_ratio:.1%} (Target: >50%)")
        print(f"Technical Reasoning: {reasoning_ratio:.1%} (Target: >80%)")
        
        overall_pass = sl_coverage >= 1.0 and reasoning_ratio >= 0.8
        print(f"\n{'✅ PASS' if overall_pass else '❌ FAIL'}: AI Stop Loss Validation")
        
        return overall_pass

async def main():
    """Main test function"""
    validator = AIStopLossValidator()
    
    try:
        await validator.run_comprehensive_validation()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
