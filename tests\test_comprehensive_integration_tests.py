#!/usr/bin/env python3
"""
Comprehensive Integration Tests for Trading System
Tests integration between all connected modules
"""

import unittest
import asyncio
import sys
import os
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all components
from strategies.base_strategy import MarketData, TradingSignal, StrategyType
from strategies.trend_following import TrendFollowingStrategy
from money_management.base_strategy import AccountInfo, TradeParameters, MoneyManagementType
from money_management.percent_risk import PercentRiskStrategy
from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import PromptBuilder
from account_management.account_manager import AccountManager
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager


class TestStrategyMoneyManagementIntegration(unittest.TestCase):
    """Test integration between strategies and money management"""
    
    def setUp(self):
        """Set up test data"""
        self.strategy = TrendFollowingStrategy({'magic_number': 12345})
        self.money_management = PercentRiskStrategy({'risk_percent': 2.0})
        
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
            ],
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001,
            pip_value=10.0
        )
        
        self.account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
    
    def test_strategy_money_management_prompt_integration(self):
        """Test integration of strategy and money management prompts"""
        prompt_builder = PromptBuilder()
        trade_history = [
            {'profit': 100.0, 'symbol': 'EURUSD', 'close_time': datetime.now()}
        ]
        
        comprehensive_prompt = prompt_builder.build_trading_prompt(
            strategy=self.strategy,
            money_management=self.money_management,
            market_data=self.market_data,
            trade_history=trade_history,
            account_info=self.account_info
        )
        
        # Verify both strategy and money management content is included
        self.assertIn("TREND FOLLOWING", comprehensive_prompt)
        self.assertIn("PERCENT RISK", comprehensive_prompt)
        self.assertIn("EURUSD", comprehensive_prompt)
        self.assertIn("12345", comprehensive_prompt)  # Magic number
        self.assertIn("2.0%", comprehensive_prompt)  # Risk percentage
        self.assertIn("JSON", comprehensive_prompt)  # Response format
    
    def test_position_size_calculation_integration(self):
        """Test position size calculation with market data"""
        market_data_dict = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        trade_params = self.money_management.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,  # 50 pips
            trade_history=[],
            market_data=market_data_dict
        )
        
        # Verify calculated parameters are reasonable
        self.assertIsInstance(trade_params, TradeParameters)
        self.assertGreater(trade_params.volume, 0)
        self.assertLessEqual(trade_params.volume, 100.0)  # Within max volume
        self.assertGreaterEqual(trade_params.volume, 0.01)  # Above min volume
        self.assertEqual(trade_params.stop_loss, 1.0950)
        
        # Risk should be approximately 2% of balance
        expected_risk = 10000.0 * 0.02  # $200
        self.assertLessEqual(trade_params.risk_amount, expected_risk * 1.1)  # Allow 10% variance


class TestAIIntegration(unittest.TestCase):
    """Test AI integration components"""
    
    def setUp(self):
        """Set up test data"""
        self.prompt_builder = PromptBuilder()
        
    @patch('aiohttp.ClientSession.post')
    async def test_qwen_client_response_parsing(self, mock_post):
        """Test QwenClient response parsing"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            'choices': [{
                'message': {
                    'content': '''
                    Based on the analysis, here is my trading recommendation:
                    
                    {
                        "action": "BUY",
                        "confidence": 0.85,
                        "entry_price": 1.1000,
                        "stop_loss": 1.0950,
                        "take_profit": 1.1150,
                        "reasoning": "Strong uptrend with good risk-reward ratio",
                        "risk_level": "MEDIUM"
                    }
                    '''
                }
            }]
        })
        
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async with QwenClient() as client:
            result = await client.generate_trading_decision("Test prompt")
            
            self.assertEqual(result['action'], 'BUY')
            self.assertEqual(result['confidence'], 0.85)
            self.assertEqual(result['entry_price'], 1.1000)
            self.assertEqual(result['stop_loss'], 1.0950)
            self.assertEqual(result['take_profit'], 1.1150)
            self.assertEqual(result['risk_level'], 'MEDIUM')
    
    @patch('aiohttp.ClientSession.post')
    async def test_qwen_client_error_handling(self, mock_post):
        """Test QwenClient error handling"""
        # Mock API error response
        mock_response = Mock()
        mock_response.status = 500
        mock_response.text = AsyncMock(return_value="Internal Server Error")
        
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async with QwenClient() as client:
            result = await client.generate_trading_decision("Test prompt")
            
            self.assertEqual(result['action'], 'HOLD')
            self.assertEqual(result['confidence'], 0.0)
            self.assertIn('error', result)
    
    def test_prompt_builder_market_data_section(self):
        """Test prompt builder market data section"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
            ],
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        market_section = self.prompt_builder._build_market_data_section(market_data)
        
        self.assertIn("EURUSD", market_section)
        self.assertIn("H1", market_section)
        self.assertIn("1.1010", market_section)
        self.assertIn("1.5", market_section)
        self.assertIn("OHLCV", market_section)


class TestAccountManagementIntegration(unittest.TestCase):
    """Test account management integration"""
    
    def setUp(self):
        """Set up test data"""
        # Create temporary config file
        self.test_config = {
            "accounts": [
                {
                    "account_id": "test_account_1",
                    "account_number": 12345,
                    "server": "RoboForex-ECN",
                    "username": "test_user",
                    "password": "test_pass",
                    "strategy_type": "trend_following",
                    "money_management_type": "percent_risk",
                    "symbols": [
                        {"symbol": "EURUSD", "timeframe": "H1"}
                    ],
                    "money_management_config": {
                        "risk_percent": 2.0
                    },
                    "max_daily_trades": 5,
                    "trading_enabled": True
                }
            ]
        }
    
    @patch('builtins.open')
    @patch('os.path.exists')
    def test_account_manager_load_accounts(self, mock_exists, mock_open):
        """Test account manager loading accounts"""
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = json.dumps(self.test_config)
        
        account_manager = AccountManager()
        result = account_manager.load_accounts()
        
        self.assertTrue(result)
        self.assertEqual(len(account_manager.accounts), 1)
        
        account = account_manager.accounts[0]
        self.assertEqual(account.account_id, "test_account_1")
        self.assertEqual(account.strategy_type, "trend_following")
        self.assertEqual(account.money_management_type, "percent_risk")
    
    @patch('builtins.open')
    @patch('os.path.exists')
    def test_account_manager_get_account_by_id(self, mock_exists, mock_open):
        """Test getting account by ID"""
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = json.dumps(self.test_config)
        
        account_manager = AccountManager()
        account_manager.load_accounts()
        
        account = account_manager.get_account_by_id("test_account_1")
        self.assertIsNotNone(account)
        self.assertEqual(account.account_id, "test_account_1")
        
        # Test non-existent account
        non_existent = account_manager.get_account_by_id("non_existent")
        self.assertIsNone(non_existent)


class TestSignalGenerationIntegration(unittest.TestCase):
    """Test signal generation integration"""
    
    def setUp(self):
        """Set up test data"""
        self.account_manager = Mock()
        self.account_manager.get_all_accounts.return_value = [
            Mock(
                account_id="test_account",
                strategy_type="trend_following",
                money_management_type="percent_risk",
                symbols=[{"symbol": "EURUSD", "timeframe": "H1"}],
                money_management_config={"risk_percent": 2.0},
                trading_enabled=True
            )
        ]
    
    @patch('signal_generation.signal_generator.MT5Client')
    def test_signal_generator_initialization(self, mock_mt5_client):
        """Test signal generator initialization"""
        mock_mt5_client.return_value.initialize.return_value = True
        
        signal_generator = SignalGenerator(self.account_manager)
        
        self.assertEqual(signal_generator.account_manager, self.account_manager)
        self.assertIsNotNone(signal_generator.mt5_client)
        self.assertFalse(signal_generator.running)
    
    @patch('signal_generation.signal_generator.MT5Client')
    @patch('signal_generation.signal_generator.QwenClient')
    async def test_signal_generation_process(self, mock_qwen_client, mock_mt5_client):
        """Test signal generation process"""
        # Mock MT5 client
        mock_mt5_instance = Mock()
        mock_mt5_instance.initialize.return_value = True
        mock_mt5_instance.get_market_data.return_value = {
            'symbol': 'EURUSD',
            'timeframe': 'H1',
            'candles': [
                {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
            ],
            'current_price': 1.1010,
            'spread': 1.5,
            'volume': 1000,
            'volatility': 0.0015,
            'pip_size': 0.0001,
            'pip_value': 10.0
        }
        mock_mt5_client.return_value = mock_mt5_instance
        
        # Mock Qwen client
        mock_qwen_instance = Mock()
        mock_qwen_instance.generate_trading_decision = AsyncMock(return_value={
            'action': 'BUY',
            'confidence': 0.85,
            'entry_price': 1.1000,
            'stop_loss': 1.0950,
            'take_profit': 1.1150,
            'reasoning': 'Strong uptrend',
            'risk_level': 'MEDIUM'
        })
        mock_qwen_client.return_value.__aenter__.return_value = mock_qwen_instance
        
        signal_generator = SignalGenerator(self.account_manager)
        
        # Test signal generation for a specific symbol
        await signal_generator._generate_signal_for_symbol(
            symbol="EURUSD",
            timeframe="H1",
            strategy=Mock(strategy_type=Mock(value="trend_following"), magic_number=12345),
            money_management=Mock(strategy_type=Mock(value="percent_risk")),
            accounts=[Mock(account_id="test_account")],
            qwen_client=mock_qwen_instance
        )
        
        # Verify MT5 client was called
        mock_mt5_instance.get_market_data.assert_called_with("EURUSD", "H1", 200)
        
        # Verify AI client was called
        mock_qwen_instance.generate_trading_decision.assert_called_once()


class TestTradeManagementIntegration(unittest.TestCase):
    """Test trade management integration"""
    
    def setUp(self):
        """Set up test data"""
        self.account_manager = Mock()
        self.account_manager.get_all_accounts.return_value = [
            Mock(
                account_id="test_account",
                account_number=12345,
                strategy_type="trend_following",
                money_management_type="percent_risk",
                symbols=[{"symbol": "EURUSD", "timeframe": "H1"}],
                money_management_config={"risk_percent": 2.0},
                trading_enabled=True
            )
        ]
    
    def test_trade_manager_initialization(self):
        """Test trade manager initialization"""
        trade_manager = TradeManager(self.account_manager)
        
        self.assertEqual(trade_manager.account_manager, self.account_manager)
        self.assertFalse(trade_manager.running)


if __name__ == '__main__':
    # Run tests with detailed output
    unittest.main(verbosity=2)
