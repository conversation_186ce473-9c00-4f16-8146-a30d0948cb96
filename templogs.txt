client_session: <aiohttp.client.ClientSession object at 0x0000024823CCA870>
Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x00000248258648F0>, 8283900.89)]']
connector: <aiohttp.connector.TCPConnector object at 0x000002482558A0F0>
2025-08-20 02:24:00 | INFO     | __main__:_verify_system_health:213 - ✅ AI client connectivity verified
2025-08-20 02:24:00 | INFO     | mt5_integration.mt5_client:initialize:30 - 🔍 INIT_DEBUG: Starting MT5 initialization with path: C:\Program Files\fulltrade\terminal64.exe
2025-08-20 02:24:00 | INFO     | mt5_integration.mt5_client:initialize:39 - 🔍 INIT_DEBUG: MT5 executable found at: C:\Program Files\fulltrade\terminal64.exe
2025-08-20 02:24:00 | INFO     | mt5_integration.mt5_client:initialize:63 - 🔍 INIT_DEBUG: Calling mt5.initialize()...
2025-08-20 02:24:00 | INFO     | mt5_integration.mt5_client:initialize:88 - ✅ INIT_DEBUG: MT5 initialized successfully
2025-08-20 02:24:00 | INFO     | mt5_integration.mt5_client:initialize:89 - 🔍 INIT_DEBUG: Terminal info - Connected: True, Build: 5200, Company: MetaQuotes Ltd.
2025-08-20 02:24:01 | INFO     | mt5_integration.mt5_client:initialize:90 - 🔍 INIT_DEBUG: Terminal path: C:\Program Files\fulltrade, Data path: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\B7135B64B90300D559787E3975CB3886
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:227 - ✅ MT5 connectivity verified
2025-08-20 02:24:01 | INFO     | mt5_integration.mt5_client:shutdown:107 - MT5 connection closed
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:240 - 📊 System Health: 5/6 components healthy
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:243 -    ⚠️ components_initialized: WARNING
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:243 -    ✅ accounts_loaded: OK
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:243 -    ✅ mt5_connected: OK
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:243 -    ✅ ai_client_ready: OK
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:243 -    ✅ cache_manager_ready: OK
2025-08-20 02:24:01 | INFO     | __main__:_verify_system_health:243 -    ✅ error_recovery_ready: OK
2025-08-20 02:24:01 | INFO     | __main__:initialize:165 - 🚀 Step 5/5: Final System Readiness Check...
2025-08-20 02:24:01 | INFO     | __main__:initialize:167 -    📊 Cache Manager: 0 entries, 0.00MB used
2025-08-20 02:24:01 | INFO     | __main__:initialize:171 - ✅ Enhanced trading system ready for operation
2025-08-20 02:24:01 | INFO     | __main__:initialize:172 - ================================================================================
2025-08-20 02:24:01 | INFO     | __main__:start:270 - 🎯 Starting Trading Operations...
2025-08-20 02:24:01 | INFO     | __main__:start:271 - ================================================================================
2025-08-20 02:24:01 | INFO     | __main__:start:272 - 🚀 System Status: ACTIVE
2025-08-20 02:24:01 | INFO     | __main__:start:273 - 📊 Monitoring both signal generation and trade management
2025-08-20 02:24:01 | INFO     | __main__:start:274 - ⏰ Press Ctrl+C to stop the system gracefully
2025-08-20 02:24:01 | INFO     | __main__:start:275 - ================================================================================
2025-08-20 02:24:01 | INFO     | __main__:run_signal_generation:369 - 🔄 Signal generation started
2025-08-20 02:24:01 | INFO     | scheduling.scheduler_coordinator:start_signal_generation:120 - 🚀 SCHEDULER: Signal generation started at 02:24:01
2025-08-20 02:24:01 | INFO     | __main__:run_signal_generation:378 - 📡 Generating trading signals...
2025-08-20 02:24:01 | INFO     | signal_generation.signal_generator:_complete_risk_manager_initialization:105 - 🔧 Completing unified risk manager initialization...
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:101 - ✅ Risk limits initialized for account Mine
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:102 -    Max daily trades: 3
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:103 -    Max open positions: 1
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:104 -    Max daily loss: $5.0
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:105 -    Max drawdown: 15.0%
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:101 - ✅ Risk limits initialized for account Customer 1
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:102 -    Max daily trades: 3
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:103 -    Max open positions: 1
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:104 -    Max daily loss: $5.0
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:105 -    Max drawdown: 15.0%
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:101 - ✅ Risk limits initialized for account fixed_volume_scalper
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:102 -    Max daily trades: 10
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:103 -    Max open positions: 2
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:104 -    Max daily loss: $2.0
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:105 -    Max drawdown: 10.0%
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:101 - ✅ Risk limits initialized for account martingale_trader
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:102 -    Max daily trades: 5
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:103 -    Max open positions: 1
2025-08-20 02:24:01 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:104 -    Max daily loss: $3.0
2025-08-20 02:24:02 | INFO     | risk_management.unified_risk_manager:initialize_account_limits:105 -    Max drawdown: 12.0%
2025-08-20 02:24:02 | INFO     | signal_generation.signal_generator:_complete_risk_manager_initialization:111 - ✅ Unified risk manager fully initialized for all accounts
2025-08-20 02:24:02 | INFO     | signal_generation.signal_generator:_is_market_open:1577 - Weekend trading enabled - allowing signal generation
2025-08-20 02:24:02 | INFO     | signal_generation.signal_generator:generate_signals:176 - Starting signal generation cycle...
2025-08-20 02:24:02 | INFO     | signal_generation.signal_generator:generate_signals:183 - No account groups found, creating virtual groups from individual accounts
2025-08-20 02:24:02 | INFO     | signal_generation.signal_generator:_create_virtual_groups:247 - Created 3 virtual groups from individual accounts
2025-08-20 02:24:02 | INFO     | __main__:run_trade_management:407 - 🔄 Trade management started
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002482558A0F0>
Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x00000248258648F0>, 8283904.359)]']
connector: <aiohttp.connector.TCPConnector object at 0x000002482582E6C0>
2025-08-20 02:24:03 | INFO     | signal_generation.signal_generator:generate_signals:193 - 🤖 MT5Client updated with AI error handling capabilities
2025-08-20 02:24:03 | INFO     | signal_generation.signal_generator:_process_account_group:257 - Processing group: trend_following/percent_risk with 2 accounts
2025-08-20 02:24:03 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:400 - Processing account Mine with individual settings: {'_comment': 'OPTIMIZED FOR $80 ACCOUNT - Conservative settings for profitability', 'risk_percent': 0.5, 'max_daily_loss_percent': 2.0, 'max_daily_trades': 3, 'max_open_positions': 1, 'max_pending_orders': 2, 'max_volume_per_trade': 0.01, 'force_min_volume': False, 'max_daily_loss': 5.0, 'max_drawdown_percent': 15.0, '_risk_controls': {'max_risk_per_trade_percent': 1.0, 'max_risk_multiplier': 1.5, 'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}, 'max_consecutive_losses': 5, 'max_risk_per_trade': 15.0, 'max_risk_multiplier': 5.0}
2025-08-20 02:24:03 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:408 - ✅ SYMBOL_DIRECT: Using account-defined symbol EURUSD for account Mine (no normalization)
2025-08-20 02:24:03 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD_M15_trend_following_percent_risk_Mine
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:login:113 - 🔍 LOGIN_DEBUG: Starting login process for account Mine (********)
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:login:118 - 🔍 LOGIN_DEBUG: MT5 not connected, initializing...
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:initialize:30 - 🔍 INIT_DEBUG: Starting MT5 initialization with path: C:\Program Files\fulltrade\terminal64.exe
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:initialize:39 - 🔍 INIT_DEBUG: MT5 executable found at: C:\Program Files\fulltrade\terminal64.exe
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:initialize:63 - 🔍 INIT_DEBUG: Calling mt5.initialize()...
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:initialize:88 - ✅ INIT_DEBUG: MT5 initialized successfully
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:initialize:89 - 🔍 INIT_DEBUG: Terminal info - Connected: True, Build: 5200, Company: MetaQuotes Ltd.
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:initialize:90 - 🔍 INIT_DEBUG: Terminal path: C:\Program Files\fulltrade, Data path: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\B7135B64B90300D559787E3975CB3886
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:login:129 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:login:141 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-20 02:24:03 | INFO     | mt5_integration.mt5_client:login:144 - 🔍 LOGIN_DEBUG: Attempting login with server: RoboForex-ECN
2025-08-20 02:24:07 | INFO     | mt5_integration.mt5_client:login:182 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-20 02:24:07 | INFO     | mt5_integration.mt5_client:login:183 - 🔍 LOGIN_DEBUG: Account details - Balance: 80.37, Equity: 80.4, Currency: USD
2025-08-20 02:24:07 | INFO     | mt5_integration.mt5_client:login:184 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-20 02:24:07 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Mine | Status: LOGIN_SUCCESS
2025-08-20 02:24:07 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:467 - ✅ MARKET_DATA_OPTIMIZED: Getting market data for account Mine: EURUSD on RoboForex-ECN (using batched retrieval)
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=market_data | key=10ed4843b37c823b
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_request_EURUSD_M15_duration=0.031 seconds
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=market_data | size_bytes=24041 | ttl_seconds=60
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_fetched=1 | timeframe=M15
2025-08-20 02:24:07 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: Mine | Symbol: EURUSD | Timeframe: M15 | Candles: 200 | Time: 0.141s
2025-08-20 02:24:07 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Mine | Status: BALANCE_UPDATE | Balance: $80.37 | Equity: $80.40 | Margin Level: 6931.0%
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=46e8937ea5a2d3e4
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=6609b658a4dee172
2025-08-20 02:24:07 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_miss=1 | strategy=trend_following | timeframe=M15
2025-08-20 02:24:26 | INFO     | ai_integration.response_sanitizer:sanitize_response:77 - 🧹 Sanitized AI response - rem oved control characters and fixed formatting
2025-08-20 02:24:26 | WARNING  | ai_integration.response_sanitizer:extract_json_from_text:172 - 🚀 JSON parsing failed, attempting manual field extraction
2025-08-20 02:24:26 | WARNING  | ai_integration.response_sanitizer:_extract_fields_manually:220 - 🔧 Successfully extracted trading decision using manual field extraction
2025-08-20 02:24:26 | INFO     | ai_integration.response_sanitizer:_extract_fields_manually:221 - Extracted fields: action=SELL, confidence=0.65
2025-08-20 02:24:26 | INFO     | ai_integration.openrouter_client:_parse_ai_response:252 - ✅ Successfully parsed AI trading decision: SELL (confidence: 65.00%)
2025-08-20 02:24:26 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_skip_low_confidence=1 | confidence=0.65 | threshold=0.7
2025-08-20 02:24:26 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_request_processed=1 | processing_time=18.************** | prompt_length=16323
2025-08-20 02:24:26 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: Mine | Symbol: EURUSD | Strategy: trend_following | Prompt Length: 16323 chars | Processing Time: 18.62s | Action: SELL | Confidence: 0.65
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:603 - 🚀 SIGNAL_PROCESSING: Processing SELL signal for Mine with confidence 0.65
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:637 - AI Signal for Mine: {'action': 'SELL', 'confidence': 0.65, 'reasoning': 'Clear bearish momentum developing with recent strong bearish candle and increased volume. Price showing rejection from resistance level around 1.16450. Multiple timeframe analysis shows weakening bullish momentum and potential trend reversal. Risk-reward ratio is favorable at 1:2.', 'risk_level': 'MEDIUM', 'entry_price': 1.16383, 'stop_loss': 1.16467, 'take_profit': 1.16215, 'take_profit_levels': None, 'market_analysis': 'Unable to parse from response', 'strategy_alignment': 'Unable to parse from response', 'risk_assessment': 'Unable to parse from response', 'additional_signals': [], 'timestamp': '2025-08-20T02:24:26.450506', 'model': 'anthropic/claude-3.5-sonnet', 'provider': 'openrouter', 'raw_response': '{\n    "action": "SELL",\n    "confidence": 0.65,\n    "entry_price": 1.16383,\n    "stop_loss": 1.16467,\n    "take_profit": 1.16215,\n    "take_profit_levels": null,\n    "reasoning": "Clear bearish momentum developing with recent strong bearish candle and increased volume. Price showing rejection from resistance level around 1.16450. Multiple timeframe analysis shows weakening bullish momentum and potential trend reversal. Risk-reward ratio is favorable at 1:2.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "Market showing signs of bearish reversal with increased volume and momentum. Recent price action shows rejection from resistance with strong bearish candle formation. Volume spike on bearish moves indicates seller dominance.",\n    "strategy_alignment": "Trade aligns with trend following strategy as we\'re catching potential trend reversal with strong momentum. Entry timing is good with clear resistance rejection and volume confirmation.",\n    "risk_assessment": "Stop loss placed above recent swing high at 1.16467 (84 pips). Given account size of $80.37 and 0.5% risk per trade, position size will be automatically calculated. Single take profit target used due to small account size for better risk control. Risk-reward ratio is 1:2.",\n    "additional_signals": []\n}\n\nDetailed Analysis:\n1. Strong bearish momentum developing with volume confirmation\n2. Clear resistance rejection around 1.16450\n3. Stop loss placed above recent swing high with buffer for spread\n4. Take profit target at next major support level\n5. Risk-reward ratio 1:2 (84 pips risk : 168 pips reward)\n6. Confidence 65% based on:\n   - Clear resistance rejection\n   - Volume confirmation\n   - Strong bearish momentum\n   - Clean market structure\n   - Multiple timeframe alignment\n\nMoney Management Considerations:\n- Single TP strategy chosen due to small account size\n- Stop loss placement allows for proper position sizing\n- Risk-reward ratio meets minimum 1:2 requirement\n- Trade size will be automatically calculated based on 0.5% risk', 'usage': {'prompt_tokens': 6393, 'completion_tokens': 502, 'total_tokens': 6895}}
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:661 - 🔍 SIGNAL_VALIDATION: Validating SELL signal for Mine
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:662 - 🔍 SIGNAL_DETAILS: Entry: 1.16383, SL: 1.16467, TP: 1.16215
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:663 - 🔍 SIGNAL_CONFIDENCE: 0.65 (required: varies by strategy)
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:666 - Validating signal for Mine: action=SELL, confidence=0.65, entry=1.16383, sl=1.16467, tp=1.16215
2025-08-20 02:24:26 | INFO     | strategies.trend_following:validate_signal:179 - Trend following risk: 8.4 pips
2025-08-20 02:24:26 | INFO     | strategies.trend_following:validate_signal:197 - Trend following risk-reward ratio: 2.00
2025-08-20 02:24:26 | INFO     | strategies.trend_following:validate_signal:208 - Trend following reward: 16.8 pips
2025-08-20 02:24:26 | INFO     | strategies.trend_following:validate_signal:221 - Trend following signal validation PASSED
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:701 - ✅ SIGNAL_VALIDATION_PASSED: Signal approved for account Mine
2025-08-20 02:24:26 | INFO     | signal_generation.signal_generator:_process_signal_for_account:744 - Generated signal for account Mine: SELL EURUSD volume=0.01 with account-specific settings
2025-08-20 02:24:26 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Mine | Status: BALANCE_UPDATE | Balance: $80.37 | Equity: $80.40 | Margin Level: 6931.0%
2025-08-20 02:24:27 | INFO     | signal_generation.signal_generator:_execute_trade_signal:857 - 🚀 EXECUTING TRADE: Mine SELL EURUSD volume=0.01 entry=1.16383 sl=1.16467 tp=1.16215
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:399 - 🔍 PLACE_ORDER_DEBUG: Starting order placement with params: {'symbol': 'EURUSD', 'action': 'SELL', 'volume': 0.01, 'price': 1.16383, 'stop_loss': 1.16467, 'take_profit': 1.16215, 'magic_number': 0, 'comment': 'trend_following_percent_risk'}
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:408 - 🔍 PLACE_ORDER_DEBUG: Current account: Mine (********)
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:423 - 🔍 PLACE_ORDER_DEBUG: Terminal connected: True, Trade allowed: True
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:432 - 🔍 PLACE_ORDER_DEBUG: Account info - Login: ********, Trade allowed: True, Trade expert: True
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:441 - 🔍 PLACE_ORDER_DEBUG: Symbol EURUSD - Visible: True, Trade mode: 4
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:458 - 🔍 PLACE_ORDER_DEBUG: Tick data - Bid: 1.****************, Ask: 1.16387, Spread: 9.999999999843467e-06
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:493 - 🔍 PLACE_ORDER_DEBUG: SELL order - Entry: 1.16383, Current Bid: 1.****************, Diff: 3.0000000000196536e-05
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:497 - 🔍 PLACE_ORDER_DEBUG: Price difference small (3.0000000000196536e-05), using current bid: 1.****************
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:512 - 🔍 PLACE_ORDER_DEBUG: Order type determined: 1, Final price: 1.****************
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_validate_price_for_symbol:1747 - 🔍 PRICE_VALIDATE: EURUSD - Original: 1.****************, Final: 1.16386
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:528 - 🔍 PLACE_ORDER_DEBUG: Price normalized from 1.**************** to 1.16386
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:566 - 🔍 PLACE_ORDER_DEBUG: Stops level is 0, using enhanced default: 25 for EURUSD
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:580 - 🔍 PLACE_ORDER_DEBUG: Symbol EURUSD - Stops level: 25, Point: 1e-05, Base min distance: 0.000250, Final min distance: 0.000300
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:606 - 🔍 PLACE_ORDER_DEBUG: Stop loss validation passed. Final SL: 1.16467, Distance: 0.000810
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:632 - 🔍 PLACE_ORDER_DEBUG: Take profit validation passed. Final TP: 1.16215, Distance: 0.001710
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1794 - 🔍 DEMO_CHECK: Account type - Demo: True, Trade mode: 0
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1797 - 🔍 DEMO_CHECK: Demo account detected - checking limitations...
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1810 - 🔍 DEMO_CHECK: Symbol EURUSD - Min volume: 0.01, Max volume: 500.0
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1811 - 🔍 DEMO_CHECK: Symbol EURUSD - Trade mode: 4, Calc mode: N/A
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1824 - 🔍 DEMO_CHECK: Server: RoboForex-ECN, Company: RoboForex Ltd
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1828 - 🔍 DEMO_CHECK: RoboForex demo account detected
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1829 - 🔍 DEMO_CHECK: RoboForex demo limitations:
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1830 -   - Some symbols may have restricted trading hours
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1831 -   - Pending orders may have minimum distance requirements
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1832 -   - Some EAs may be restricted
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:654 - 🔍 PLACE_ORDER_DEBUG: Preparing MARKET order
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:670 - 🔍 PLACE_ORDER_DEBUG: Using safe comment: 'AI_TF_S_EUR' (RoboForex demo restriction)
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:678 - 🔍 PLACE_ORDER_DEBUG: Final request: {'action': 1, 'symbol': 'EURUSD', 'volume': 0.01, 'type': 1, 'price': 1.16386, 'magic': 0, 'comment': 'AI_TF_S_EUR', 'type_time': 0, 'type_filling': 1, 'sl': 1.16467, 'tp': 1.16215}
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:681 - 🔍 PLACE_ORDER_DEBUG: Performing order check...
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:834 - 🔍 PLACE_ORDER_DEBUG: Order check passed! Retcode: 0 (Success)
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:835 - 🔍 PLACE_ORDER_DEBUG: Margin required: 3.49, Free margin: 76.91
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:838 - 🔍 PLACE_ORDER_DEBUG: Sending order to MT5...
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:862 - 🔍 PLACE_ORDER_DEBUG: Order result received: OrderSendResult(retcode=10009, deal=*********, order=*********, volume=0.01, price=1.****************, bid=0.0, ask=0.0, comment='Request executed', request_id=1008427111, retcode_external=0, request=TradeRequest(action=1, magic=0, order=0, symbol='EURUSD', volume=0.01, price=1.16386, stoplimit=0.0, sl=1.16467, tp=1.16215, deviation=0, type=1, type_filling=1, type_time=0, expiration=0, comment='AI_TF_S_EUR', position=0, position_by=0))
2025-08-20 02:24:27 | INFO     | mt5_integration.mt5_client:place_order:883 - ✅ PLACE_ORDER_DEBUG: Order placed successfully! Order ID: *********, Deal ID: *********
2025-08-20 02:24:27 | INFO     | logging_system.logger:log_trade_execution:137 - EXECUTION | SUCCESS | Account: Mine | SELL 0.01 EURUSD @ 1.16386 | Order ID: *********
2025-08-20 02:24:27 | INFO     | signal_generation.signal_generator:_execute_trade_signal:873 - ✅ TRADE EXECUTED: Mine SELL EURUSD order_id=********* volume=0.01
2025-08-20 02:24:27 | INFO     | risk_management.unified_risk_manager:update_trade_executed:414 - 📊 Trade executed - Account Mine: 1 trades today
2025-08-20 02:24:27 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | TRADE_EXECUTED | Account Mine: SELL EURUSD order_id=********* volume=0.01 entry=1.16383 sl=1.16467 tp=1.16215
2025-08-20 02:24:28 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account Mine: SELL EURUSD volume=0.01 confidence=0.65
2025-08-20 02:24:28 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:400 - Processing account Customer 1 with individual settings: {'_comment': 'OPTIMIZED FOR $80 ACCOUNT - Conservative settings for profitability', 'risk_percent': 0.5, 'max_daily_loss_percent': 2.0, 'max_daily_trades': 3, 'max_open_positions': 1, 'max_pending_orders': 2, 'max_volume_per_trade': 0.01, 'force_min_volume': False, 'max_daily_loss': 5.0, 'max_drawdown_percent': 15.0, '_risk_controls': {'max_risk_per_trade_percent': 1.0, 'max_risk_multiplier': 1.5, 'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}, 'max_consecutive_losses': 5, 'max_risk_per_trade': 15.0, 'max_risk_multiplier': 5.0}
2025-08-20 02:24:28 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:408 - ✅ SYMBOL_DIRECT: Using account-defined symbol EURUSD! for account Customer 1 (no normalization)
2025-08-20 02:24:28 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD!_M15_trend_following_percent_risk_Customer 1
2025-08-20 02:24:28 | INFO     | mt5_integration.mt5_client:login:113 - 🔍 LOGIN_DEBUG: Starting login process for account Customer 1 (********)
2025-08-20 02:24:28 | INFO     | mt5_integration.mt5_client:login:129 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-20 02:24:28 | INFO     | mt5_integration.mt5_client:login:141 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-20 02:24:28 | INFO     | mt5_integration.mt5_client:login:144 - 🔍 LOGIN_DEBUG: Attempting login with server: CapitalxtendLLC-MU
2025-08-20 02:24:32 | INFO     | mt5_integration.mt5_client:login:182 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-20 02:24:32 | INFO     | mt5_integration.mt5_client:login:183 - 🔍 LOGIN_DEBUG: Account details - Balance: 87.14, Equity: 87.23, Currency: USD
2025-08-20 02:24:32 | INFO     | mt5_integration.mt5_client:login:184 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-20 02:24:32 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Customer 1 | Status: LOGIN_SUCCESS
2025-08-20 02:24:33 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:467 - ✅ MARKET_DATA_OPTIMIZED: Getting market data for account Customer 1: EURUSD! on CapitalxtendLLC-MU (using batched retrieval)
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=market_data | key=9add6fcd3118c171
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_request_EURUSD!_M15_duration=0.016 seconds
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=market_data | size_bytes=23960 | ttl_seconds=60
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_fetched=1 | timeframe=M15
2025-08-20 02:24:33 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: Customer 1 | Symbol: EURUSD! | Timeframe: M15 | Candles: 200 | Time: 0.125s
2025-08-20 02:24:33 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Customer 1 | Status: BALANCE_UPDATE | Balance: $87.14 | Equity: $87.23 | Margin Level: 3743.8%
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=7c438802b33333fd
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=7374733a6f81acf1
2025-08-20 02:24:33 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_miss=1 | strategy=trend_following | timeframe=M15
2025-08-20 02:24:45 | INFO     | ai_integration.response_sanitizer:sanitize_response:77 - 🧹 Sanitized AI response - rem oved control characters and fixed formatting
2025-08-20 02:24:45 | WARNING  | ai_integration.response_sanitizer:extract_json_from_text:172 - 🔧 JSON parsing failed, attempting manual field extraction
2025-08-20 02:24:45 | WARNING  | ai_integration.response_sanitizer:_extract_fields_manually:220 - 🔧 Successfully extracted trading decision using manual field extraction
2025-08-20 02:24:45 | INFO     | ai_integration.response_sanitizer:_extract_fields_manually:221 - Extracted fields: action=SELL, confidence=0.65
2025-08-20 02:24:45 | INFO     | ai_integration.openrouter_client:_parse_ai_response:252 - ✅ Successfully parsed AI trading decision: SELL (confidence: 65.00%)
2025-08-20 02:24:45 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_skip_low_confidence=1 | confidence=0.65 | threshold=0.7
2025-08-20 02:24:45 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_request_processed=1 | processing_time=12.*************** | prompt_length=16260
2025-08-20 02:24:45 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: Customer 1 | Symbol: EURUSD! | Strategy: trend_following | Prompt Length: 16260 chars | Processing Time: 12.60s | Action: SELL | Confidence: 0.65
2025-08-20 02:24:45 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:603 - 🚀 SIGNAL_PROCESSING: Processing SELL signal for Customer 1 with confidence 0.65
2025-08-20 02:24:45 | INFO     | signal_generation.signal_generator:_process_signal_for_account:637 - AI Signal for Customer 1: {'action': 'SELL', 'confidence': 0.65, 'reasoning': 'Clear bearish momentum on M15 with recent strong bearish candle breaking support. Price showing lower highs and lower lows pattern. Volume increasing on bearish moves indicating seller dominance. RSI showing downward momentum and room to move lower.', 'risk_level': 'MEDIUM', 'entry_price': 1.16393, 'stop_loss': 1.16461, 'take_profit': 1.16257, 'take_profit_levels': None, 'market_analysis': 'Unable to parse from response', 'strategy_alignment': 'Unable to parse from response', 'risk_assessment': 'Unable to parse from response', 'additional_signals': [], 'timestamp': '2025-08-20T02:24:45.794608', 'model': 'anthropic/claude-3.5-sonnet', 'provider': 'openrouter', 'raw_response': '{\n    "action": "SELL",\n    "confidence": 0.65,\n    "entry_price": 1.16393,\n    "stop_loss": 1.16461,\n    "take_profit": 1.16257,\n    "take_profit_levels": null,\n    "reasoning": "Clear bearish momentum on M15 with recent strong bearish candle breaking support. Price showing lower highs and lower lows pattern. Volume increasing on bearish moves indicating seller dominance. RSI showing downward momentum and room to move lower.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "Market is in a short-term downtrend with increasing bearish momentum. Recent price action shows rejection at 1.16460 resistance level with strong volume. Volatility has increased supporting potential continuation of move.",\n    "strategy_alignment": "Trade aligns with trend following strategy as we\'re entering after confirmation of trend continuation. Entry comes after pullback to resistance with clear stop placement above recent swing high.",\n    "risk_assessment": "Risk-reward ratio is approximately 1:2 (68 pips risk for 136 pips reward). Stop loss placed above recent swing high at 1.16461 to protect against false breakout. Position size will be calculated based on 0.5% account risk. Single take profit chosen due to small account size.",\n    "additional_signals": []\n}\n\nDetailed Analysis:\n1. Trend Analysis: Clear short-term downtrend with lower highs and lower lows\n2. Entry Trigger: Strong bearish momentum with volume confirmation\n3. Stop Loss: Placed above recent swing high at 1.16461 (68 pips)\n4. Take Profit: Set at 1.16257 based on previous support level (136 pips)\n5. Risk Management: 1:2 risk-reward ratio, 0.5% account risk\n6. Confidence: 65% based on clear trend and multiple confirmations\n7. Volume: Increasing on bearish moves supporting direction\n8. Market Structure: Bearish continuation pattern with resistance rejection\n\nThe setup meets all key strategy requirements:\n- Clear trend direction\n- Good risk-reward ratio (1:2)\n- Technical stop loss placement\n- Volume confirmation\n- Multiple timeframe alignment\n- Conservative take profit target', 'usage': {'prompt_tokens': 6387, 'completion_tokens': 532, 'total_tokens': 6919}}
2025-08-20 02:24:45 | INFO     | signal_generation.signal_generator:_process_signal_for_account:661 - 🔍 SIGNAL_VALIDATION: Validating SELL signal for Customer 1
2025-08-20 02:24:45 | INFO     | signal_generation.signal_generator:_process_signal_for_account:662 - 🔍 SIGNAL_DETAILS: Entry: 1.16393, SL: 1.16461, TP: 1.16257
2025-08-20 02:24:46 | INFO     | signal_generation.signal_generator:_process_signal_for_account:663 - 🔍 SIGNAL_CONFIDENCE: 0.65 (required: varies by strategy)
2025-08-20 02:24:46 | INFO     | signal_generation.signal_generator:_process_signal_for_account:666 - Validating signal for Customer 1: action=SELL, confidence=0.65, entry=1.16393, sl=1.16461, tp=1.16257
2025-08-20 02:24:46 | INFO     | strategies.trend_following:validate_signal:179 - Trend following risk: 6.8 pips
2025-08-20 02:24:46 | INFO     | strategies.trend_following:validate_signal:197 - Trend following risk-reward ratio: 2.00
2025-08-20 02:24:46 | INFO     | strategies.trend_following:validate_signal:208 - Trend following reward: 13.6 pips
2025-08-20 02:24:46 | INFO     | strategies.trend_following:validate_signal:221 - Trend following signal validation PASSED
2025-08-20 02:24:46 | INFO     | signal_generation.signal_generator:_process_signal_for_account:701 - ✅ SIGNAL_VALIDATION_PASSED: Signal approved for account Customer 1
2025-08-20 02:24:46 | INFO     | signal_generation.signal_generator:_process_signal_for_account:744 - Generated signal for account Customer 1: SELL EURUSD! volume=0.01 with account-specific settings
2025-08-20 02:24:46 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Customer 1 | Status: BALANCE_UPDATE | Balance: $87.14 | Equity: $87.29 | Margin Level: 3746.4%
2025-08-20 02:24:46 | INFO     | signal_generation.signal_generator:_execute_trade_signal:857 - 🚀 EXECUTING TRADE: Customer 1 SELL EURUSD! volume=0.01 entry=1.16393 sl=1.16461 tp=1.16257
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:399 - 🔍 PLACE_ORDER_DEBUG: Starting order placement with params: {'symbol': 'EURUSD!', 'action': 'SELL', 'volume': 0.01, 'price': 1.16393, 'stop_loss': 1.16461, 'take_profit': 1.16257, 'magic_number': 0, 'comment': 'trend_following_percent_risk'}
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:408 - 🔍 PLACE_ORDER_DEBUG: Current account: Customer 1 (********)
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:423 - 🔍 PLACE_ORDER_DEBUG: Terminal connected: True, Trade allowed: True
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:432 - 🔍 PLACE_ORDER_DEBUG: Account info - Login: ********, Trade allowed: True, Trade expert: True
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:441 - 🔍 PLACE_ORDER_DEBUG: Symbol EURUSD! - Visible: True, Trade mode: 4
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:458 - 🔍 PLACE_ORDER_DEBUG: Tick data - Bid: 1.16376, Ask: 1.1639, Spread: 0.000140000000000029
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:493 - 🔍 PLACE_ORDER_DEBUG: SELL order - Entry: 1.16393, Current Bid: 1.16376, Diff: 0.00017000000000000348
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:497 - 🔍 PLACE_ORDER_DEBUG: Price difference small (0.00017000000000000348), using current bid: 1.16376
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:512 - 🔍 PLACE_ORDER_DEBUG: Order type determined: 1, Final price: 1.16376
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:566 - 🔍 PLACE_ORDER_DEBUG: Stops level is 0, using enhanced default: 30 for EURUSD!
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:580 - 🔍 PLACE_ORDER_DEBUG: Symbol EURUSD! - Stops level: 30, Point: 1e-05, Base min distance: 0.000300, Final min distance: 0.000390
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:606 - 🔍 PLACE_ORDER_DEBUG: Stop loss validation passed. Final SL: 1.16461, Distance: 0.000850
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:632 - 🔍 PLACE_ORDER_DEBUG: Take profit validation passed. Final TP: 1.16257, Distance: 0.001190
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1794 - 🔍 DEMO_CHECK: Account type - Demo: False, Trade mode: 2
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1841 - 🔍 DEMO_CHECK: Real account detected
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:654 - 🔍 PLACE_ORDER_DEBUG: Preparing MARKET order
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:670 - 🔍 PLACE_ORDER_DEBUG: Using safe comment: 'AI_TF_S_EUR' (RoboForex demo restriction)
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:678 - 🔍 PLACE_ORDER_DEBUG: Final request: {'action': 1, 'symbol': 'EURUSD!', 'volume': 0.01, 'type': 1, 'price': 1.16376, 'magic': 0, 'comment': 'AI_TF_S_EUR', 'type_time': 0, 'type_filling': 1, 'sl': 1.16461, 'tp': 1.16257}
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:681 - 🔍 PLACE_ORDER_DEBUG: Performing order check...
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:834 - 🔍 PLACE_ORDER_DEBUG: Order check passed! Retcode: 0 (Success)
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:835 - 🔍 PLACE_ORDER_DEBUG: Margin required: 3.49, Free margin: 83.8
2025-08-20 02:24:46 | INFO     | mt5_integration.mt5_client:place_order:838 - 🔍 PLACE_ORDER_DEBUG: Sending order to MT5...
2025-08-20 02:24:47 | INFO     | mt5_integration.mt5_client:place_order:862 - 🔍 PLACE_ORDER_DEBUG: Order result received: OrderSendResult(retcode=10009, deal=********, order=********, volume=0.01, price=1.16376, bid=1.16376, ask=1.1639, comment='Request executed', request_id=1008427112, retcode_external=0, request=TradeRequest(action=1, magic=0, order=0, symbol='EURUSD!', volume=0.01, price=1.16376, stoplimit=0.0, sl=1.16461, tp=1.16257, deviation=0, type=1, type_filling=1, type_time=0, expiration=0, comment='AI_TF_S_EUR', position=0, position_by=0))
2025-08-20 02:24:47 | INFO     | mt5_integration.mt5_client:place_order:883 - ✅ PLACE_ORDER_DEBUG: Order placed successfully! Order ID: ********, Deal ID: ********
2025-08-20 02:24:47 | INFO     | logging_system.logger:log_trade_execution:137 - EXECUTION | SUCCESS | Account: Customer 1 | SELL 0.01 EURUSD! @ 1.16376 | Order ID: ********
2025-08-20 02:24:47 | INFO     | signal_generation.signal_generator:_execute_trade_signal:873 - ✅ TRADE EXECUTED: Customer 1 SELL EURUSD! order_id=******** volume=0.01
2025-08-20 02:24:47 | INFO     | risk_management.unified_risk_manager:update_trade_executed:414 - 📊 Trade executed - Account Customer 1: 1 trades today
2025-08-20 02:24:47 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | TRADE_EXECUTED | Account Customer 1: SELL EURUSD! order_id=******** volume=0.01 entry=1.16393 sl=1.16461 tp=1.16257
2025-08-20 02:24:47 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account Customer 1: SELL EURUSD! volume=0.01 confidence=0.65
2025-08-20 02:24:47 | INFO     | signal_generation.signal_generator:_process_account_group:257 - Processing group: mean_reversion/fixed_volume with 1 accounts
2025-08-20 02:24:47 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:400 - Processing account fixed_volume_scalper with individual settings: {'_comment': 'FIXED VOLUME - Conservative scalping approach', 'fixed_volume': 0.01, 'max_daily_trades': 10, 'max_daily_loss': 2.0, 'max_daily_loss_percent': 3.0, 'max_drawdown_percent': 10.0, 'max_open_positions': 2, 'max_pending_orders': 3, '_risk_controls': {'max_risk_per_trade_percent': 2.0, 'max_risk_multiplier': 1.2, 'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}, 'risk_percent': 2.0, 'max_consecutive_losses': 5, 'max_risk_per_trade': 15.0, 'max_risk_multiplier': 5.0}
2025-08-20 02:24:47 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:408 - ✅ SYMBOL_DIRECT: Using account-defined symbol EURUSD! for account fixed_volume_scalper (no normalization)
2025-08-20 02:24:47 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD!_M5_mean_reversion_fixed_volume_fixed_volume_scalper
2025-08-20 02:24:47 | INFO     | mt5_integration.mt5_client:login:113 - 🔍 LOGIN_DEBUG: Starting login process for account fixed_volume_scalper (********)
2025-08-20 02:24:47 | INFO     | mt5_integration.mt5_client:login:129 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-20 02:24:47 | INFO     | mt5_integration.mt5_client:login:141 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-20 02:24:47 | INFO     | mt5_integration.mt5_client:login:144 - 🔍 LOGIN_DEBUG: Attempting login with server: CapitalxtendLLC-MU
2025-08-20 02:24:50 | INFO     | mt5_integration.mt5_client:login:182 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-20 02:24:50 | INFO     | mt5_integration.mt5_client:login:183 - 🔍 LOGIN_DEBUG: Account details - Balance: 972.25, Equity: 972.24, Currency: USD
2025-08-20 02:24:51 | INFO     | mt5_integration.mt5_client:login:184 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-20 02:24:51 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: LOGIN_SUCCESS
2025-08-20 02:24:51 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:467 - ✅ MARKET_DATA_OPTIMIZED: Getting market data for account fixed_volume_scalper: EURUSD! on CapitalxtendLLC-MU (using batched retrieval)
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=market_data | key=266b55584117aabc
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_request_EURUSD!_M5_duration=0.016 seconds
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=market_data | size_bytes=23798 | ttl_seconds=60
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_fetched=1 | timeframe=M5
2025-08-20 02:24:51 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: fixed_volume_scalper | Symbol: EURUSD! | Timeframe: M5 | Candles: 200 | Time: 0.110s
2025-08-20 02:24:51 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: BALANCE_UPDATE | Balance: $972.25 | Equity: $972.21 | Margin Level: 83811.2%
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=a62ba9c3c2e2c69f
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=e4da741944841f27
2025-08-20 02:24:51 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_miss=1 | strategy=mean_reversion | timeframe=M5
2025-08-20 02:25:03 | INFO     | ai_integration.response_sanitizer:sanitize_response:77 - 💡 Sanitized AI response - rem oved control characters and fixed formatting
2025-08-20 02:25:03 | WARNING  | ai_integration.response_sanitizer:extract_json_from_text:172 - 🔧 JSON parsing failed, attempting manual field extraction
2025-08-20 02:25:03 | WARNING  | ai_integration.response_sanitizer:_extract_fields_manually:220 - 🔧 Successfully extracted trading decision using manual field extraction
2025-08-20 02:25:03 | INFO     | ai_integration.response_sanitizer:_extract_fields_manually:221 - Extracted fields: action=HOLD, confidence=0.8
2025-08-20 02:25:03 | INFO     | ai_integration.openrouter_client:_parse_ai_response:252 - ✅ Successfully parsed AI trading decision: HOLD (confidence: 80.00%)
2025-08-20 02:25:03 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=ai_response | size_bytes=2761 | ttl_seconds=1800
2025-08-20 02:25:03 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=ai_response | size_bytes=2761 | ttl_seconds=900
2025-08-20 02:25:03 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_response_cached=1 | strategy=mean_reversion | confidence=0.8
2025-08-20 02:25:03 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_request_processed=1 | processing_time=11.*************** | prompt_length=14412
2025-08-20 02:25:03 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: fixed_volume_scalper | Symbol: EURUSD! | Strategy: mean_reversion | Prompt Length: 14412 chars | Processing Time: 11.94s | Action: HOLD | Confidence: 0.80
2025-08-20 02:25:03 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:608 - ⏸️ SIGNAL_HOLD: AI decided to HOLD for fixed_volume_scalper - confidence: 0.80
2025-08-20 02:25:03 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:609 - ⏸️ HOLD_REASON: Current market conditions don't align with mean reversion strategy requirements. Price is showing moderate movement without extreme oversold/overbought conditions. RSI is in neutral territory, and the...
2025-08-20 02:25:03 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_HOLD_DECISION | Account fixed_volume_scalper - EURUSD! - HOLD decision with confidence 0.80
2025-08-20 02:25:04 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:408 - ✅ SYMBOL_DIRECT: Using account-defined symbol GBPUSD! for account fixed_volume_scalper (no normalization)
2025-08-20 02:25:04 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for GBPUSD!_M5_mean_reversion_fixed_volume_fixed_volume_scalper
2025-08-20 02:25:04 | INFO     | mt5_integration.mt5_client:login:113 - 🔍 LOGIN_DEBUG: Starting login process for account fixed_volume_scalper (********)
2025-08-20 02:25:04 | INFO     | mt5_integration.mt5_client:login:129 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-20 02:25:04 | INFO     | mt5_integration.mt5_client:login:134 - 🔍 LOGIN_DEBUG: Already logged into account ********
2025-08-20 02:25:04 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: LOGIN_SUCCESS
2025-08-20 02:25:04 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:467 - ✅ MARKET_DATA_OPTIMIZED: Getting market data for account fixed_volume_scalper: GBPUSD! on CapitalxtendLLC-MU (using batched retrieval)
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=market_data | key=2fd5918386fb4f5b
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_request_GBPUSD!_M5_duration=0.031 seconds
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=market_data | size_bytes=24250 | ttl_seconds=60
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_fetched=1 | timeframe=M5
2025-08-20 02:25:04 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: fixed_volume_scalper | Symbol: GBPUSD! | Timeframe: M5 | Candles: 200 | Time: 0.094s
2025-08-20 02:25:04 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: BALANCE_UPDATE | Balance: $972.25 | Equity: $972.25 | Margin Level: 83814.7%
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=2a28ad9beb6d2f78
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=c74292cc41286d36
2025-08-20 02:25:04 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_miss=1 | strategy=mean_reversion | timeframe=M5
2025-08-20 02:25:15 | INFO     | ai_integration.response_sanitizer:sanitize_response:77 - 🧹 Sanitized AI response - rem oved control characters and fixed formatting
2025-08-20 02:25:15 | WARNING  | ai_integration.response_sanitizer:extract_json_from_text:172 - 🔧 JSON parsing failed, attempting manual field extraction
2025-08-20 02:25:15 | WARNING  | ai_integration.response_sanitizer:_extract_fields_manually:220 - 🔧 Successfully extracted trading decision using manual field extraction
2025-08-20 02:25:15 | INFO     | ai_integration.response_sanitizer:_extract_fields_manually:221 - Extracted fields: action=SELL, confidence=0.75
2025-08-20 02:25:15 | INFO     | ai_integration.openrouter_client:_parse_ai_response:252 - ✅ Successfully parsed AI trading decision: SELL (confidence: 75.00%)
2025-08-20 02:25:15 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=ai_response | size_bytes=2480 | ttl_seconds=1800
2025-08-20 02:25:15 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=ai_response | size_bytes=2480 | ttl_seconds=900
2025-08-20 02:25:16 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_response_cached=1 | strategy=mean_reversion | confidence=0.75
2025-08-20 02:25:16 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_request_processed=1 | processing_time=11.************** | prompt_length=14473
2025-08-20 02:25:16 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: fixed_volume_scalper | Symbol: GBPUSD! | Strategy: mean_reversion | Prompt Length: 14473 chars | Processing Time: 11.53s | Action: SELL | Confidence: 0.75
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:603 - 🚀 SIGNAL_PROCESSING: Processing SELL signal for fixed_volume_scalper with confidence 0.75
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:637 - AI Signal for fixed_volume_scalper: {'action': 'SELL', 'confidence': 0.75, 'reasoning': 'Price has reached overbought conditions on M5 timeframe with clear signs of exhaustion. Recent price action shows rejection at 1.3502 level with decreasing volume, suggesting weakening buying pressure. Multiple tests of resistance around 1.3502-1.3505 area without successful breakout indicates potential mean reversion opportunity.', 'risk_level': 'MEDIUM', 'entry_price': 1.34987, 'stop_loss': 1.35037, 'take_profit': 1.34887, 'take_profit_levels': None, 'market_analysis': 'Unable to parse from response', 'strategy_alignment': 'Unable to parse from response', 'risk_assessment': 'Unable to parse from response', 'additional_signals': [], 'timestamp': '2025-08-20T02:25:15.965860', 'model': 'anthropic/claude-3.5-sonnet', 'provider': 'openrouter', 'raw_response': '{\n    "action": "SELL",\n    "confidence": 0.75,\n    "entry_price": 1.34987,\n    "stop_loss": 1.35037,\n    "take_profit": 1.34887,\n    "take_profit_levels": null,\n    "reasoning": "Price has reached overbought conditions on M5 timeframe with clear signs of exhaustion. Recent price action shows rejection at 1.3502 level with decreasing volume, suggesting weakening buying pressure. Multiple tests of resistance around 1.3502-1.3505 area without successful breakout indicates potential mean reversion opportunity.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "Market is showing ranging behavior between 1.3485-1.3505, with recent price action indicating resistance at upper range. Volume profile shows diminishing buying pressure at highs. RSI approaching overbought territory on M5 timeframe.",\n    "strategy_alignment": "Trade aligns with mean reversion strategy by:\n    - Trading against overextended move in ranging market\n    - Taking advantage of clear resistance level rejection\n    - Entering after momentum exhaustion\n    - Using tight stop loss above recent swing high\n    - Targeting quick return to mean (10 pips)",\n    "risk_assessment": "Risk parameters within account guidelines:\n    - 5 pip stop loss = $5 risk at 0.01 lots (0.51% of account)\n    - 10 pip target provides 1:2 risk-reward ratio\n    - Current spread of 1.8 pips acceptable for setup\n    - No other open positions affecting total exposure\n    - Recent performance suggests careful position sizing",\n    "additional_signals": []\n}', 'usage': {'prompt_tokens': 5903, 'completion_tokens': 404, 'total_tokens': 6307}}
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:661 - 🔍 SIGNAL_VALIDATION: Validating SELL signal for fixed_volume_scalper
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:662 - 🔍 SIGNAL_DETAILS: Entry: 1.34987, SL: 1.35037, TP: 1.34887
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:663 - 🔍 SIGNAL_CONFIDENCE: 0.75 (required: varies by strategy)
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:666 - Validating signal for fixed_volume_scalper: action=SELL, confidence=0.75, entry=1.34987, sl=1.35037, tp=1.34887
2025-08-20 02:25:16 | INFO     | strategies.mean_reversion:validate_signal:157 - Mean reversion risk: 5.0 pips
2025-08-20 02:25:16 | INFO     | strategies.mean_reversion:validate_signal:173 - Mean reversion reward: 10.0 pips
2025-08-20 02:25:16 | INFO     | strategies.mean_reversion:validate_signal:191 - Mean reversion risk-reward ratio: 2.00
2025-08-20 02:25:16 | INFO     | strategies.mean_reversion:validate_signal:200 - Mean reversion signal validation PASSED
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:701 - ✅ SIGNAL_VALIDATION_PASSED: Signal approved for account fixed_volume_scalper
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_process_signal_for_account:744 - Generated signal for account fixed_volume_scalper: SELL GBPUSD! volume=0.01 with account-specific settings
2025-08-20 02:25:16 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: BALANCE_UPDATE | Balance: $972.25 | Equity: $972.25 | Margin Level: 83814.7%
2025-08-20 02:25:16 | INFO     | signal_generation.signal_generator:_execute_trade_signal:857 - 🚀 EXECUTING TRADE: fixed_volume_scalper SELL GBPUSD! volume=0.01 entry=1.34987 sl=1.35037 tp=1.34887
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:399 - 🔍 PLACE_ORDER_DEBUG: Starting order placement with params: {'symbol': 'GBPUSD!', 'action': 'SELL', 'volume': 0.01, 'price': 1.34987, 'stop_loss': 1.35037, 'take_profit': 1.34887, 'magic_number': 0, 'comment': 'mean_reversion_fixed_volume'}
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:408 - 🔍 PLACE_ORDER_DEBUG: Current account: fixed_volume_scalper (********)
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:423 - 🔍 PLACE_ORDER_DEBUG: Terminal connected: True, Trade allowed: True
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:432 - 🔍 PLACE_ORDER_DEBUG: Account info - Login: ********, Trade allowed: True, Trade expert: True
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:441 - 🔍 PLACE_ORDER_DEBUG: Symbol GBPUSD! - Visible: True, Trade mode: 4
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:458 - 🔍 PLACE_ORDER_DEBUG: Tick data - Bid: 1.34969, Ask: 1.34988, Spread: 0.00018999999999991246
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:493 - 🔍 PLACE_ORDER_DEBUG: SELL order - Entry: 1.34987, Current Bid: 1.34969, Diff: 0.00017999999999984695
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:497 - 🔍 PLACE_ORDER_DEBUG: Price difference small (0.00017999999999984695), using current bid: 1.34969
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:512 - 🔍 PLACE_ORDER_DEBUG: Order type determined: 1, Final price: 1.34969
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:566 - 🔍 PLACE_ORDER_DEBUG: Stops level is 0, using enhanced default: 30 for GBPUSD!
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:580 - 🔍 PLACE_ORDER_DEBUG: Symbol GBPUSD! - Stops level: 30, Point: 1e-05, Base min distance: 0.000300, Final min distance: 0.000390
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:606 - 🔍 PLACE_ORDER_DEBUG: Stop loss validation passed. Final SL: 1.35037, Distance: 0.000680
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:632 - 🔍 PLACE_ORDER_DEBUG: Take profit validation passed. Final TP: 1.34887, Distance: 0.000820
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1794 - 🔍 DEMO_CHECK: Account type - Demo: True, Trade mode: 0
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1797 - 🔍 DEMO_CHECK: Demo account detected - checking limitations...
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1810 - 🔍 DEMO_CHECK: Symbol GBPUSD! - Min volume: 0.01, Max volume: 100.0
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1811 - 🔍 DEMO_CHECK: Symbol GBPUSD! - Trade mode: 4, Calc mode: N/A
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1824 - 🔍 DEMO_CHECK: Server: CapitalxtendLLC-MU, Company: Capitalxtend (Mauritius) LLC
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:654 - 🔍 PLACE_ORDER_DEBUG: Preparing MARKET order
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:670 - 🔍 PLACE_ORDER_DEBUG: Using safe comment: 'AI_AI_S_GBP' (RoboForex demo restriction)
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:678 - 🔍 PLACE_ORDER_DEBUG: Final request: {'action': 1, 'symbol': 'GBPUSD!', 'volume': 0.01, 'type': 1, 'price': 1.34969, 'magic': 0, 'comment': 'AI_AI_S_GBP', 'type_time': 0, 'type_filling': 1, 'sl': 1.35037, 'tp': 1.34887}
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:681 - 🔍 PLACE_ORDER_DEBUG: Performing order check...
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:834 - 🔍 PLACE_ORDER_DEBUG: Order check passed! Retcode: 0 (Success)
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:835 - 🔍 PLACE_ORDER_DEBUG: Margin required: 2.51, Free margin: 969.74
2025-08-20 02:25:16 | INFO     | mt5_integration.mt5_client:place_order:838 - 🔍 PLACE_ORDER_DEBUG: Sending order to MT5...
2025-08-20 02:25:17 | INFO     | mt5_integration.mt5_client:place_order:862 - 🔍 PLACE_ORDER_DEBUG: Order result received: OrderSendResult(retcode=10009, deal=********, order=********, volume=0.01, price=1.34969, bid=1.34969, ask=1.34988, comment='Request executed', request_id=1008427113, retcode_external=0, request=TradeRequest(action=1, magic=0, order=0, symbol='GBPUSD!', volume=0.01, price=1.34969, stoplimit=0.0, sl=1.35037, tp=1.34887, deviation=0, type=1, type_filling=1, type_time=0, expiration=0, comment='AI_AI_S_GBP', position=0, position_by=0))
2025-08-20 02:25:17 | INFO     | mt5_integration.mt5_client:place_order:883 - ✅ PLACE_ORDER_DEBUG: Order placed successfully! Order ID: ********, Deal ID: ********
2025-08-20 02:25:17 | INFO     | logging_system.logger:log_trade_execution:137 - EXECUTION | SUCCESS | Account: fixed_volume_scalper | SELL 0.01 GBPUSD! @ 1.34969 | Order ID: ********
2025-08-20 02:25:17 | INFO     | signal_generation.signal_generator:_execute_trade_signal:873 - ✅ TRADE EXECUTED: fixed_volume_scalper SELL GBPUSD! order_id=******** volume=0.01
2025-08-20 02:25:17 | INFO     | risk_management.unified_risk_manager:update_trade_executed:414 - 📊 Trade executed - Account fixed_volume_scalper: 3 trades today
2025-08-20 02:25:17 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | TRADE_EXECUTED | Account fixed_volume_scalper: SELL GBPUSD! order_id=******** volume=0.01 entry=1.34987 sl=1.35037 tp=1.34887
2025-08-20 02:25:17 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account fixed_volume_scalper: SELL GBPUSD! volume=0.01 confidence=0.75
2025-08-20 02:25:17 | INFO     | signal_generation.signal_generator:_process_account_group:257 - Processing group: mean_reversion/martingale with 1 accounts
2025-08-20 02:25:17 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:400 - Processing account martingale_trader with individual settings: {'_comment': 'MARTINGALE - Very conservative for small accounts', 'base_volume': 0.01, 'max_multiplier': 2, 'max_consecutive_losses': 2, 'max_daily_loss': 3.0, 'max_daily_loss_percent': 4.0, 'max_drawdown_percent': 12.0, 'max_daily_trades': 5, 'max_open_positions': 1, 'max_pending_orders': 2, '_risk_controls': {'max_risk_per_trade_percent': 3.0, 'max_risk_multiplier': 2.0, 'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}, 'risk_percent': 2.0, 'max_risk_per_trade': 15.0, 'max_risk_multiplier': 5.0}
2025-08-20 02:25:17 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:408 - ✅ SYMBOL_DIRECT: Using account-defined symbol EURUSD! for account martingale_trader (no normalization)
2025-08-20 02:25:17 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD!_M15_mean_reversion_martingale_martingale_trader
2025-08-20 02:25:17 | INFO     | mt5_integration.mt5_client:login:113 - 🔍 LOGIN_DEBUG: Starting login process for account martingale_trader (********)
2025-08-20 02:25:17 | INFO     | mt5_integration.mt5_client:login:129 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-20 02:25:17 | INFO     | mt5_integration.mt5_client:login:141 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-20 02:25:17 | INFO     | mt5_integration.mt5_client:login:144 - 🔍 LOGIN_DEBUG: Attempting login with server: CapitalxtendLLC-MU
2025-08-20 02:25:21 | INFO     | mt5_integration.mt5_client:login:182 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-20 02:25:39 | INFO     | mt5_integration.mt5_client:login:183 - 🔍 LOGIN_DEBUG: Account details - Balance: 995.88, Equity: 995.58, Currency: USD
2025-08-20 02:25:39 | INFO     | mt5_integration.mt5_client:login:184 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-20 02:25:39 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: martingale_trader | Status: LOGIN_SUCCESS
2025-08-20 02:25:39 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:467 - ✅ MARKET_DATA_OPTIMIZED: Getting market data for account martingale_trader: EURUSD! on CapitalxtendLLC-MU (using batched retrieval)
2025-08-20 02:25:39 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_expired=1 | cache_type=market_data
2025-08-20 02:25:39 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_request_EURUSD!_M15_duration=0.016 seconds
2025-08-20 02:25:39 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=market_data | size_bytes=23958 | ttl_seconds=60
2025-08-20 02:25:39 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: market_data_fetched=1 | timeframe=M15
2025-08-20 02:25:40 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: martingale_trader | Symbol: EURUSD! | Timeframe: M15 | Candles: 200 | Time: 0.126s
2025-08-20 02:25:40 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: martingale_trader | Status: BALANCE_UPDATE | Balance: $995.88 | Equity: $995.57 | Margin Level: 85825.0%
2025-08-20 02:25:40 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=93e4503d665b0411
2025-08-20 02:25:40 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_miss=1 | cache_type=ai_response | key=7a652f5e07c676f5
2025-08-20 02:25:40 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_cache_miss=1 | strategy=mean_reversion | timeframe=M15

🛑 Shutdown signal received...
2025-08-20 02:25:44 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:44 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:44 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:45 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:45 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:45 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:46 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:46 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:46 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:46 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:47 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations

🛑 Shutdown signal received...
2025-08-20 02:25:47 | INFO     | __main__:stop:365 - 🛑 Stop signal received - system will shutdown after current operations
2025-08-20 02:25:58 | INFO     | ai_integration.response_sanitizer:sanitize_response:77 - 🧹 Sanitized AI response - rem oved control characters and fixed formatting
2025-08-20 02:25:58 | WARNING  | ai_integration.response_sanitizer:extract_json_from_text:172 - 🔧 JSON parsing failed, attempting manual field extraction
2025-08-20 02:25:58 | WARNING  | ai_integration.response_sanitizer:_extract_fields_manually:220 - 🔧 Successfully extracted trading decision using manual field extraction
2025-08-20 02:25:58 | INFO     | ai_integration.response_sanitizer:_extract_fields_manually:221 - Extracted fields: action=BUY, confidence=0.75
2025-08-20 02:25:58 | INFO     | ai_integration.openrouter_client:_parse_ai_response:252 - ✅ Successfully parsed AI trading decision: BUY (confidence: 75.00%)
2025-08-20 02:25:58 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=ai_response | size_bytes=3235 | ttl_seconds=1800
2025-08-20 02:25:59 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: cache_set=1 | cache_type=ai_response | size_bytes=3235 | ttl_seconds=900
2025-08-20 02:25:59 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_response_cached=1 | strategy=mean_reversion | confidence=0.75
2025-08-20 02:26:00 | INFO     | logging_system.logging_standards:log_performance_metric:195 - [PERFORMANCE] METRIC: ai_request_processed=1 | processing_time=18.*************** | prompt_length=14794
2025-08-20 02:26:00 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: martingale_trader | Symbol: EURUSD! | Strategy: mean_reversion | Prompt Length: 14794 chars | Processing Time: 18.84s | Action: BUY | Confidence: 0.75
2025-08-20 02:26:02 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:603 - 🚀 SIGNAL_PROCESSING: Processing BUY signal for martingale_trader with confidence 0.75
2025-08-20 02:26:04 | INFO     | signal_generation.signal_generator:_process_signal_for_account:637 - AI Signal for martingale_trader: {'action': 'BUY', 'confidence': 0.75, 'reasoning': 'Price has shown a strong mean reversion setup with multiple confirmations: 1) RSI recently reached oversold conditions during the recent drop to 1.16224, 2) Price has formed a double bottom pattern with higher lows, 3) Volume is decreasing on the downward moves indicating selling pressure exhaustion, 4) Current price is near the lower Bollinger Band showing potential for mean reversion.', 'risk_level': 'LOW', 'entry_price': 1.16387, 'stop_loss': 1.16337, 'take_profit': 1.16437, 'take_profit_levels': None, 'market_analysis': 'Unable to parse from response', 'strategy_alignment': 'Unable to parse from response', 'risk_assessment': 'Unable to parse from response', 'additional_signals': [], 'timestamp': '2025-08-20T02:25:58.965946', 'model': 'anthropic/claude-3.5-sonnet', 'provider': 'openrouter', 'raw_response': '{\n    "action": "BUY",\n    "confidence": 0.75,\n    "entry_price": 1.16387,\n    "stop_loss": 1.16337,\n    "take_profit": 1.16437,\n    "take_profit_levels": null,\n    "reasoning": "Price has shown a strong mean reversion setup with multiple confirmations: 1) RSI recently reached oversold conditions during the recent drop to 1.16224, 2) Price has formed a double bottom pattern with higher lows, 3) Volume is decreasing on the downward moves indicating selling pressure exhaustion, 4) Current price is near the lower Bollinger Band showing potential for mean reversion.",\n    "risk_level": "LOW",\n    "market_analysis": "Market is showing ranging behavior between 1.16200-1.16500, with decreasing volatility and clear support/resistance levels. Recent sharp drop appears to be overextended, creating ideal mean reversion opportunity. Volume profile supports potential reversal.",\n    "strategy_alignment": "This setup aligns perfectly with our mean reversion strategy: 1) Market is ranging, not trending strongly 2) Price reached extreme levels 3) Technical indicators suggest oversold conditions 4) Setup allows for tight stop loss and quick profit target",\n    "risk_assessment": "Risk is well-controlled with 5 pip stop loss targeting 5 pip profit (1:1 ratio). Position size of 0.02 lots (current Martingale level) puts risk at acceptable level given account size. Single take profit chosen due to scalping nature of strategy and need for quick exits.",\n    "additional_signals": []\n}\n\nExplanation of key decisions:\n\n1. Entry (1.16387): Current price showing rejection from lows with support\n2. Stop Loss (1.16337): 5 pips below entry, below recent support level\n3. Take Profit (1.16437): 5 pips above entry, quick scalp target aligned with strategy\n4. Single TP chosen over multiple TPs due to:\n   - Scalping strategy requiring quick exits\n   - Mean reversion typically has limited upside\n   - Current market volatility favors conservative targets\n\nRisk management considerations:\n- Position size (0.02) aligns with current Martingale level\n- Risk per trade within 2% account risk limit\n- Quick profit target supports higher win rate needed for Martingale\n- Tight stop loss protects against trend continuation', 'usage': {'prompt_tokens': 5977, 'completion_tokens': 568, 'total_tokens': 6545}}
2025-08-20 02:26:06 | INFO     | signal_generation.signal_generator:_process_signal_for_account:661 - 🔍 SIGNAL_VALIDATION: Validating BUY signal for martingale_trader
2025-08-20 02:26:08 | INFO     | signal_generation.signal_generator:_process_signal_for_account:662 - 🔍 SIGNAL_DETAILS: Entry: 1.16387, SL: 1.16337, TP: 1.16437
2025-08-20 02:26:11 | INFO     | signal_generation.signal_generator:_process_signal_for_account:663 - 🔍 SIGNAL_CONFIDENCE: 0.75 (required: varies by strategy)
2025-08-20 02:26:14 | INFO     | signal_generation.signal_generator:_process_signal_for_account:666 - Validating signal for martingale_trader: action=BUY, confidence=0.75, entry=1.16387, sl=1.16337, tp=1.16437
2025-08-20 02:26:14 | INFO     | strategies.mean_reversion:validate_signal:157 - Mean reversion risk: 5.0 pips
2025-08-20 02:26:15 | INFO     | strategies.mean_reversion:validate_signal:173 - Mean reversion reward: 5.0 pips
2025-08-20 02:26:16 | INFO     | strategies.mean_reversion:validate_signal:191 - Mean reversion risk-reward ratio: 1.00
2025-08-20 02:26:17 | INFO     | strategies.mean_reversion:validate_signal:200 - Mean reversion signal validation PASSED
2025-08-20 02:26:17 | INFO     | signal_generation.signal_generator:_process_signal_for_account:701 - ✅ SIGNAL_VALIDATION_PASSED: Signal approved for account martingale_trader
2025-08-20 02:26:17 | INFO     | signal_generation.signal_generator:_process_signal_for_account:744 - Generated signal for account martingale_trader: BUY EURUSD! volume=0.01 with account-specific settings
2025-08-20 02:26:17 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: martingale_trader | Status: BALANCE_UPDATE | Balance: $995.88 | Equity: $995.71 | Margin Level: 85837.1%
2025-08-20 02:26:17 | INFO     | signal_generation.signal_generator:_execute_trade_signal:857 - 🚀 EXECUTING TRADE: martingale_trader BUY EURUSD! volume=0.01 entry=1.16387 sl=1.16337 tp=1.16437
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:399 - 🔍 PLACE_ORDER_DEBUG: Starting order placement with params: {'symbol': 'EURUSD!', 'action': 'BUY', 'volume': 0.01, 'price': 1.16387, 'stop_loss': 1.16337, 'take_profit': 1.16437, 'magic_number': 0, 'comment': 'mean_reversion_martingale'}
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:408 - 🔍 PLACE_ORDER_DEBUG: Current account: martingale_trader (********)
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:423 - 🔍 PLACE_ORDER_DEBUG: Terminal connected: True, Trade allowed: True
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:432 - 🔍 PLACE_ORDER_DEBUG: Account info - Login: ********, Trade allowed: True, Trade expert: True
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:441 - 🔍 PLACE_ORDER_DEBUG: Symbol EURUSD! - Visible: True, Trade mode: 4
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:458 - 🔍 PLACE_ORDER_DEBUG: Tick data - Bid: 1.16387, Ask: 1.164, Spread: 0.00012999999999996348
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:471 - 🔍 PLACE_ORDER_DEBUG: BUY order - Entry: 1.16387, Current Ask: 1.164, Diff: 0.00012999999999996348
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:475 - 🔍 PLACE_ORDER_DEBUG: Price difference small (0.00012999999999996348), using current ask: 1.164
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:512 - 🔍 PLACE_ORDER_DEBUG: Order type determined: 0, Final price: 1.164
2025-08-20 02:26:17 | INFO     | mt5_integration.mt5_client:place_order:566 - 🔍 PLACE_ORDER_DEBUG: Stops level is 0, using enhanced default: 30 for EURUSD!
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:580 - 🔍 PLACE_ORDER_DEBUG: Symbol EURUSD! - Stops level: 30, Point: 1e-05, Base min distance: 0.000300, Final min distance: 0.000390
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:606 - 🔍 PLACE_ORDER_DEBUG: Stop loss validation passed. Final SL: 1.16337, Distance: 0.000630
2025-08-20 02:26:18 | WARNING  | mt5_integration.mt5_client:place_order:619 - ⚠️ PLACE_ORDER_DEBUG: Take profit distance 0.000370 < required 0.000390
2025-08-20 02:26:18 | WARNING  | mt5_integration.mt5_client:place_order:628 - ⚠️ PLACE_ORDER_DEBUG: Adjusting TP from 1.16437 to 1.16439 (distance: 0.000390)
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:632 - 🔍 PLACE_ORDER_DEBUG: Take profit validation passed. Final TP: 1.16439, Distance: 0.000390
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1794 - 🔍 DEMO_CHECK: Account type - Demo: True, Trade mode: 0
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1797 - 🔍 DEMO_CHECK: Demo account detected - checking limitations...
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1810 - 🔍 DEMO_CHECK: Symbol EURUSD! - Min volume: 0.01, Max volume: 100.0
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1811 - 🔍 DEMO_CHECK: Symbol EURUSD! - Trade mode: 4, Calc mode: N/A
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:_check_demo_account_limitations:1824 - 🔍 DEMO_CHECK: Server: CapitalxtendLLC-MU, Company: Capitalxtend (Mauritius) LLC
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:654 - 🔍 PLACE_ORDER_DEBUG: Preparing MARKET order
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:670 - 🔍 PLACE_ORDER_DEBUG: Using safe comment: 'AI_AI_B_EUR' (RoboForex demo restriction)
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:678 - 🔍 PLACE_ORDER_DEBUG: Final request: {'action': 1, 'symbol': 'EURUSD!', 'volume': 0.01, 'type': 0, 'price': 1.164, 'magic': 0, 'comment': 'AI_AI_B_EUR', 'type_time': 0, 'type_filling': 1, 'sl': 1.16337, 'tp': 1.16439}
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:681 - 🔍 PLACE_ORDER_DEBUG: Performing order check...
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:834 - 🔍 PLACE_ORDER_DEBUG: Order check passed! Retcode: 0 (Success)
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:835 - 🔍 PLACE_ORDER_DEBUG: Margin required: 2.33, Free margin: 993.38
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:838 - 🔍 PLACE_ORDER_DEBUG: Sending order to MT5...
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:862 - 🔍 PLACE_ORDER_DEBUG: Order result received: OrderSendResult(retcode=10009, deal=********, order=********, volume=0.01, price=1.16401, bid=1.16387, ask=1.16401, comment='Request executed', request_id=1008427114, retcode_external=0, request=TradeRequest(action=1, magic=0, order=0, symbol='EURUSD!', volume=0.01, price=1.164, stoplimit=0.0, sl=1.16337, tp=1.16439, deviation=0, type=0, type_filling=1, type_time=0, expiration=0, comment='AI_AI_B_EUR', position=0, position_by=0))
2025-08-20 02:26:18 | INFO     | mt5_integration.mt5_client:place_order:883 - ✅ PLACE_ORDER_DEBUG: Order placed successfully! Order ID: ********, Deal ID: ********
2025-08-20 02:26:18 | INFO     | logging_system.logger:log_trade_execution:137 - EXECUTION | SUCCESS | Account: martingale_trader | BUY 0.01 EURUSD! @ 1.164 | Order ID: ********
2025-08-20 02:26:18 | INFO     | signal_generation.signal_generator:_execute_trade_signal:873 - ✅ TRADE EXECUTED: martingale_trader BUY EURUSD! order_id=******** volume=0.01
2025-08-20 02:26:18 | INFO     | risk_management.unified_risk_manager:update_trade_executed:414 - 📊 Trade executed - Account martingale_trader: 3 trades today
2025-08-20 02:26:18 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | TRADE_EXECUTED | Account martingale_trader: BUY EURUSD! order_id=******** volume=0.01 entry=1.16387 sl=1.16337 tp=1.16437
2025-08-20 02:26:18 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account martingale_trader: BUY EURUSD! volume=0.01 confidence=0.75
2025-08-20 02:26:18 | INFO     | logging_system.logging_standards:log_system_operation:86 - [SYSTEM] SIGNAL_GENERATION_CYCLE: Signal generation cycle completed successfully | groups_processed=3
2025-08-20 02:26:18 | INFO     | __main__:run_signal_generation:383 - ✅ Signal generation completed
2025-08-20 02:26:18 | INFO     | scheduling.scheduler_coordinator:finish_signal_generation:140 - 🕐 SCHEDULER: Signal generation finished at 02:26:18
2025-08-20 02:27:18 | INFO     | __main__:_shutdown:308 - 🛑 Initiating enhanced graceful shutdown...
2025-08-20 02:27:19 | INFO     | mt5_integration.mt5_client:shutdown:107 - MT5 connection closed
2025-08-20 02:27:19 | INFO     | signal_generation.signal_generator:stop:154 - Signal generation system stop requested
2025-08-20 02:27:19 | INFO     | __main__:_shutdown:314 - ✅ Signal generator stopped
2025-08-20 02:27:19 | INFO     | trade_management.trade_manager:stop:1017 - Trade management system stop requested
2025-08-20 02:27:20 | INFO     | __main__:_shutdown:318 - ✅ Trade manager stopped
2025-08-20 02:27:20 | INFO     | __main__:_shutdown:327 - ✅ Cache cleanup task stopped
2025-08-20 02:27:20 | INFO     | error_recovery.async_context_manager:cleanup_all_async_resources:318 - 🧹 Starting comp rehensive async resource cleanup
2025-08-20 02:27:20 | INFO     | error_recovery.async_context_manager:cancel_all_operations:295 - 🚫 Cancelling 0 active operations
2025-08-20 02:27:21 | INFO     | error_recovery.async_context_manager:cleanup_all_resources:61 - 🧹 Cleaning up 0 tracke d resources
2025-08-20 02:27:22 | INFO     | error_recovery.async_context_manager:cleanup_all_async_resources:327 - ✅ Async resource cleanup completed
2025-08-20 02:27:22 | INFO     | __main__:_shutdown:331 - ✅ Async resources cleaned up
2025-08-20 02:27:22 | INFO     | scheduling.scheduler_coordinator:reset:188 - 🔍 SCHEDULER: Resetting scheduler state for shutdown
2025-08-20 02:27:24 | INFO     | scheduling.scheduler_coordinator:reset:190 - ✅ SCHEDULER: State reset complete
2025-08-20 02:27:26 | INFO     | __main__:_shutdown:335 - ✅ Scheduler coordinator reset
2025-08-20 02:27:26 | INFO     | __main__:_shutdown:339 - 📊 Final cache stats: 0 hits, 15 misses, 0.0% hit rate
2025-08-20 02:27:28 | INFO     | logging_system.logging_standards:log_system_operation:86 - [SYSTEM] SYSTEM_SHUTDOWN: Enhanced trading system shutdown completed | cache_stats={'total_entries': 10, 'total_size_mb': 0.10776424407958984, 'max_size_mb': 100.0, 'hit_rate': 0.0, 'hits': 0, 'misses': 15, 'evictions': 0, 'type_breakdown': {'market_data': {'count': 4, 'size_bytes': 96047}, 'ai_response': {'count': 6, 'size_bytes': 16952}}}
2025-08-20 02:27:28 | INFO     | __main__:_shutdown:350 - 🏁 Enhanced trading system shutdown complete
