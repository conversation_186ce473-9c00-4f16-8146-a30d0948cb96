#!/usr/bin/env python3
"""
Test script to verify datetime handling fixes across the codebase
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def test_loss_prevention_datetime_handling():
    """Test that loss prevention system handles datetime objects correctly"""
    print("🔍 Testing Loss Prevention DateTime Handling...")
    
    try:
        from risk_management.loss_prevention import LossPreventionSystem
        
        loss_prevention = LossPreventionSystem()
        
        # Create test trades with mixed datetime formats
        test_trades = [
            {
                'profit': -50.0,
                'time': datetime.now() - timedelta(hours=2),  # datetime object
                'symbol': 'EURUSD'
            },
            {
                'profit': -30.0,
                'time': (datetime.now() - timedelta(hours=1)).timestamp(),  # timestamp
                'symbol': 'EURUSD'
            },
            {
                'profit': 20.0,
                'time': datetime.now().timestamp(),  # timestamp
                'symbol': 'EURUSD'
            }
        ]
        
        # Test _analyze_loss_streak
        loss_streak = loss_prevention._analyze_loss_streak("test_account", test_trades)
        print(f"   ✅ Loss streak analysis: {loss_streak.consecutive_losses} consecutive losses")
        
        # Test _calculate_daily_loss
        daily_loss = loss_prevention._calculate_daily_loss(test_trades)
        print(f"   ✅ Daily loss calculation: ${daily_loss:.2f}")
        
        # Test risk assessment
        from money_management.base_strategy import AccountInfo

        account_info = AccountInfo(
            balance=1000.0,
            equity=950.0,
            margin=100.0,
            free_margin=850.0,
            margin_level=950.0,
            currency="USD",
            leverage=100
        )

        account_settings = {
            'money_management_settings': {
                'max_daily_loss': 100.0
            }
        }

        risk_assessment = loss_prevention.assess_trading_risk(
            "test_account", account_info, test_trades, account_settings
        )
        print(f"   ✅ Risk assessment: {risk_assessment.risk_level} - Trading allowed: {risk_assessment.allow_trading}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Loss prevention test failed: {e}")
        return False

def test_trade_manager_datetime_handling():
    """Test that trade manager handles datetime objects correctly"""
    print("\n🔍 Testing Trade Manager DateTime Handling...")
    
    try:
        # Create mock order with datetime object
        mock_order = {
            'ticket': 12345,
            'symbol': 'EURUSD',
            'type': 2,  # BUY_LIMIT
            'volume': 0.1,
            'price_open': 1.1000,
            'sl': 1.0950,
            'tp': 1.1050,
            'time_setup': datetime.now() - timedelta(hours=3),  # datetime object
            'magic': 123456
        }
        
        # Test order age calculation (simulating the logic from trade_manager.py)
        order_time = mock_order.get('time_setup', datetime.now().timestamp())
        if isinstance(order_time, datetime):
            order_age_hours = (datetime.now() - order_time).total_seconds() / 3600
        elif isinstance(order_time, (int, float)):
            order_age_hours = (datetime.now().timestamp() - order_time) / 3600
        else:
            order_age_hours = 0
        
        print(f"   ✅ Order age calculation: {order_age_hours:.1f} hours")
        
        # Test with timestamp format
        mock_order['time_setup'] = (datetime.now() - timedelta(hours=2)).timestamp()
        order_time = mock_order.get('time_setup', datetime.now().timestamp())
        if isinstance(order_time, datetime):
            order_age_hours = (datetime.now() - order_time).total_seconds() / 3600
        elif isinstance(order_time, (int, float)):
            order_age_hours = (datetime.now().timestamp() - order_time) / 3600
        else:
            order_age_hours = 0
        
        print(f"   ✅ Order age calculation (timestamp): {order_age_hours:.1f} hours")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Trade manager test failed: {e}")
        return False

def test_mt5_client_datetime_consistency():
    """Test that MT5 client datetime handling is consistent"""
    print("\n🔍 Testing MT5 Client DateTime Consistency...")
    
    try:
        # Test datetime conversion logic (simulating MT5 client behavior)
        test_timestamp = datetime.now().timestamp()
        
        # Simulate MT5 order data conversion
        converted_datetime = datetime.fromtimestamp(test_timestamp)
        print(f"   ✅ Timestamp to datetime conversion: {converted_datetime}")
        
        # Test that we can handle both formats
        formats_to_test = [
            test_timestamp,  # float timestamp
            int(test_timestamp),  # int timestamp
            converted_datetime,  # datetime object
        ]
        
        for i, time_value in enumerate(formats_to_test):
            if isinstance(time_value, datetime):
                result_timestamp = time_value.timestamp()
                result_datetime = time_value
            elif isinstance(time_value, (int, float)):
                result_timestamp = time_value
                result_datetime = datetime.fromtimestamp(time_value)
            else:
                continue
            
            print(f"   ✅ Format {i+1} handled correctly: {type(time_value).__name__} -> {result_datetime}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ MT5 client test failed: {e}")
        return False

def test_sorting_with_mixed_datetime_formats():
    """Test sorting trades with mixed datetime formats"""
    print("\n🔍 Testing Sorting with Mixed DateTime Formats...")
    
    try:
        # Create test data with mixed formats
        now = datetime.now()
        test_data = [
            {'time': now - timedelta(hours=3), 'profit': -10},  # datetime
            {'time': (now - timedelta(hours=2)).timestamp(), 'profit': -20},  # timestamp
            {'time': now - timedelta(hours=1), 'profit': 30},  # datetime
            {'time': now.timestamp(), 'profit': -40},  # timestamp
        ]
        
        # Helper function to safely get timestamp for sorting
        def get_trade_timestamp(trade):
            time_value = trade.get('time', 0)
            if isinstance(time_value, datetime):
                return time_value.timestamp()
            elif isinstance(time_value, (int, float)):
                return time_value
            else:
                return 0
        
        # Sort trades by time (most recent first)
        sorted_trades = sorted(test_data, key=get_trade_timestamp, reverse=True)
        
        print("   ✅ Sorted trades (most recent first):")
        for i, trade in enumerate(sorted_trades):
            time_value = trade['time']
            if isinstance(time_value, datetime):
                time_str = time_value.strftime('%H:%M:%S')
            else:
                time_str = datetime.fromtimestamp(time_value).strftime('%H:%M:%S')
            print(f"      {i+1}. {time_str} - Profit: ${trade['profit']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Sorting test failed: {e}")
        return False

def main():
    """Run all datetime handling tests"""
    print("🚀 DATETIME HANDLING FIXES VERIFICATION")
    print("=" * 50)
    
    tests = [
        test_loss_prevention_datetime_handling,
        test_trade_manager_datetime_handling,
        test_mt5_client_datetime_consistency,
        test_sorting_with_mixed_datetime_formats,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All datetime handling fixes are working correctly!")
        return True
    else:
        print("❌ Some datetime handling issues remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
