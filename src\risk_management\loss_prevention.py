"""
Loss Prevention System
Implements advanced risk management to prevent consecutive losses and improve win rates
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from money_management.base_strategy import AccountInfo
from logging_system.logger import get_logger, trading_logger

logger = get_logger(__name__)

@dataclass
class LossStreakInfo:
    """Information about current loss streak"""
    consecutive_losses: int
    total_loss_amount: float
    last_loss_time: datetime
    streak_start_time: datetime

@dataclass
class RiskAssessment:
    """Risk assessment result"""
    risk_level: str  # LOW, MEDIUM, HIGH, CRITICAL
    allow_trading: bool
    recommended_volume_multiplier: float
    reasons: List[str]
    cooling_off_until: Optional[datetime] = None

class LossPreventionSystem:
    """Advanced loss prevention and risk management system"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.loss_streaks = {}  # account_id -> LossStreakInfo
        self.daily_stats = {}   # account_id -> daily trading stats
        
    def assess_trading_risk(
        self,
        account_id: str,
        account_info: AccountInfo,
        recent_trades: List[Dict[str, Any]],
        account_settings: Dict[str, Any]
    ) -> RiskAssessment:
        """
        Assess current trading risk and determine if trading should be allowed
        
        Args:
            account_id: Account identifier
            account_info: Current account balance and equity information
            recent_trades: List of recent trades for analysis
            account_settings: Account risk management settings
            
        Returns:
            RiskAssessment with recommendations
        """
        try:
            reasons = []
            risk_level = "LOW"
            allow_trading = True
            volume_multiplier = 1.0
            cooling_off_until = None
            
            # 1. Check consecutive losses
            loss_streak = self._analyze_loss_streak(account_id, recent_trades)
            if loss_streak.consecutive_losses >= 3:
                risk_level = "HIGH"
                volume_multiplier = 0.5  # Reduce volume by 50%
                reasons.append(f"Consecutive losses: {loss_streak.consecutive_losses}")
                
                if loss_streak.consecutive_losses >= 5:
                    risk_level = "CRITICAL"
                    allow_trading = False
                    cooling_off_until = datetime.now() + timedelta(hours=2)
                    reasons.append("Critical loss streak - trading suspended for 2 hours")
            
            # 2. Check daily loss limits
            daily_loss = self._calculate_daily_loss(recent_trades)
            max_daily_loss = account_settings.get('money_management_settings', {}).get('max_daily_loss', 5.0)
            
            if daily_loss >= max_daily_loss * 0.8:  # 80% of limit
                risk_level = "HIGH"
                volume_multiplier = min(volume_multiplier, 0.3)
                reasons.append(f"Daily loss approaching limit: ${daily_loss:.2f}/${max_daily_loss:.2f}")
                
                if daily_loss >= max_daily_loss:
                    allow_trading = False
                    cooling_off_until = datetime.now().replace(hour=23, minute=59, second=59)
                    reasons.append("Daily loss limit exceeded - trading suspended until tomorrow")
            
            # 3. Check drawdown
            max_drawdown_percent = account_settings.get('money_management_settings', {}).get('max_drawdown_percent', 15.0)
            current_drawdown = ((account_info.balance - account_info.equity) / account_info.balance) * 100
            
            if current_drawdown >= max_drawdown_percent * 0.7:  # 70% of limit
                risk_level = "HIGH"
                volume_multiplier = min(volume_multiplier, 0.4)
                reasons.append(f"High drawdown: {current_drawdown:.1f}%/{max_drawdown_percent:.1f}%")
                
                if current_drawdown >= max_drawdown_percent:
                    allow_trading = False
                    reasons.append("Maximum drawdown exceeded - trading suspended")
            
            # 4. Check margin level
            if account_info.margin_level < 200:
                risk_level = "CRITICAL"
                allow_trading = False
                reasons.append(f"Critical margin level: {account_info.margin_level:.1f}%")
            elif account_info.margin_level < 300:
                risk_level = "HIGH"
                volume_multiplier = min(volume_multiplier, 0.2)
                reasons.append(f"Low margin level: {account_info.margin_level:.1f}%")
            
            # 5. Check win rate (last 20 trades)
            recent_20_trades = recent_trades[-20:] if len(recent_trades) >= 20 else recent_trades
            if len(recent_20_trades) >= 10:
                winning_trades = len([t for t in recent_20_trades if t.get('profit', 0) > 0])
                win_rate = (winning_trades / len(recent_20_trades)) * 100
                
                if win_rate < 30:  # Very low win rate
                    risk_level = "HIGH"
                    volume_multiplier = min(volume_multiplier, 0.5)
                    reasons.append(f"Low win rate: {win_rate:.1f}% (last {len(recent_20_trades)} trades)")
            
            # 6. Time-based restrictions (avoid high-impact news times)
            current_hour = datetime.now().hour
            if current_hour in [8, 9, 13, 14, 15]:  # Major news hours (UTC)
                volume_multiplier = min(volume_multiplier, 0.7)
                reasons.append("High-impact news time - reduced volume")
            
            # Log risk assessment
            if risk_level != "LOW" or not allow_trading:
                self.logger.warning(f"🚨 RISK ASSESSMENT: Account {account_id} - {risk_level} risk")
                for reason in reasons:
                    self.logger.warning(f"   • {reason}")
                
                trading_logger.log_system_event(
                    "RISK_ASSESSMENT",
                    f"Account {account_id}: {risk_level} risk - Trading {'ALLOWED' if allow_trading else 'SUSPENDED'}",
                    "WARNING" if allow_trading else "ERROR"
                )
            
            return RiskAssessment(
                risk_level=risk_level,
                allow_trading=allow_trading,
                recommended_volume_multiplier=volume_multiplier,
                reasons=reasons,
                cooling_off_until=cooling_off_until
            )
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment for {account_id}: {e}")
            # Conservative fallback
            return RiskAssessment(
                risk_level="HIGH",
                allow_trading=False,
                recommended_volume_multiplier=0.1,
                reasons=[f"Risk assessment error: {e}"]
            )
    
    def _analyze_loss_streak(self, account_id: str, recent_trades: List[Dict[str, Any]]) -> LossStreakInfo:
        """Analyze current loss streak for an account"""
        try:
            consecutive_losses = 0
            total_loss_amount = 0.0
            last_loss_time = None
            streak_start_time = None

            # Helper function to safely get timestamp for sorting
            def get_trade_timestamp(trade):
                time_value = trade.get('time', 0)
                if isinstance(time_value, datetime):
                    return time_value.timestamp()
                elif isinstance(time_value, (int, float)):
                    return time_value
                else:
                    return 0

            # Sort trades by time (most recent first)
            sorted_trades = sorted(recent_trades, key=get_trade_timestamp, reverse=True)

            for trade in sorted_trades:
                profit = trade.get('profit', 0)
                if profit < 0:  # Loss
                    consecutive_losses += 1
                    total_loss_amount += abs(profit)

                    # Safely convert time to datetime
                    time_value = trade.get('time', 0)
                    if isinstance(time_value, datetime):
                        trade_time = time_value
                    elif isinstance(time_value, (int, float)) and time_value > 0:
                        trade_time = datetime.fromtimestamp(time_value)
                    else:
                        trade_time = datetime.now()

                    if last_loss_time is None:
                        last_loss_time = trade_time
                    streak_start_time = trade_time
                else:  # Profit or break-even
                    break

            return LossStreakInfo(
                consecutive_losses=consecutive_losses,
                total_loss_amount=total_loss_amount,
                last_loss_time=last_loss_time or datetime.now(),
                streak_start_time=streak_start_time or datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error analyzing loss streak for {account_id}: {e}")
            return LossStreakInfo(0, 0.0, datetime.now(), datetime.now())
    
    def _calculate_daily_loss(self, recent_trades: List[Dict[str, Any]]) -> float:
        """Calculate total loss for today"""
        try:
            today = datetime.now().date()
            daily_loss = 0.0

            for trade in recent_trades:
                # Safely convert time to datetime
                time_value = trade.get('time', 0)
                if isinstance(time_value, datetime):
                    trade_time = time_value.date()
                elif isinstance(time_value, (int, float)) and time_value > 0:
                    trade_time = datetime.fromtimestamp(time_value).date()
                else:
                    continue  # Skip trades with invalid time

                if trade_time == today:
                    profit = trade.get('profit', 0)
                    if profit < 0:
                        daily_loss += abs(profit)

            return daily_loss

        except Exception as e:
            self.logger.error(f"Error calculating daily loss: {e}")
            return 0.0
    
    def should_skip_signal(
        self,
        account_id: str,
        signal_confidence: float,
        risk_assessment: RiskAssessment
    ) -> bool:
        """Determine if a signal should be skipped based on risk assessment"""
        try:
            # Don't trade if trading is not allowed
            if not risk_assessment.allow_trading:
                return True
            
            # Skip low confidence signals during high risk periods
            if risk_assessment.risk_level == "HIGH" and signal_confidence < 0.8:
                self.logger.info(f"Skipping low confidence signal for {account_id} during high risk period")
                return True
            
            # Skip medium confidence signals during critical risk
            if risk_assessment.risk_level == "CRITICAL" and signal_confidence < 0.9:
                self.logger.info(f"Skipping signal for {account_id} during critical risk period")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in signal skip decision for {account_id}: {e}")
            return True  # Conservative approach

# Global instance
loss_prevention_system = LossPreventionSystem()
