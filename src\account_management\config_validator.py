#!/usr/bin/env python3
"""
Configuration Validation and Standardization Utility
Ensures all account configurations follow the unified schema
"""

import json
from typing import Dict, Any, List, Tuple
from pathlib import Path

from logging_system.logger import get_logger

logger = get_logger(__name__)

class ConfigValidator:
    """Validates and standardizes account configurations"""
    
    def __init__(self):
        self.required_fields = {
            'account_id': str,
            'account_number': int,
            'password': str,
            'server': str,
            'strategy': str,
            'money_management': str,
            'symbols': list
        }
        
        self.required_money_management_fields = {
            'max_daily_trades': int,
            'max_open_positions': int,
            'max_pending_orders': int,
            'max_daily_loss': (int, float)
        }
        
        self.optional_money_management_fields = {
            'risk_percent': (int, float),
            'max_daily_loss_percent': (int, float),
            'max_drawdown_percent': (int, float),
            'max_consecutive_losses': int,
            'max_risk_per_trade': (int, float),
            'max_risk_multiplier': (int, float),
            'fixed_volume': (int, float),
            'base_volume': (int, float),
            'max_multiplier': int,
            'win_multiplier_increment': (int, float)
        }
    
    def validate_config_file(self, config_path: Path) -> Tuple[bool, List[str]]:
        """Validate entire configuration file"""
        try:
            if not config_path.exists():
                return False, [f"Configuration file not found: {config_path}"]
            
            with open(config_path, 'r') as f:
                data = json.load(f)
            
            if 'accounts' not in data:
                return False, ["Configuration file missing 'accounts' section"]
            
            all_issues = []
            for i, account in enumerate(data['accounts']):
                issues = self.validate_account_config(account, f"Account {i}")
                all_issues.extend(issues)
            
            if all_issues:
                return False, all_issues
            
            logger.info(f"✅ Configuration validation passed for {len(data['accounts'])} accounts")
            return True, []
            
        except json.JSONDecodeError as e:
            return False, [f"Invalid JSON in configuration file: {e}"]
        except Exception as e:
            return False, [f"Error validating configuration: {e}"]
    
    def validate_account_config(self, account: Dict[str, Any], context: str = "Account") -> List[str]:
        """Validate individual account configuration"""
        issues = []
        
        # Check required fields
        for field, expected_type in self.required_fields.items():
            if field not in account:
                issues.append(f"{context}: Missing required field '{field}'")
            elif not isinstance(account[field], expected_type):
                issues.append(f"{context}: Field '{field}' must be of type {expected_type.__name__}")
        
        # Validate symbols structure
        if 'symbols' in account:
            for i, symbol in enumerate(account['symbols']):
                if not isinstance(symbol, dict):
                    issues.append(f"{context}: Symbol {i} must be a dictionary")
                    continue
                
                if 'symbol' not in symbol:
                    issues.append(f"{context}: Symbol {i} missing 'symbol' field")
                if 'timeframe' not in symbol:
                    issues.append(f"{context}: Symbol {i} missing 'timeframe' field")
        
        # Validate money management settings
        mm_settings = account.get('money_management_settings', {})
        if not isinstance(mm_settings, dict):
            issues.append(f"{context}: 'money_management_settings' must be a dictionary")
        else:
            # Check required money management fields
            for field, expected_type in self.required_money_management_fields.items():
                if field not in mm_settings:
                    issues.append(f"{context}: Missing required money management field '{field}'")
                elif not isinstance(mm_settings[field], expected_type):
                    issues.append(f"{context}: Money management field '{field}' must be of type {expected_type}")
            
            # Validate optional fields if present
            for field, expected_type in self.optional_money_management_fields.items():
                if field in mm_settings and not isinstance(mm_settings[field], expected_type):
                    issues.append(f"{context}: Money management field '{field}' must be of type {expected_type}")
            
            # Validate value ranges
            if 'risk_percent' in mm_settings:
                risk_percent = mm_settings['risk_percent']
                if risk_percent <= 0 or risk_percent > 100:
                    issues.append(f"{context}: 'risk_percent' must be between 0 and 100")
            
            if 'max_daily_trades' in mm_settings:
                max_trades = mm_settings['max_daily_trades']
                if max_trades <= 0:
                    issues.append(f"{context}: 'max_daily_trades' must be positive")
            
            if 'max_open_positions' in mm_settings:
                max_positions = mm_settings['max_open_positions']
                if max_positions <= 0:
                    issues.append(f"{context}: 'max_open_positions' must be positive")
            
            if 'max_pending_orders' in mm_settings:
                max_pending = mm_settings['max_pending_orders']
                if max_pending <= 0:
                    issues.append(f"{context}: 'max_pending_orders' must be positive")
        
        return issues
    
    def standardize_config_file(self, config_path: Path, backup: bool = True) -> Tuple[bool, List[str]]:
        """Standardize configuration file to use unified schema"""
        try:
            if not config_path.exists():
                return False, [f"Configuration file not found: {config_path}"]
            
            # Create backup if requested
            if backup:
                backup_path = config_path.with_suffix('.backup.json')
                import shutil
                shutil.copy2(config_path, backup_path)
                logger.info(f"Created backup: {backup_path}")
            
            with open(config_path, 'r') as f:
                data = json.load(f)
            
            if 'accounts' not in data:
                return False, ["Configuration file missing 'accounts' section"]
            
            changes = []
            for i, account in enumerate(data['accounts']):
                account_changes = self.standardize_account_config(account, f"Account {i}")
                changes.extend(account_changes)
            
            # Write standardized configuration
            with open(config_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            if changes:
                logger.info(f"✅ Standardized configuration with {len(changes)} changes")
                for change in changes:
                    logger.info(f"   • {change}")
            else:
                logger.info("✅ Configuration already standardized")
            
            return True, changes
            
        except Exception as e:
            return False, [f"Error standardizing configuration: {e}"]
    
    def standardize_account_config(self, account: Dict[str, Any], context: str = "Account") -> List[str]:
        """Standardize individual account configuration"""
        changes = []
        
        # Check for old field names and rename them
        if 'money_management_config' in account and 'money_management_settings' not in account:
            account['money_management_settings'] = account.pop('money_management_config')
            changes.append(f"{context}: Renamed 'money_management_config' to 'money_management_settings'")
        
        # Ensure money_management_settings exists
        if 'money_management_settings' not in account:
            account['money_management_settings'] = {}
            changes.append(f"{context}: Added missing 'money_management_settings' section")
        
        mm_settings = account['money_management_settings']
        
        # Standardize field names within money_management_settings
        field_mappings = {
            'max_concurrent_positions': 'max_open_positions',
            'daily_loss_limit': 'max_daily_loss',
            'daily_trade_limit': 'max_daily_trades'
        }
        
        for old_field, new_field in field_mappings.items():
            if old_field in mm_settings and new_field not in mm_settings:
                mm_settings[new_field] = mm_settings.pop(old_field)
                changes.append(f"{context}: Renamed '{old_field}' to '{new_field}' in money_management_settings")
        
        # Add default values for required fields if missing
        defaults = {
            'max_daily_trades': 10,
            'max_open_positions': 5,
            'max_pending_orders': 10,
            'max_daily_loss': 5.0,
            'max_drawdown_percent': 15.0,
            'max_consecutive_losses': 5
        }
        
        for field, default_value in defaults.items():
            if field not in mm_settings:
                mm_settings[field] = default_value
                changes.append(f"{context}: Added default value for '{field}': {default_value}")
        
        return changes


# Global validator instance
config_validator = ConfigValidator()
