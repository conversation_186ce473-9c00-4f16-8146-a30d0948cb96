# MetaTrader 5 Configuration
MT5_PATH=C:\Program Files\MetaTrader 5\terminal64.exe

# AI Provider Configuration
AI_PROVIDER=qwen  # Options: qwen, openrouter
AI_TIMEOUT=30
AI_TEMPERATURE=0.3
AI_MAX_TOKENS=2000
AI_TOP_P=0.8

# Qwen AI API Configuration
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_URL=https://dashscope-intl.aliyuncs.com/compatible-mode/v1
AI_MODEL=qwen-max-2025-01-25

# OpenRouter AI API Configuration (Alternative Provider)
OPENROUTER_API_KEY=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
OPENROUTER_API_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
OPENROUTER_SITE_URL=https://fulltrade-ai.com
OPENROUTER_APP_NAME=FullTrade AI Trading System

# Trading Configuration
SIGNAL_GENERATION_INTERVAL=300  # seconds
TRADE_MANAGEMENT_INTERVAL=60   # seconds
MAX_CONCURRENT_REQUESTS=5
ENABLE_WEEKEND_TRADING=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/trading_system.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# Risk Management
MAX_DAILY_TRADES_PER_ACCOUNT=10
MAX_DRAWDOWN_PERCENT=20
EMERGENCY_STOP_LOSS_PERCENT=50
