#!/usr/bin/env python3
"""
AI-Driven Trading System - Unified Main Application
Consolidated entry point that resolves all architectural inconsistencies
"""

import asyncio
import sys
import os
import signal
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional, Dict, Any

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

# Set PYTHONPATH environment variable for consistency
os.environ['PYTHONPATH'] = str(src_dir)

# Import system components
from logging_system.logger import setup_logger, get_logger
from logging_system.logging_standards import create_logger, LogLevel
from account_management.account_manager import AccountManager
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager
from scheduling.scheduler_coordinator import scheduler_coordinator
from error_recovery.async_context_manager import cleanup_all_async_resources
from performance.cache_manager import cache_manager, start_cache_cleanup_task

class TradingSystem:
    """
    Unified Trading System Orchestrator
    Consolidates all entry points and provides consistent initialization
    """

    def __init__(self):
        self.logger: Optional[Any] = None
        self.standardized_logger: Optional[Any] = None
        self.account_manager: Optional[AccountManager] = None
        self.signal_generator: Optional[SignalGenerator] = None
        self.trade_manager: Optional[TradeManager] = None
        self.running: bool = False
        self.initialization_complete: bool = False
        self.cache_cleanup_task: Optional[asyncio.Task] = None
        self.system_health: Dict[str, Any] = {
            'components_initialized': False,
            'accounts_loaded': False,
            'mt5_connected': False,
            'ai_client_ready': False,
            'cache_manager_ready': False,
            'error_recovery_ready': False
        }
    
    def check_prerequisites(self) -> bool:
        """Check system prerequisites before initialization"""
        try:
            # Check environment variables
            required_env_vars = ['QWEN_API_KEY', 'MT5_PATH']
            missing_vars = []

            for var in required_env_vars:
                if not os.getenv(var):
                    missing_vars.append(var)

            if missing_vars:
                print(f"❌ Missing environment variables: {missing_vars}")
                print("Please add these to your .env file:")
                for var in missing_vars:
                    if var == 'QWEN_API_KEY':
                        print(f"{var}=your_qwen_api_key_here")
                    elif var == 'MT5_PATH':
                        print(f"{var}=C:\\Program Files\\MetaTrader 5\\terminal64.exe")
                return False

            # Check configuration file
            config_file = Path("config/accounts.json")
            if not config_file.exists():
                print(f"❌ Account configuration not found: {config_file}")
                return False

            # Check MT5 path exists
            mt5_path = os.getenv('MT5_PATH')
            if mt5_path and not os.path.exists(mt5_path):
                print(f"❌ MetaTrader 5 not found at: {mt5_path}")
                return False

            return True

        except Exception as e:
            print(f"❌ Prerequisites check failed: {e}")
            return False

    async def initialize(self):
        """Initialize all system components with comprehensive error handling"""
        try:
            # Load environment variables first
            load_dotenv()

            # Check prerequisites
            if not self.check_prerequisites():
                return False

            # Setup logging
            self.logger = setup_logger()
            self.standardized_logger = create_logger("TradingSystem")
            self.logger.info("🚀 AI-Driven Trading System - Enhanced Initialization Starting...")
            self.logger.info("=" * 80)

            # Initialize enhanced components
            self.logger.info("🔧 Step 0/5: Initializing Enhanced Components...")

            # Start cache cleanup task
            self.cache_cleanup_task = asyncio.create_task(start_cache_cleanup_task())
            self.system_health['cache_manager_ready'] = True
            self.system_health['error_recovery_ready'] = True
            self.logger.info("✅ Enhanced components initialized (cache manager, error recovery)")

            self.standardized_logger.log_system_operation(
                "SYSTEM_INIT",
                "Enhanced trading system initialization started",
                LogLevel.INFO,
                {'version': '2.0', 'enhanced_features': True}
            )

            # Initialize account manager
            self.logger.info("📊 Step 1/5: Initializing Account Manager...")
            self.account_manager = AccountManager()

            if not self.account_manager.load_accounts():
                self.logger.error("❌ Failed to load accounts")
                return False

            self.system_health['accounts_loaded'] = True
            self.logger.info(f"✅ Loaded {len(self.account_manager.accounts)} trading accounts")

            # Log account details for verification
            for account_id, account in self.account_manager.accounts.items():
                self.logger.info(f"   📋 Account: {account.account_id}")
                self.logger.info(f"      Strategy: {account.strategy_type} | MM: {account.money_management_type}")
                self.logger.info(f"      Symbols: {len(account.symbols)} configured")

                # Log enhanced configuration details
                mm_settings = account.money_management_settings
                self.logger.info(f"      Risk Settings: {mm_settings.get('risk_percent', 'N/A')}% risk, "
                               f"{mm_settings.get('max_daily_trades', 'N/A')} max trades, "
                               f"{mm_settings.get('max_open_positions', 'N/A')} max positions")

            # Initialize signal generator
            self.logger.info("🎯 Step 2/5: Initializing Enhanced Signal Generator...")
            self.signal_generator = SignalGenerator(self.account_manager)

            # Initialize trade manager
            self.logger.info("📈 Step 3/5: Initializing Enhanced Trade Manager...")
            self.trade_manager = TradeManager(self.account_manager)

            # Verify component health
            self.logger.info("🔍 Step 4/5: Verifying System Health...")
            if await self._verify_system_health():
                self.system_health['components_initialized'] = True

                # Final system readiness check
                self.logger.info("🚀 Step 5/5: Final System Readiness Check...")
                cache_stats = cache_manager.get_stats()
                self.logger.info(f"   📊 Cache Manager: {cache_stats['total_entries']} entries, "
                               f"{cache_stats['total_size_mb']:.2f}MB used")

                self.initialization_complete = True
                self.logger.info("✅ Enhanced trading system ready for operation")
                self.logger.info("=" * 80)
                return True
            else:
                self.logger.error("❌ System health verification failed")
                return False
            # Health verification already done above

        except Exception as e:
            if self.logger:
                self.logger.error(f"❌ Initialization failed: {e}")
                self.logger.exception("Full error details:")
            else:
                print(f"❌ Initialization failed: {e}")
            return False

    async def _verify_system_health(self) -> bool:
        """Verify all system components are healthy and ready"""
        try:
            # Check account manager
            if not self.account_manager or len(self.account_manager.accounts) == 0:
                self.logger.error("❌ Account manager not properly initialized")
                return False

            # Check signal generator
            if not self.signal_generator:
                self.logger.error("❌ Signal generator not initialized")
                return False

            # Check trade manager
            if not self.trade_manager:
                self.logger.error("❌ Trade manager not initialized")
                return False

            # Test AI client connectivity (basic check)
            try:
                from ai_integration.ai_provider_factory import get_ai_provider_with_fallback
                client = await get_ai_provider_with_fallback()
                async with client:
                    # Basic connectivity test
                    if await client.validate_connection():
                        self.system_health['ai_client_ready'] = True
                        self.logger.info("✅ AI client connectivity verified")
                    else:
                        self.system_health['ai_client_ready'] = False
                        self.logger.warning("⚠️ AI client connection validation failed")
            except Exception as e:
                self.logger.warning(f"⚠️ AI client test failed: {e}")
                self.system_health['ai_client_ready'] = False

            # Test MT5 basic connectivity
            try:
                from mt5_integration.mt5_client import MT5Client
                mt5_client = MT5Client()
                if mt5_client.initialize():
                    self.system_health['mt5_connected'] = True
                    self.logger.info("✅ MT5 connectivity verified")
                    mt5_client.shutdown()
                else:
                    self.logger.warning("⚠️ MT5 initialization test failed")
                    self.system_health['mt5_connected'] = False
            except Exception as e:
                self.logger.warning(f"⚠️ MT5 connectivity test failed: {e}")
                self.system_health['mt5_connected'] = False

            # Log system health summary
            healthy_components = sum(1 for status in self.system_health.values() if status)
            total_components = len(self.system_health)

            self.logger.info(f"📊 System Health: {healthy_components}/{total_components} components healthy")
            for component, status in self.system_health.items():
                status_icon = "✅" if status else "⚠️"
                self.logger.info(f"   {status_icon} {component}: {'OK' if status else 'WARNING'}")

            # FIXED: Set components_initialized to True if basic components are working
            if (self.signal_generator and self.trade_manager and
                self.system_health['accounts_loaded']):
                self.system_health['components_initialized'] = True

            # System is considered healthy if core components are working
            core_healthy = (self.system_health['components_initialized'] and
                          self.system_health['accounts_loaded'])

            return core_healthy

        except Exception as e:
            self.logger.error(f"❌ System health verification failed: {e}")
            return False

    async def start(self):
        """Start the trading system with comprehensive error handling"""
        if not await self.initialize():
            return False

        if not self.initialization_complete:
            self.logger.error("❌ Cannot start system - initialization not complete")
            return False

        self.running = True
        self.logger.info("🎯 Starting Trading Operations...")
        self.logger.info("=" * 80)
        self.logger.info("🚀 System Status: ACTIVE")
        self.logger.info("📊 Monitoring both signal generation and trade management")
        self.logger.info("⏰ Press Ctrl+C to stop the system gracefully")
        self.logger.info("=" * 80)

        try:
            # Start both components concurrently with proper exception handling
            tasks = [
                asyncio.create_task(self.run_signal_generation(), name="SignalGeneration"),
                asyncio.create_task(self.run_trade_management(), name="TradeManagement")
            ]

            # Wait for tasks with proper exception handling
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for exceptions in tasks
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    task_name = tasks[i].get_name()
                    self.logger.error(f"❌ {task_name} failed with error: {result}")

            return True

        except KeyboardInterrupt:
            self.logger.info("🛑 Keyboard interrupt received - shutting down gracefully...")
            return True
        except Exception as e:
            self.logger.error(f"❌ System error: {e}")
            self.logger.exception("Full error details:")
            return False
        finally:
            await self._shutdown()

    async def _shutdown(self):
        """Enhanced graceful system shutdown"""
        self.running = False
        self.logger.info("🛑 Initiating enhanced graceful shutdown...")

        try:
            # Stop components gracefully
            if self.signal_generator:
                self.signal_generator.stop()
                self.logger.info("✅ Signal generator stopped")

            if self.trade_manager:
                self.trade_manager.stop()
                self.logger.info("✅ Trade manager stopped")

            # Stop cache cleanup task
            if self.cache_cleanup_task and not self.cache_cleanup_task.done():
                self.cache_cleanup_task.cancel()
                try:
                    await self.cache_cleanup_task
                except asyncio.CancelledError:
                    pass
                self.logger.info("✅ Cache cleanup task stopped")

            # Clean up async resources
            await cleanup_all_async_resources()
            self.logger.info("✅ Async resources cleaned up")

            # Reset scheduler state
            await scheduler_coordinator.reset()
            self.logger.info("✅ Scheduler coordinator reset")

            # Log final cache statistics
            cache_stats = cache_manager.get_stats()
            self.logger.info(f"📊 Final cache stats: {cache_stats['hits']} hits, "
                           f"{cache_stats['misses']} misses, "
                           f"{cache_stats['hit_rate']:.1%} hit rate")

            self.standardized_logger.log_system_operation(
                "SYSTEM_SHUTDOWN",
                "Enhanced trading system shutdown completed",
                LogLevel.INFO,
                {'cache_stats': cache_stats}
            )

            self.logger.info("🏁 Enhanced trading system shutdown complete")

        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")
            if self.standardized_logger:
                self.standardized_logger.log_system_operation(
                    "SHUTDOWN_ERROR",
                    f"Error during shutdown: {e}",
                    LogLevel.ERROR
                )

    def stop(self):
        """Stop the trading system (called by signal handler)"""
        self.running = False
        if self.logger:
            self.logger.info("🛑 Stop signal received - system will shutdown after current operations")
    
    async def run_signal_generation(self):
        """Run signal generation loop with scheduling coordination"""
        self.logger.info("🔄 Signal generation started")

        try:
            while self.running:
                # Check if signal generation can run
                if await scheduler_coordinator.can_run_signal_generation():
                    # Mark as starting
                    if await scheduler_coordinator.start_signal_generation():
                        try:
                            self.logger.info("📡 Generating trading signals...")

                            # Generate signals for all accounts
                            await self.signal_generator.generate_signals()

                            self.logger.info("✅ Signal generation completed")
                        finally:
                            # Mark as finished
                            await scheduler_coordinator.finish_signal_generation()
                    else:
                        self.logger.debug("⏸️ Signal generation blocked by scheduler")
                else:
                    # Get next scheduled time
                    next_time = scheduler_coordinator.get_next_signal_generation_time()
                    if next_time:
                        time_to_next = (next_time - datetime.now()).total_seconds()
                        if time_to_next > 0:
                            self.logger.debug(f"⏰ Next signal generation in {time_to_next:.0f} seconds")

                # Wait before checking again
                await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            self.logger.error(f"❌ Signal generation error: {e}")
            await scheduler_coordinator.finish_signal_generation()  # Ensure state is reset
            raise
    
    async def run_trade_management(self):
        """Run trade management loop with scheduling coordination"""
        self.logger.info("🔄 Trade management started")

        try:
            while self.running:
                # Check if trade management can run
                if await scheduler_coordinator.can_run_trade_management():
                    # Mark as starting
                    if await scheduler_coordinator.start_trade_management():
                        try:
                            self.logger.info("📊 Managing existing trades...")

                            # Manage existing trades
                            await self.trade_manager.manage_trades()

                            self.logger.info("✅ Trade management completed")
                        finally:
                            # Mark as finished
                            await scheduler_coordinator.finish_trade_management()
                    else:
                        self.logger.debug("⏸️ Trade management blocked by scheduler")
                else:
                    # Get next scheduled time
                    next_time = scheduler_coordinator.get_next_trade_management_time()
                    if next_time:
                        time_to_next = (next_time - datetime.now()).total_seconds()
                        if time_to_next > 0:
                            self.logger.debug(f"⏰ Next trade management in {time_to_next:.0f} seconds")

                # Wait before checking again
                await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            self.logger.error(f"❌ Trade management error: {e}")
            await scheduler_coordinator.finish_trade_management()  # Ensure state is reset
            raise
    
    # REMOVED: Duplicate stop method - already defined above

async def main():
    """
    Unified main application entry point
    Consolidates all previous entry point functionality
    """
    print("🤖 AI-Driven Trading System - Unified Entry Point")
    print("=" * 60)
    print("🔧 Consolidated from multiple entry points for consistency")
    print("=" * 60)

    # Create trading system
    trading_system = TradingSystem()

    # Setup signal handlers for graceful shutdown
    def signal_handler(_signum, _frame):
        """Handle shutdown signals gracefully"""
        print("\n🛑 Shutdown signal received...")
        trading_system.stop()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Start the trading system
        success = await trading_system.start()
        return success

    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received")
        trading_system.stop()
        return True

    except Exception as e:
        print(f"❌ System error: {e}")
        # Try to log the error if logger is available
        if trading_system.logger:
            trading_system.logger.exception("Fatal system error:")
        return False

def run_system():
    """
    Main system runner - replaces all other entry points
    This is the ONLY entry point that should be used
    """
    print("🚀 Starting AI-Driven Trading System...")
    print("📋 Unified Entry Point - All other entry points deprecated")
    print("⏰ Press Ctrl+C to stop the system gracefully")
    print()

    try:
        success = asyncio.run(main())

        if success:
            print("\n✅ System shutdown complete")
            print("📊 Check logs for detailed operation history")
        else:
            print("\n❌ System encountered errors")
            print("📋 Check logs for error details")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n🛑 System interrupted by user")
        print("✅ Graceful shutdown completed")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_system()
