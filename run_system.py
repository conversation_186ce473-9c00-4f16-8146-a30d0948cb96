#!/usr/bin/env python3
"""
Simple System Runner - Tests the trading system without import issues
"""

import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

def test_environment():
    """Test environment setup"""
    print("🔍 Testing Environment Setup...")
    
    # Load environment variables
    load_dotenv()
    
    # Check required environment variables
    required_vars = ['QWEN_API_KEY', 'MT5_PATH']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            print(f"✓ {var}: {'*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '***'}")
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("\nPlease add these to your .env file:")
        for var in missing_vars:
            if var == 'QWEN_API_KEY':
                print(f"{var}=your_qwen_api_key_here")
            elif var == 'MT5_PATH':
                print(f"{var}=C:\\Program Files\\MetaTrader 5\\terminal64.exe")
        return False
    
    print("✅ Environment setup complete")
    return True

def test_configuration():
    """Test account configuration"""
    print("\n🔍 Testing Account Configuration...")
    
    config_file = "config/accounts.json"
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        if 'accounts' not in config:
            print("❌ No 'accounts' section in configuration")
            return False
        
        accounts = config['accounts']
        if len(accounts) == 0:
            print("❌ No accounts configured")
            return False
        
        print(f"✓ Found {len(accounts)} configured accounts")
        
        for i, account in enumerate(accounts):
            account_id = account.get('account_id', f'account_{i}')
            server = account.get('server', 'Unknown')
            strategy = account.get('strategy', 'Unknown')
            mm = account.get('money_management', 'Unknown')
            
            print(f"  Account {i+1}: {account_id}")
            print(f"    Server: {server}")
            print(f"    Strategy: {strategy}")
            print(f"    Money Management: {mm}")
        
        print("✅ Account configuration valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in configuration file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\n🔍 Testing File Structure...")
    
    required_files = [
        "src/logging_system/logger.py",
        "src/account_management/account_manager.py",
        "src/account_management/models.py",
        "src/money_management/base_strategy.py",
        "src/money_management/fixed_volume.py",
        "src/money_management/percent_risk.py",
        "src/money_management/martingale.py",
        "src/money_management/anti_martingale.py",
        "src/strategies/base_strategy.py",
        "src/strategies/trend_following.py",
        "src/strategies/mean_reversion.py",
        "src/strategies/breakout.py",
        "src/ai_integration/qwen_client.py",
        "src/ai_integration/prompt_builder.py",
        "src/mt5_integration/mt5_client.py",
        "src/signal_generation/signal_generator.py",
        "src/trade_management/trade_manager.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        return False
    
    print("✅ All required files present")
    return True

def test_dependencies():
    """Test that all dependencies are installed"""
    print("\n🔍 Testing Dependencies...")
    
    required_packages = [
        ('MetaTrader5', 'MetaTrader5'),
        ('openai', 'openai'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('loguru', 'loguru'),
        ('schedule', 'schedule'),
        ('aiohttp', 'aiohttp'),
        ('python-dotenv', 'dotenv')
    ]

    missing_packages = []
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name}")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {missing_packages}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed")
    return True

def run_basic_system_test():
    """Run a basic system test without starting the full trading system"""
    print("\n🔍 Testing Basic System Components...")
    
    # Add src to path
    src_path = str(Path(__file__).parent / "src")
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
    
    try:
        # Test logging system
        from logging_system.logger import setup_logger, get_logger
        logger = setup_logger()
        logger.info("Basic system test started")
        print("✓ Logging system working")
        
        # Test that we can create the basic structure
        print("✓ Basic imports successful")
        
        print("✅ Basic system test passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic system test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AI-Driven Trading System - System Check")
    print("=" * 60)
    
    tests = [
        ("Environment Setup", test_environment),
        ("Account Configuration", test_configuration),
        ("File Structure", test_file_structure),
        ("Dependencies", test_dependencies),
        ("Basic System", run_basic_system_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n⚠️  {test_name} test failed")
        except Exception as e:
            print(f"\n❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! System is ready.")
        print("\n📋 Next Steps:")
        print("1. Ensure MetaTrader 5 is running")
        print("2. Add your Qwen API key to .env file")
        print("3. Test with demo account first")
        print("4. Run: python main.py")
        return True
    else:
        print(f"\n❌ {total - passed} tests failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
