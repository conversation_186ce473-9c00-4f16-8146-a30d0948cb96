"""
Account Management Data Models
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum
from datetime import datetime

class AccountStatus(Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    ERROR = "error"

@dataclass
class TradingAccount:
    """Trading account configuration"""
    account_id: str
    account_number: int
    server: str
    username: str
    password: str
    
    # Trading configuration
    strategy_type: str
    money_management_type: str
    symbols: List[Dict[str, str]]  # List of {"symbol": "EURUSD", "timeframe": "M15"}
    timeframes: List[str] = field(default_factory=list)  # Deprecated, kept for compatibility
    
    # UNIFIED: Money management and risk settings (standardized naming)
    money_management_settings: Dict[str, Any] = field(default_factory=dict)

    # DEPRECATED: Individual limit fields - now consolidated in money_management_settings
    # These are kept for backward compatibility but should use money_management_settings
    trading_enabled: bool = True
    
    # Status and metadata
    status: AccountStatus = AccountStatus.ACTIVE
    last_login: Optional[datetime] = None
    last_trade: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    # Performance tracking
    total_trades: int = 0
    winning_trades: int = 0
    total_profit: float = 0.0
    max_drawdown: float = 0.0

    @property
    def money_management_config(self) -> Dict[str, Any]:
        """Backward compatibility property for money_management_config"""
        return self.money_management_settings

    @money_management_config.setter
    def money_management_config(self, value: Dict[str, Any]):
        """Backward compatibility setter for money_management_config"""
        self.money_management_settings = value

    def get_risk_setting(self, key: str, default: Any = None) -> Any:
        """Get a risk management setting with standardized defaults"""
        # Standardized default values for all risk settings
        standard_defaults = {
            'risk_percent': 2.0,
            'max_daily_trades': 10,
            'max_open_positions': 5,
            'max_pending_orders': 10,
            'max_daily_loss': 5.0,
            'max_daily_loss_percent': None,  # If set, overrides max_daily_loss
            'max_drawdown_percent': 15.0,
            'max_consecutive_losses': 5,
            'max_risk_per_trade': 15.0,
            'max_risk_multiplier': 5.0
        }

        # First check money_management_settings
        if key in self.money_management_settings:
            return self.money_management_settings[key]

        # Then check provided default
        if default is not None:
            return default

        # Finally use standard default
        return standard_defaults.get(key, None)

    def validate_settings(self) -> List[str]:
        """Validate account settings and return list of issues"""
        issues = []

        # Check required fields
        if not self.account_id:
            issues.append("account_id is required")
        if not self.account_number:
            issues.append("account_number is required")
        if not self.server:
            issues.append("server is required")
        if not self.password:
            issues.append("password is required")

        # Check symbols configuration
        if not self.symbols:
            issues.append("At least one symbol must be configured")
        else:
            for i, symbol_config in enumerate(self.symbols):
                if not isinstance(symbol_config, dict):
                    issues.append(f"Symbol {i} must be a dictionary")
                    continue
                if 'symbol' not in symbol_config:
                    issues.append(f"Symbol {i} missing 'symbol' field")
                if 'timeframe' not in symbol_config:
                    issues.append(f"Symbol {i} missing 'timeframe' field")

        # Validate risk settings
        risk_percent = self.get_risk_setting('risk_percent')
        if risk_percent and (risk_percent <= 0 or risk_percent > 100):
            issues.append("risk_percent must be between 0 and 100")

        max_daily_trades = self.get_risk_setting('max_daily_trades')
        if max_daily_trades and max_daily_trades <= 0:
            issues.append("max_daily_trades must be positive")

        max_open_positions = self.get_risk_setting('max_open_positions')
        if max_open_positions and max_open_positions <= 0:
            issues.append("max_open_positions must be positive")

        return issues

@dataclass
class AccountGroup:
    """Group of accounts with same strategy and money management"""
    group_id: str
    strategy_type: str
    money_management_type: str
    accounts: List[str] = field(default_factory=list)
    last_ai_request: Optional[datetime] = None
    shared_signals: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class AccountBalance:
    """Account balance and margin information"""
    account_id: str
    balance: float
    equity: float
    margin: float
    free_margin: float
    margin_level: float
    currency: str
    leverage: int
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class AccountPerformance:
    """Account performance metrics"""
    account_id: str
    period_start: datetime
    period_end: datetime
    
    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Financial metrics
    total_profit: float
    gross_profit: float
    gross_loss: float
    profit_factor: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_percent: float
    sharpe_ratio: float
    
    # Trade analysis
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_trade_duration: float
