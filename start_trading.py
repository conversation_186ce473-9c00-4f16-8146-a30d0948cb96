#!/usr/bin/env python3
"""
DEPRECATED: Trading System Launcher (start_trading.py)
This entry point has been consolidated into trading_system_main.py

Please use: python trading_system_main.py
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Redirect to the unified entry point with prerequisite checking"""
    print("❌ DEPRECATED ENTRY POINT")
    print("=" * 50)
    print("This entry point (start_trading.py) has been deprecated.")
    print("All entry points have been consolidated for consistency.")
    print()
    print("✅ Please use the unified entry point:")
    print("   python trading_system_main.py")
    print()
    print("🔧 The unified entry point includes all prerequisite")
    print("   checking and provides better error handling.")
    print("=" * 50)

    # Check if the unified entry point exists
    unified_entry = Path(__file__).parent / "trading_system_main.py"
    if unified_entry.exists():
        print("✅ Unified entry point found: trading_system_main.py")
        print()
        print("🚀 Would you like to run the unified entry point now? (y/n)")

        try:
            response = input().lower().strip()
            if response in ['y', 'yes']:
                print("🔄 Launching unified trading system...")
                # Run the unified entry point
                result = subprocess.run([sys.executable, "trading_system_main.py"],
                                      cwd=Path(__file__).parent)
                return result.returncode == 0
            else:
                print("👋 Please run: python trading_system_main.py")
                return True
        except KeyboardInterrupt:
            print("\n👋 Cancelled by user")
            return True
    else:
        print("❌ Unified entry point not found!")
        return False

# Remove all old functionality - everything redirects to unified entry point
if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
