"""
AI Provider Configuration Validator
"""

import os
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum

from .base_ai_provider import AIProviderType, AIProviderConfig
from logging_system.logger import get_logger

logger = get_logger(__name__)


class ValidationResult:
    """Result of configuration validation"""
    
    def __init__(self, is_valid: bool, errors: List[str] = None, warnings: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def add_error(self, error: str):
        """Add validation error"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add validation warning"""
        self.warnings.append(warning)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'is_valid': self.is_valid,
            'errors': self.errors,
            'warnings': self.warnings
        }


class AIConfigValidator:
    """Validator for AI provider configurations"""
    
    def __init__(self):
        self.required_env_vars = {
            AIProviderType.QWEN: ['QWEN_API_KEY'],
            AIProviderType.OPENROUTER: ['OPENROUTER_API_KEY']
        }
        
        self.recommended_models = {
            AIProviderType.QWEN: [
                'qwen-max-2025-01-25',
                'qwen-max',
                'qwen-plus',
                'qwen-turbo'
            ],
            AIProviderType.OPENROUTER: [
                'anthropic/claude-3.5-sonnet',
                'anthropic/claude-3-haiku',
                'openai/gpt-4o',
                'openai/gpt-4o-mini'
            ]
        }
    
    def validate_environment(self) -> ValidationResult:
        """Validate environment configuration"""
        result = ValidationResult(True)
        
        # Check AI provider selection
        provider_name = os.getenv('AI_PROVIDER', 'qwen').lower()
        try:
            provider_type = AIProviderType(provider_name)
        except ValueError:
            result.add_error(f"Invalid AI_PROVIDER '{provider_name}'. Valid options: {[p.value for p in AIProviderType]}")
            return result
        
        # Validate provider-specific requirements
        self._validate_provider_config(provider_type, result)
        
        # Check for fallback providers
        self._validate_fallback_providers(result)
        
        # Validate common AI parameters
        self._validate_ai_parameters(result)
        
        return result
    
    def _validate_provider_config(self, provider_type: AIProviderType, result: ValidationResult):
        """Validate specific provider configuration"""
        
        # Check required environment variables
        required_vars = self.required_env_vars.get(provider_type, [])
        for var in required_vars:
            if not os.getenv(var):
                result.add_error(f"Missing required environment variable: {var}")
        
        # Provider-specific validation
        if provider_type == AIProviderType.QWEN:
            self._validate_qwen_config(result)
        elif provider_type == AIProviderType.OPENROUTER:
            self._validate_openrouter_config(result)
    
    def _validate_qwen_config(self, result: ValidationResult):
        """Validate Qwen-specific configuration"""
        api_key = os.getenv('QWEN_API_KEY')
        if api_key:
            if not api_key.startswith('sk-'):
                result.add_warning("Qwen API key should typically start with 'sk-'")
            if len(api_key) < 20:
                result.add_warning("Qwen API key seems unusually short")
        
        api_url = os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1')
        if not api_url.startswith('https://'):
            result.add_warning("Qwen API URL should use HTTPS for security")
        
        model = os.getenv('AI_MODEL', 'qwen-max-2025-01-25')
        recommended = self.recommended_models[AIProviderType.QWEN]
        if model not in recommended:
            result.add_warning(f"Model '{model}' not in recommended list: {recommended}")
    
    def _validate_openrouter_config(self, result: ValidationResult):
        """Validate OpenRouter-specific configuration"""
        api_key = os.getenv('OPENROUTER_API_KEY')
        if api_key:
            if not api_key.startswith('sk-'):
                result.add_warning("OpenRouter API key should typically start with 'sk-'")
            if len(api_key) < 20:
                result.add_warning("OpenRouter API key seems unusually short")
        
        api_url = os.getenv('OPENROUTER_API_URL', 'https://openrouter.ai/api/v1')
        if not api_url.startswith('https://'):
            result.add_warning("OpenRouter API URL should use HTTPS for security")
        
        model = os.getenv('OPENROUTER_MODEL', 'anthropic/claude-3.5-sonnet')
        recommended = self.recommended_models[AIProviderType.OPENROUTER]
        if model not in recommended:
            result.add_warning(f"Model '{model}' not in recommended list: {recommended}")
        
        # Check OpenRouter-specific parameters
        site_url = os.getenv('OPENROUTER_SITE_URL')
        if not site_url:
            result.add_warning("OPENROUTER_SITE_URL not set - this may be required for some models")
        
        app_name = os.getenv('OPENROUTER_APP_NAME')
        if not app_name:
            result.add_warning("OPENROUTER_APP_NAME not set - this helps with API usage tracking")
    
    def _validate_fallback_providers(self, result: ValidationResult):
        """Validate fallback provider availability"""
        available_providers = []
        
        # Check Qwen availability
        if os.getenv('QWEN_API_KEY'):
            available_providers.append('qwen')
        
        # Check OpenRouter availability
        if os.getenv('OPENROUTER_API_KEY'):
            available_providers.append('openrouter')
        
        if len(available_providers) == 0:
            result.add_error("No AI providers configured - at least one provider is required")
        elif len(available_providers) == 1:
            result.add_warning(f"Only one AI provider configured ({available_providers[0]}) - consider adding a fallback provider")
        else:
            logger.info(f"Multiple AI providers available for fallback: {available_providers}")
    
    def _validate_ai_parameters(self, result: ValidationResult):
        """Validate common AI parameters"""
        
        # Validate timeout
        try:
            timeout = int(os.getenv('AI_TIMEOUT', '30'))
            if timeout < 5:
                result.add_warning("AI_TIMEOUT is very low - may cause frequent timeouts")
            elif timeout > 120:
                result.add_warning("AI_TIMEOUT is very high - may cause long waits")
        except ValueError:
            result.add_error("AI_TIMEOUT must be a valid integer")
        
        # Validate temperature
        try:
            temperature = float(os.getenv('AI_TEMPERATURE', '0.3'))
            if temperature < 0 or temperature > 2:
                result.add_error("AI_TEMPERATURE must be between 0 and 2")
            elif temperature > 1:
                result.add_warning("AI_TEMPERATURE > 1 may produce inconsistent trading decisions")
        except ValueError:
            result.add_error("AI_TEMPERATURE must be a valid float")
        
        # Validate max_tokens
        try:
            max_tokens = int(os.getenv('AI_MAX_TOKENS', '2000'))
            if max_tokens < 100:
                result.add_warning("AI_MAX_TOKENS is very low - may truncate responses")
            elif max_tokens > 8000:
                result.add_warning("AI_MAX_TOKENS is very high - may increase costs")
        except ValueError:
            result.add_error("AI_MAX_TOKENS must be a valid integer")
        
        # Validate top_p
        try:
            top_p = float(os.getenv('AI_TOP_P', '0.8'))
            if top_p <= 0 or top_p > 1:
                result.add_error("AI_TOP_P must be between 0 and 1")
        except ValueError:
            result.add_error("AI_TOP_P must be a valid float")
        
        # Validate concurrent requests
        try:
            max_concurrent = int(os.getenv('MAX_CONCURRENT_REQUESTS', '5'))
            if max_concurrent < 1:
                result.add_error("MAX_CONCURRENT_REQUESTS must be at least 1")
            elif max_concurrent > 20:
                result.add_warning("MAX_CONCURRENT_REQUESTS is very high - may hit API rate limits")
        except ValueError:
            result.add_error("MAX_CONCURRENT_REQUESTS must be a valid integer")
    
    def validate_config(self, config: AIProviderConfig) -> ValidationResult:
        """Validate a specific AI provider configuration"""
        result = ValidationResult(True)
        
        # Validate API key
        if not config.api_key:
            result.add_error("API key is required")
        elif len(config.api_key) < 10:
            result.add_warning("API key seems unusually short")
        
        # Validate API URL
        if not config.api_url:
            result.add_error("API URL is required")
        elif not config.api_url.startswith('https://'):
            result.add_warning("API URL should use HTTPS for security")
        
        # Validate model
        if not config.model:
            result.add_error("Model name is required")
        else:
            recommended = self.recommended_models.get(config.provider_type, [])
            if recommended and config.model not in recommended:
                result.add_warning(f"Model '{config.model}' not in recommended list: {recommended}")
        
        # Validate parameters
        if config.temperature < 0 or config.temperature > 2:
            result.add_error("Temperature must be between 0 and 2")
        
        if config.max_tokens < 1:
            result.add_error("Max tokens must be positive")
        
        if config.top_p <= 0 or config.top_p > 1:
            result.add_error("Top-p must be between 0 and 1")
        
        if config.max_concurrent_requests < 1:
            result.add_error("Max concurrent requests must be at least 1")
        
        if config.timeout < 1:
            result.add_error("Timeout must be at least 1 second")
        
        return result
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get summary of current AI configuration"""
        provider_name = os.getenv('AI_PROVIDER', 'qwen')
        
        summary = {
            'selected_provider': provider_name,
            'available_providers': [],
            'configuration': {},
            'validation': self.validate_environment().to_dict()
        }
        
        # Check available providers
        if os.getenv('QWEN_API_KEY'):
            summary['available_providers'].append('qwen')
        if os.getenv('OPENROUTER_API_KEY'):
            summary['available_providers'].append('openrouter')
        
        # Get configuration details (without sensitive info)
        summary['configuration'] = {
            'timeout': os.getenv('AI_TIMEOUT', '30'),
            'temperature': os.getenv('AI_TEMPERATURE', '0.3'),
            'max_tokens': os.getenv('AI_MAX_TOKENS', '2000'),
            'top_p': os.getenv('AI_TOP_P', '0.8'),
            'max_concurrent_requests': os.getenv('MAX_CONCURRENT_REQUESTS', '5')
        }
        
        if provider_name == 'qwen':
            summary['configuration']['model'] = os.getenv('AI_MODEL', 'qwen-max-2025-01-25')
            summary['configuration']['api_url'] = os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1')
        elif provider_name == 'openrouter':
            summary['configuration']['model'] = os.getenv('OPENROUTER_MODEL', 'anthropic/claude-3.5-sonnet')
            summary['configuration']['api_url'] = os.getenv('OPENROUTER_API_URL', 'https://openrouter.ai/api/v1')
        
        return summary


# Global validator instance
ai_config_validator = AIConfigValidator()
