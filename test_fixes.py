#!/usr/bin/env python3
"""
Test the fixes we implemented
"""

import sys
import os

# Add src to path
sys.path.append('src')

def test_imports():
    """Test that all our modified files can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from ai_error_handler import AI<PERSON>rror<PERSON><PERSON><PERSON>, ErrorContext, ErrorResolution
        print("✅ AI Error Handler imports successful")
    except Exception as e:
        print(f"❌ AI Error Handler import failed: {e}")
        return False
    
    try:
        from strategies.trend_following import TrendFollowingStrategy
        print("✅ Trend Following Strategy imports successful")
    except Exception as e:
        print(f"❌ Trend Following Strategy import failed: {e}")
        return False
    
    try:
        from mt5_integration.mt5_client import MT5Client
        print("✅ MT5 Client imports successful")
    except Exception as e:
        print(f"❌ MT5 Client import failed: {e}")
        return False
    
    try:
        from signal_generation.signal_generator import SignalGenerator
        print("✅ Signal Generator imports successful")
    except Exception as e:
        print(f"❌ Signal Generator import failed: {e}")
        return False
    
    return True

def test_confidence_threshold():
    """Test that the confidence threshold was updated correctly"""
    print("\n🔍 Testing confidence threshold...")
    
    try:
        from strategies.trend_following import TrendFollowingStrategy
        from strategies.base_strategy import TradingSignal, MarketData

        # Create strategy with required config
        config = {
            'magic_number': 12345,
            'strategy_type': 'trend_following'
        }
        strategy = TrendFollowingStrategy(config)
        
        # Create a test signal with 0.65 confidence (should pass)
        signal = TradingSignal(
            action="BUY",
            confidence=0.65,
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1100,
            reasoning="Test signal",
            risk_level="MEDIUM"
        )
        
        # Create test market data
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="M15",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001,
            pip_value=10.0,
            min_volume=0.01,
            max_volume=100.0
        )
        
        # Test validation
        result = strategy.validate_signal(signal, market_data)
        if result:
            print("✅ Confidence threshold test passed (0.65 confidence accepted)")
        else:
            print("❌ Confidence threshold test failed (0.65 confidence rejected)")
            return False
        
        # Test with low confidence (should fail)
        signal.confidence = 0.50
        result = strategy.validate_signal(signal, market_data)
        if not result:
            print("✅ Low confidence rejection test passed (0.50 confidence rejected)")
        else:
            print("❌ Low confidence rejection test failed (0.50 confidence accepted)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Confidence threshold test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 TESTING TRADING SYSTEM FIXES")
    print("=" * 50)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test confidence threshold
    if not test_confidence_threshold():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL TESTS PASSED")
        print("\nFixes implemented:")
        print("1. ✅ Trend following confidence threshold set to 60%")
        print("2. ✅ AI error handler for MT5 errors created")
        print("3. ✅ Enhanced logging for signal processing added")
        print("4. ✅ Optimized trend following prompt for higher confidence")
    else:
        print("❌ SOME TESTS FAILED")
    
    return success

if __name__ == "__main__":
    main()
