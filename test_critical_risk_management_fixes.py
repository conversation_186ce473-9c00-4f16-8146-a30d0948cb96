#!/usr/bin/env python3
"""
Test the critical risk management fixes:
1. Position limits enforcement (max_open + max_pending rule)
2. Maximum drawdown checking
3. Daily trade counter incrementation
4. Account settings override (not using defaults when configured)
"""

import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class MockMT5Client:
    """Mock MT5 client for testing"""
    def __init__(self, positions=2, pending_orders=2, balance=75.84, equity=60.0):
        self.current_account = True
        self.positions_count = positions
        self.pending_orders_count = pending_orders
        self.balance = balance
        self.equity = equity
        
    def get_account_info(self):
        class MockBalance:
            def __init__(self, balance, equity):
                self.balance = balance
                self.equity = equity
        return MockBalance(self.balance, self.equity)
    
    def get_positions(self):
        # Return mock positions
        positions = []
        for i in range(self.positions_count):
            positions.append({'symbol': 'EURUSD', 'magic': 0, 'volume': 0.01})
        return positions
    
    def get_pending_orders(self):
        # Return mock pending orders
        orders = []
        for i in range(self.pending_orders_count):
            orders.append({'symbol': 'EURUSD', 'magic': 0, 'volume': 0.01})
        return orders

async def test_position_limits_enforcement():
    """Test that position limits are properly enforced"""
    print("=" * 60)
    print("TESTING POSITION LIMITS ENFORCEMENT")
    print("=" * 60)
    
    try:
        from signal_generation.signal_generator import SignalGenerator
        from account_management.account_manager import AccountManager
        
        # Create signal generator
        account_manager = AccountManager()
        account_manager.load_accounts()
        signal_gen = SignalGenerator(account_manager)
        
        # Get the "Mine" account which has strict limits
        mine_account = None
        for account in account_manager.accounts.values():
            if account.account_id == "Mine":
                mine_account = account
                break
        
        if not mine_account:
            print("❌ Could not find 'Mine' account")
            return False
        
        # Create account dict for testing - match the structure expected by _get_account_risk_settings
        account_dict = {
            'account_id': mine_account.account_id,
            'money_management_config': mine_account.money_management_config,
            # Also add the individual settings at the top level for compatibility
            **mine_account.money_management_config
        }
        
        print(f"Account: {mine_account.account_id}")
        print(f"Max Open Positions: {mine_account.money_management_config['max_open_positions']}")
        print(f"Max Pending Orders: {mine_account.money_management_config['max_pending_orders']}")
        print(f"Total Allowed: {mine_account.money_management_config['max_open_positions'] + mine_account.money_management_config['max_pending_orders']}")
        
        # Test scenarios
        test_scenarios = [
            {
                "description": "Within limits (0 open, 0 pending, adding 1)",
                "existing_positions": 0,
                "existing_pending": 0,
                "is_multiple_tp": False,
                "should_allow": True
            },
            {
                "description": "At open position limit (1 open, 0 pending, adding 1)",
                "existing_positions": 1,
                "existing_pending": 0,
                "is_multiple_tp": False,
                "should_allow": False  # Would exceed max_open_positions (1)
            },
            {
                "description": "At total limit (0 open, 3 pending, adding 1)",
                "existing_positions": 0,
                "existing_pending": 3,
                "is_multiple_tp": False,
                "should_allow": False  # Would exceed total limit (3)
            },
            {
                "description": "Multiple TP exceeds pending limit (0 open, 1 pending, adding 3)",
                "existing_positions": 0,
                "existing_pending": 1,
                "is_multiple_tp": True,
                "should_allow": False  # 1 + 3 = 4 > max_pending_orders (2)
            }
        ]
        
        all_passed = True
        
        for scenario in test_scenarios:
            print(f"\n🧪 TEST: {scenario['description']}")
            
            # Mock MT5 client with specific counts
            signal_gen.mt5_client = MockMT5Client(
                positions=scenario['existing_positions'],
                pending_orders=scenario['existing_pending']
            )
            
            # Get risk settings
            risk_settings = signal_gen._get_account_risk_settings(account_dict)
            
            # Test position limits
            can_trade = signal_gen._check_position_limits(account_dict, risk_settings, scenario['is_multiple_tp'])
            
            print(f"  Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
            print(f"  Actual: {'ALLOW' if can_trade else 'BLOCK'}")
            
            is_correct = can_trade == scenario['should_allow']
            print(f"  Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
            
            if not is_correct:
                all_passed = False
        
        print(f"\n📊 Position Limits Test: {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED'}")
        return all_passed
        
    except Exception as e:
        print(f"❌ Position limits test failed: {e}")
        return False

async def test_drawdown_enforcement():
    """Test that maximum drawdown is properly enforced"""
    print("\n" + "=" * 60)
    print("TESTING MAXIMUM DRAWDOWN ENFORCEMENT")
    print("=" * 60)
    
    try:
        from signal_generation.signal_generator import SignalGenerator
        from account_management.account_manager import AccountManager
        
        # Create signal generator
        account_manager = AccountManager()
        account_manager.load_accounts()
        signal_gen = SignalGenerator(account_manager)
        
        # Get account
        mine_account = list(account_manager.accounts.values())[0]
        account_dict = {
            'account_id': mine_account.account_id,
            'money_management_config': mine_account.money_management_config,
            # Also add the individual settings at the top level for compatibility
            **mine_account.money_management_config
        }
        
        # Test scenarios
        drawdown_scenarios = [
            {
                "description": "No drawdown (equity >= balance)",
                "balance": 100.0,
                "equity": 105.0,
                "should_allow": True
            },
            {
                "description": "Small drawdown (5%)",
                "balance": 100.0,
                "equity": 95.0,
                "should_allow": True
            },
            {
                "description": "At drawdown limit (15%)",
                "balance": 100.0,
                "equity": 85.0,
                "should_allow": False
            },
            {
                "description": "Exceeds drawdown limit (20%)",
                "balance": 100.0,
                "equity": 80.0,
                "should_allow": False
            }
        ]
        
        all_passed = True
        
        for scenario in drawdown_scenarios:
            print(f"\n🧪 TEST: {scenario['description']}")
            
            # Mock MT5 client with specific balance/equity
            signal_gen.mt5_client = MockMT5Client(
                balance=scenario['balance'],
                equity=scenario['equity']
            )
            
            # Get risk settings
            risk_settings = signal_gen._get_account_risk_settings(account_dict)
            
            # Test drawdown check
            can_trade = signal_gen._check_maximum_drawdown(account_dict['account_id'], risk_settings)
            
            drawdown_percent = ((scenario['balance'] - scenario['equity']) / scenario['balance']) * 100 if scenario['equity'] < scenario['balance'] else 0
            
            print(f"  Balance: ${scenario['balance']:.2f}")
            print(f"  Equity: ${scenario['equity']:.2f}")
            print(f"  Drawdown: {drawdown_percent:.1f}%")
            print(f"  Max Allowed: {risk_settings.get('max_drawdown_percent', 15.0):.1f}%")
            print(f"  Expected: {'ALLOW' if scenario['should_allow'] else 'BLOCK'}")
            print(f"  Actual: {'ALLOW' if can_trade else 'BLOCK'}")
            
            is_correct = can_trade == scenario['should_allow']
            print(f"  Result: {'✅ PASS' if is_correct else '❌ FAIL'}")
            
            if not is_correct:
                all_passed = False
        
        print(f"\n📊 Drawdown Test: {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED'}")
        return all_passed
        
    except Exception as e:
        print(f"❌ Drawdown test failed: {e}")
        return False

async def test_daily_trade_counter():
    """Test that daily trade counter is properly incremented"""
    print("\n" + "=" * 60)
    print("TESTING DAILY TRADE COUNTER")
    print("=" * 60)
    
    try:
        from signal_generation.signal_generator import SignalGenerator
        from account_management.account_manager import AccountManager
        
        # Create signal generator
        account_manager = AccountManager()
        account_manager.load_accounts()
        signal_gen = SignalGenerator(account_manager)
        
        account_id = "test_account"
        
        # Clear any existing daily stats
        today = datetime.now().date()
        daily_key = f"{account_id}_{today}"
        signal_gen.daily_trades[daily_key] = 0
        
        print(f"Account: {account_id}")
        print(f"Initial trade count: {signal_gen.daily_trades[daily_key]}")
        
        # Test single TP trade
        signal_gen._update_daily_stats(account_id, pnl=0.0, is_multiple_tp=False)
        count_after_single = signal_gen.daily_trades[daily_key]
        print(f"After single TP trade: {count_after_single}")
        
        # Test multiple TP trade (should still count as 1)
        signal_gen._update_daily_stats(account_id, pnl=0.0, is_multiple_tp=True)
        count_after_multi = signal_gen.daily_trades[daily_key]
        print(f"After multiple TP trade: {count_after_multi}")
        
        # Verify counts
        single_correct = count_after_single == 1
        multi_correct = count_after_multi == 2
        
        print(f"\nSingle TP increment: {'✅ PASS' if single_correct else '❌ FAIL'}")
        print(f"Multiple TP increment: {'✅ PASS' if multi_correct else '❌ FAIL'}")
        
        all_passed = single_correct and multi_correct
        print(f"\n📊 Daily Counter Test: {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED'}")
        return all_passed
        
    except Exception as e:
        print(f"❌ Daily counter test failed: {e}")
        return False

async def test_account_settings_override():
    """Test that account settings properly override defaults"""
    print("\n" + "=" * 60)
    print("TESTING ACCOUNT SETTINGS OVERRIDE")
    print("=" * 60)
    
    try:
        from account_management.account_manager import AccountManager
        
        # Load accounts
        account_manager = AccountManager()
        account_manager.load_accounts()
        
        # Check the "Mine" account which has specific settings
        mine_account = None
        for account in account_manager.accounts.values():
            if account.account_id == "Mine":
                mine_account = account
                break
        
        if not mine_account:
            print("❌ Could not find 'Mine' account")
            return False
        
        # Check that configured values are used, not defaults
        mm_config = mine_account.money_management_config
        
        print(f"Account: {mine_account.account_id}")
        print(f"Risk Percent: {mm_config.get('risk_percent')} (should be 0.5, not default 2.0)")
        print(f"Max Daily Trades: {mm_config.get('max_daily_trades')} (should be 3, not default 10)")
        print(f"Max Open Positions: {mm_config.get('max_open_positions')} (should be 1, not default 5)")
        print(f"Max Pending Orders: {mm_config.get('max_pending_orders')} (should be 2, not default 10)")
        
        # Verify values match configuration, not defaults
        risk_correct = mm_config.get('risk_percent') == 0.5
        trades_correct = mm_config.get('max_daily_trades') == 3
        positions_correct = mm_config.get('max_open_positions') == 1
        pending_correct = mm_config.get('max_pending_orders') == 2
        
        print(f"\nRisk percent override: {'✅ PASS' if risk_correct else '❌ FAIL'}")
        print(f"Max daily trades override: {'✅ PASS' if trades_correct else '❌ FAIL'}")
        print(f"Max open positions override: {'✅ PASS' if positions_correct else '❌ FAIL'}")
        print(f"Max pending orders override: {'✅ PASS' if pending_correct else '❌ FAIL'}")
        
        all_passed = risk_correct and trades_correct and positions_correct and pending_correct
        print(f"\n📊 Settings Override Test: {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED'}")
        return all_passed
        
    except Exception as e:
        print(f"❌ Settings override test failed: {e}")
        return False

async def main():
    """Run all critical risk management tests"""
    print("🚨 CRITICAL RISK MANAGEMENT FIXES VERIFICATION")
    print("=" * 80)
    
    tests = [
        test_position_limits_enforcement,
        test_drawdown_enforcement,
        test_daily_trade_counter,
        test_account_settings_override
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 80)
    print("CRITICAL RISK MANAGEMENT VERIFICATION SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL CRITICAL RISK MANAGEMENT FIXES VERIFIED!")
        print("✅ Position limits are properly enforced")
        print("✅ Maximum drawdown is checked and enforced")
        print("✅ Daily trade counter increments correctly")
        print("✅ Account settings override defaults properly")
        print("\n🚀 SYSTEM IS NOW SAFE FOR PRODUCTION!")
    else:
        print("⚠️ Some critical fixes need attention")
        print("❌ DO NOT DEPLOY TO PRODUCTION UNTIL ALL TESTS PASS")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
