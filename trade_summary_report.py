#!/usr/bin/env python3
"""
Generate a comprehensive trade management summary report
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mt5_integration.mt5_client import MT5Client
from account_management.models import TradingAccount
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

def generate_trade_summary():
    """Generate comprehensive trade summary"""
    print("📊 COMPREHENSIVE TRADE MANAGEMENT SUMMARY")
    print("=" * 60)
    print(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    mt5_client = MT5Client()
    
    try:
        # Initialize MT5
        if not mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        # Create account object
        account = TradingAccount(
            account_id="demo1",
            account_number=********,
            server="RoboForex-ECN",
            username="********",
            password="Daadb123",
            strategy_type="scalping",
            money_management_type="fixed_lot",
            symbols=["EURUSD", "GBPUSD", "USDJPY"],
            timeframes=["M15"]
        )
        
        if not mt5_client.login(account):
            print("❌ Failed to login")
            return False
        
        print("✅ Connected to MT5 and logged in successfully")
        
        # Get account information
        account_info = mt5_client.get_account_info()
        print(f"\n💰 ACCOUNT STATUS:")
        print(f"   Account Number: {account.account_number}")
        print(f"   Server: {account.server}")
        print(f"   Balance: ${account_info.balance:.2f}")
        print(f"   Equity: ${account_info.equity:.2f}")
        print(f"   Margin Used: ${account_info.margin:.2f}")
        print(f"   Free Margin: ${account_info.free_margin:.2f}")
        print(f"   Margin Level: {account_info.margin_level:.1f}%")
        print(f"   Currency: {account_info.currency}")
        print(f"   Leverage: 1:{account_info.leverage}")
        
        # Calculate floating P&L
        floating_pl = account_info.equity - account_info.balance
        print(f"   Floating P&L: ${floating_pl:.2f}")
        
        # Get current positions
        positions = mt5_client.get_positions()
        print(f"\n📈 OPEN POSITIONS ANALYSIS ({len(positions)} positions):")
        print("-" * 50)
        
        total_position_value = 0
        total_profit = 0
        
        if positions:
            for i, pos in enumerate(positions, 1):
                profit_status = "🟢" if pos['profit'] > 0 else "🔴" if pos['profit'] < 0 else "⚪"
                
                # Get current market data for analysis
                market_data = mt5_client.get_market_data(pos['symbol'], "M15", 5)
                current_price = market_data['current_price'] if market_data else pos.get('price_current', 0)
                
                # Calculate position metrics
                entry_price = pos['price_open']
                if pos['type'] == 'BUY':
                    pips = (current_price - entry_price) / (0.01 if 'JPY' in pos['symbol'] else 0.0001)
                else:
                    pips = (entry_price - current_price) / (0.01 if 'JPY' in pos['symbol'] else 0.0001)
                
                position_value = pos['volume'] * current_price * (100000 if 'JPY' not in pos['symbol'] else 1000)
                total_position_value += position_value
                total_profit += pos['profit']
                
                print(f"\n{i}. {pos['symbol']} - {pos['type']} Position")
                print(f"   Volume: {pos['volume']} lots")
                print(f"   Entry Price: {entry_price:.5f}")
                print(f"   Current Price: {current_price:.5f}")
                print(f"   Pips: {pips:+.1f}")
                print(f"   P&L: {profit_status} ${pos['profit']:.2f}")
                print(f"   Stop Loss: {pos.get('sl', 0):.5f}")
                print(f"   Take Profit: {pos.get('tp', 0):.5f}")
                print(f"   Position Value: ${position_value:,.2f}")
                
                # Trade management recommendations
                print(f"   📋 MANAGEMENT ANALYSIS:")
                if pos['profit'] > 0:
                    if pips > 20:
                        print(f"      ✅ Position in good profit (+{pips:.1f} pips)")
                        print(f"      💡 Consider trailing stop loss to lock in profits")
                    else:
                        print(f"      ⚪ Small profit (+{pips:.1f} pips)")
                        print(f"      💡 Monitor for further movement")
                elif pos['profit'] < 0:
                    if abs(pips) > 30:
                        print(f"      ⚠️ Significant loss ({pips:.1f} pips)")
                        print(f"      💡 Review stop loss and consider risk management")
                    else:
                        print(f"      🔴 Minor loss ({pips:.1f} pips)")
                        print(f"      💡 Monitor for reversal signals")
                else:
                    print(f"      ⚪ Position at breakeven")
                    print(f"      💡 Consider setting breakeven stop loss")
        else:
            print("   No open positions")
        
        # Get pending orders
        orders = mt5_client.get_pending_orders()
        print(f"\n📋 PENDING ORDERS ANALYSIS ({len(orders)} orders):")
        print("-" * 50)
        
        if orders:
            order_types = {2: "BUY_LIMIT", 3: "SELL_LIMIT", 4: "BUY_STOP", 5: "SELL_STOP"}
            
            for i, order in enumerate(orders, 1):
                order_type = order_types.get(order['type'], f"TYPE_{order['type']}")
                
                # Get current market data for analysis
                market_data = mt5_client.get_market_data(order['symbol'], "M15", 5)
                current_price = market_data['current_price'] if market_data else 0
                
                # Calculate distance from current price
                order_price = order['price_open']
                if current_price > 0:
                    distance_pips = abs(order_price - current_price) / (0.01 if 'JPY' in order['symbol'] else 0.0001)
                else:
                    distance_pips = 0
                
                # Calculate order age
                order_time = order.get('time_setup', datetime.now().timestamp())
                if isinstance(order_time, datetime):
                    order_age_hours = (datetime.now() - order_time).total_seconds() / 3600
                elif isinstance(order_time, (int, float)):
                    order_age_hours = (datetime.now().timestamp() - order_time) / 3600
                else:
                    order_age_hours = 0
                
                print(f"\n{i}. {order['symbol']} - {order_type}")
                print(f"   Volume: {order['volume']} lots")
                print(f"   Order Price: {order_price:.5f}")
                print(f"   Current Price: {current_price:.5f}")
                print(f"   Distance: {distance_pips:.1f} pips")
                print(f"   Stop Loss: {order.get('sl', 0):.5f}")
                print(f"   Take Profit: {order.get('tp', 0):.5f}")
                print(f"   Age: {order_age_hours:.1f} hours")
                print(f"   Ticket: {order['ticket']}")
                
                # Order management recommendations
                print(f"   📋 MANAGEMENT ANALYSIS:")
                if distance_pips > 100:
                    print(f"      ⚠️ Order far from market ({distance_pips:.1f} pips)")
                    print(f"      💡 Consider adjusting price closer to market")
                elif distance_pips < 10:
                    print(f"      ⚡ Order close to market ({distance_pips:.1f} pips)")
                    print(f"      💡 May trigger soon - monitor closely")
                else:
                    print(f"      ⚪ Order at reasonable distance ({distance_pips:.1f} pips)")
                
                if order_age_hours > 24:
                    print(f"      🕐 Old order ({order_age_hours:.1f} hours)")
                    print(f"      💡 Consider canceling if market conditions changed")
        else:
            print("   No pending orders")
        
        # Summary and recommendations
        print(f"\n📊 OVERALL PORTFOLIO SUMMARY:")
        print("-" * 50)
        print(f"Total Open Positions: {len(positions)}")
        print(f"Total Pending Orders: {len(orders)}")
        print(f"Total Position Value: ${total_position_value:,.2f}")
        print(f"Total Floating P&L: ${total_profit:.2f}")
        print(f"Account Utilization: {(account_info.margin / account_info.equity * 100):.1f}%")
        
        print(f"\n🎯 TRADE MANAGEMENT RECOMMENDATIONS:")
        print("-" * 50)
        
        if len(positions) > 0:
            avg_profit = total_profit / len(positions)
            if avg_profit > 0:
                print("✅ Overall positions are profitable")
                print("💡 Consider implementing trailing stops to protect profits")
            else:
                print("⚠️ Overall positions are in loss")
                print("💡 Review risk management and consider position sizing")
        
        if len(orders) > 3:
            print("⚠️ Many pending orders active")
            print("💡 Consider consolidating or canceling outdated orders")
        
        if account_info.margin_level < 200:
            print("🚨 Low margin level - high risk")
            print("💡 Consider reducing position sizes or closing losing trades")
        elif account_info.margin_level > 1000:
            print("✅ Healthy margin level")
            print("💡 Good risk management - maintain current approach")
        
        print(f"\n🤖 AI TRADE MANAGEMENT STATUS:")
        print("-" * 50)
        print("✅ Pending order management functions implemented")
        print("✅ Position management functions active")
        print("✅ Weekend trading enabled for testing")
        print("✅ AI can now modify/cancel pending orders")
        print("✅ AI can modify position SL/TP or close positions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating summary: {e}")
        return False
        
    finally:
        mt5_client.shutdown()

def main():
    """Main function"""
    success = generate_trade_summary()
    
    if success:
        print(f"\n🎉 TRADE SUMMARY COMPLETED SUCCESSFULLY")
        print(f"\nThe trading system now has full pending order management capabilities.")
        print(f"AI can analyze and make decisions about both open positions and pending orders.")
    else:
        print(f"\n❌ TRADE SUMMARY FAILED")
    
    return success

if __name__ == "__main__":
    main()
