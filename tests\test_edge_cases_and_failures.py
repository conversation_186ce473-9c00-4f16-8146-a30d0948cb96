#!/usr/bin/env python3
"""
Comprehensive Edge Cases and Failure Scenario Tests
Tests edge cases and failure scenarios with full coverage
"""

import unittest
import asyncio
import sys
import os
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all components
from strategies.base_strategy import MarketData, TradingSignal, StrategyType
from strategies.trend_following import TrendFollowingStrategy
from money_management.base_strategy import AccountInfo, TradeParameters, MoneyManagementType
from money_management.percent_risk import PercentRiskStrategy
from money_management.martingale import MartingaleStrategy
from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import PromptBuilder


class TestMarketDataEdgeCases(unittest.TestCase):
    """Test edge cases for market data handling"""
    
    def test_market_data_empty_candles(self):
        """Test MarketData with empty candles list"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],  # Empty candles
            current_price=1.1000,
            spread=1.5,
            volume=0,  # Zero volume
            volatility=0.0  # Zero volatility
        )
        
        self.assertEqual(len(market_data.candles), 0)
        self.assertEqual(market_data.volume, 0)
        self.assertEqual(market_data.volatility, 0.0)
    
    def test_market_data_extreme_values(self):
        """Test MarketData with extreme values"""
        market_data = MarketData(
            symbol="XAUUSD",  # Gold
            timeframe="M1",
            candles=[],
            current_price=2500.50,  # High price
            spread=50.0,  # Very wide spread
            volume=1000000,  # Very high volume
            volatility=0.1,  # Very high volatility
            pip_size=0.01,  # Gold pip size
            pip_value=1000.0  # High pip value
        )
        
        self.assertEqual(market_data.current_price, 2500.50)
        self.assertEqual(market_data.spread, 50.0)
        self.assertEqual(market_data.pip_size, 0.01)
    
    def test_market_data_invalid_timeframe(self):
        """Test MarketData with unusual timeframe"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="INVALID_TF",  # Invalid timeframe
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        self.assertEqual(market_data.timeframe, "INVALID_TF")


class TestAccountInfoEdgeCases(unittest.TestCase):
    """Test edge cases for account information"""
    
    def test_account_info_zero_balance(self):
        """Test AccountInfo with zero balance"""
        account_info = AccountInfo(
            balance=0.0,  # Zero balance
            equity=0.0,
            margin=0.0,
            free_margin=0.0,
            margin_level=0.0,
            currency="USD",
            leverage=100
        )
        
        self.assertEqual(account_info.balance, 0.0)
        self.assertEqual(account_info.equity, 0.0)
    
    def test_account_info_negative_balance(self):
        """Test AccountInfo with negative balance"""
        account_info = AccountInfo(
            balance=-1000.0,  # Negative balance
            equity=-1000.0,
            margin=0.0,
            free_margin=-1000.0,
            margin_level=0.0,
            currency="USD",
            leverage=100
        )
        
        self.assertEqual(account_info.balance, -1000.0)
        self.assertEqual(account_info.equity, -1000.0)
    
    def test_account_info_extreme_leverage(self):
        """Test AccountInfo with extreme leverage"""
        account_info = AccountInfo(
            balance=1000.0,
            equity=1000.0,
            margin=0.0,
            free_margin=1000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=2000  # Very high leverage
        )
        
        self.assertEqual(account_info.leverage, 2000)


class TestMoneyManagementEdgeCases(unittest.TestCase):
    """Test edge cases for money management strategies"""
    
    def setUp(self):
        """Set up test data"""
        self.market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
    
    def test_percent_risk_zero_balance(self):
        """Test PercentRiskStrategy with zero balance"""
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        account_info = AccountInfo(
            balance=0.0,  # Zero balance
            equity=0.0,
            margin=0.0,
            free_margin=0.0,
            margin_level=0.0,
            currency="USD",
            leverage=100
        )
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],
            market_data=self.market_data
        )
        
        # Should return minimum volume when balance is zero
        self.assertEqual(trade_params.volume, 0.01)
        self.assertAlmostEqual(trade_params.risk_amount, 0.0, places=2)
    
    def test_percent_risk_very_small_balance(self):
        """Test PercentRiskStrategy with very small balance"""
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        account_info = AccountInfo(
            balance=1.0,  # Very small balance
            equity=1.0,
            margin=0.0,
            free_margin=1.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],
            market_data=self.market_data
        )
        
        # Should return minimum volume
        self.assertEqual(trade_params.volume, 0.01)
        self.assertGreater(trade_params.risk_amount, 0)
    
    def test_percent_risk_no_stop_loss(self):
        """Test PercentRiskStrategy with no stop loss"""
        strategy = PercentRiskStrategy({'risk_percent': 2.0})
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=None,  # No stop loss
            trade_history=[],
            market_data=self.market_data
        )
        
        self.assertIsNone(trade_params.stop_loss)
        self.assertEqual(trade_params.confidence_level, 0.3)  # Lower confidence
    
    def test_martingale_extreme_losses(self):
        """Test MartingaleStrategy with extreme consecutive losses"""
        strategy = MartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 8})
        account_info = AccountInfo(
            balance=1000.0,
            equity=1000.0,
            margin=100.0,
            free_margin=900.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        # Create 10 consecutive losses (more than max multiplier allows)
        losing_trades = []
        for i in range(10):
            losing_trades.append({
                'profit': -50.0,
                'symbol': 'EURUSD',
                'close_time': datetime.now() - timedelta(hours=i)
            })
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=losing_trades,
            market_data=self.market_data
        )
        
        # Should be capped at max multiplier
        expected_volume = 0.01 * 8  # max_multiplier
        self.assertEqual(trade_params.volume, expected_volume)


class TestAIIntegrationEdgeCases(unittest.TestCase):
    """Test edge cases for AI integration"""
    
    def setUp(self):
        """Set up test data"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """Clean up"""
        self.loop.close()
    
    @patch('aiohttp.ClientSession.post')
    def test_qwen_client_malformed_json_response(self, mock_post):
        """Test QwenClient with malformed JSON response"""
        # Mock response with malformed JSON
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            'choices': [{
                'message': {
                    'content': '''
                    This is not valid JSON response from AI:
                    action: BUY
                    confidence: high
                    reasoning: malformed response
                    '''
                }
            }]
        })
        
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async def test_malformed_response():
            async with QwenClient() as client:
                result = await client.generate_trading_decision("Test prompt")
                
                # Should return fallback response
                self.assertEqual(result['action'], 'HOLD')
                self.assertEqual(result['confidence'], 0.0)
                self.assertIn('parsing_failed', result.get('error', ''))
                
                return result
        
        result = self.loop.run_until_complete(test_malformed_response())
        self.assertIsInstance(result, dict)
    
    @patch('aiohttp.ClientSession.post')
    def test_qwen_client_incomplete_json_response(self, mock_post):
        """Test QwenClient with incomplete JSON response"""
        # Mock response with incomplete JSON (missing required fields)
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            'choices': [{
                'message': {
                    'content': '''
                    {
                        "action": "BUY",
                        "confidence": 0.85
                        // Missing required fields: reasoning, risk_level
                    }
                    '''
                }
            }]
        })
        
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async def test_incomplete_response():
            async with QwenClient() as client:
                result = await client.generate_trading_decision("Test prompt")
                
                # Should return fallback response due to missing fields
                self.assertEqual(result['action'], 'HOLD')
                self.assertEqual(result['confidence'], 0.0)
                
                return result
        
        result = self.loop.run_until_complete(test_incomplete_response())
        self.assertIsInstance(result, dict)
    
    @patch('aiohttp.ClientSession.post')
    def test_qwen_client_empty_response(self, mock_post):
        """Test QwenClient with empty response"""
        # Mock empty response
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            'choices': []  # Empty choices
        })
        
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async def test_empty_response():
            async with QwenClient() as client:
                result = await client.generate_trading_decision("Test prompt")
                
                # Should return error response
                self.assertEqual(result['action'], 'HOLD')
                self.assertEqual(result['confidence'], 0.0)
                self.assertIn('error', result)
                
                return result
        
        result = self.loop.run_until_complete(test_empty_response())
        self.assertIsInstance(result, dict)


class TestStrategyValidationEdgeCases(unittest.TestCase):
    """Test edge cases for strategy validation"""
    
    def setUp(self):
        """Set up test data"""
        self.strategy = TrendFollowingStrategy({'magic_number': 12345})
    
    def test_signal_validation_extreme_spread(self):
        """Test signal validation with extreme spread"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=100.0,  # Extremely wide spread
            volume=1000,
            volatility=0.0015
        )
        
        signal = TradingSignal(
            action="BUY",
            confidence=0.90,
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="Test signal",
            risk_level="MEDIUM"
        )
        
        # Should fail validation due to wide spread
        self.assertFalse(self.strategy.validate_signal(signal, market_data))
    
    def test_signal_validation_zero_confidence(self):
        """Test signal validation with zero confidence"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        signal = TradingSignal(
            action="BUY",
            confidence=0.0,  # Zero confidence
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="No confidence signal",
            risk_level="HIGH"
        )
        
        # Should fail validation due to low confidence
        self.assertFalse(self.strategy.validate_signal(signal, market_data))
    
    def test_signal_validation_invalid_prices(self):
        """Test signal validation with invalid price relationships"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        # BUY signal with stop loss above entry (invalid)
        signal = TradingSignal(
            action="BUY",
            confidence=0.85,
            entry_price=1.1000,
            stop_loss=1.1050,  # Stop loss above entry for BUY (invalid)
            take_profit=1.1150,
            reasoning="Invalid price relationship",
            risk_level="MEDIUM"
        )
        
        # Should still pass basic validation (price relationship validation would be in a more specific validator)
        result = self.strategy.validate_signal(signal, market_data)
        # The base validation doesn't check price relationships, so this passes
        self.assertTrue(result)


class TestPromptBuilderEdgeCases(unittest.TestCase):
    """Test edge cases for prompt builder"""
    
    def test_prompt_builder_empty_trade_history(self):
        """Test prompt builder with empty trade history"""
        prompt_builder = PromptBuilder()
        strategy = TrendFollowingStrategy({'magic_number': 12345})
        money_management = PercentRiskStrategy({'risk_percent': 2.0})
        
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],  # Empty candles
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        prompt = prompt_builder.build_trading_prompt(
            strategy=strategy,
            money_management=money_management,
            market_data=market_data,
            trade_history=[],  # Empty trade history
            account_info=account_info
        )
        
        # Should still generate a valid prompt
        self.assertIn("TREND FOLLOWING", prompt)
        self.assertIn("PERCENT RISK", prompt)
        self.assertIn("EURUSD", prompt)
    
    def test_prompt_builder_exception_handling(self):
        """Test prompt builder exception handling"""
        prompt_builder = PromptBuilder()
        
        # Create mock strategy that raises exception
        mock_strategy = Mock()
        mock_strategy.get_ai_prompt.side_effect = Exception("Strategy error")
        
        mock_money_management = Mock()
        mock_money_management.get_ai_prompt.return_value = "Test MM prompt"
        
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        # Should return fallback prompt when exception occurs
        prompt = prompt_builder.build_trading_prompt(
            strategy=mock_strategy,
            money_management=mock_money_management,
            market_data=market_data,
            trade_history=[],
            account_info=account_info
        )
        
        # Should return fallback prompt
        self.assertIn("FALLBACK", prompt)
        self.assertIn("EURUSD", prompt)


if __name__ == '__main__':
    # Run tests with detailed output
    unittest.main(verbosity=2)
