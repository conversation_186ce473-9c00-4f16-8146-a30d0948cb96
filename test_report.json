{"timestamp": "2025-08-06T09:40:31.054930", "duration": 42.602710247039795, "summary": {"total_tests": 82, "passed_tests": 64, "failed_tests": 9, "error_tests": 9, "skipped_tests": 0, "success_rate": 78.04878048780488, "overall_status": "FAILED"}, "test_suites": {"Unit Tests": {"name": "Unit Tests", "description": "Tests individual functions and methods", "tests": [], "summary": {"total": 31, "passed": 29, "failed": 1, "errors": 1, "skipped": 0}, "duration": 0.0, "status": "FAILED"}, "Integration Tests": {"name": "Integration Tests", "description": "Tests integration between modules", "tests": [], "summary": {"total": 10, "passed": 7, "failed": 1, "errors": 2, "skipped": 0}, "duration": 0.21564769744873047, "status": "FAILED"}, "Async Behavior Tests": {"name": "Async Behavior Tests", "description": "Tests async behavior and race conditions", "tests": [], "summary": {"total": 8, "passed": 5, "failed": 2, "errors": 1, "skipped": 0}, "duration": 24.77423930168152, "status": "FAILED"}, "End-to-End Tests": {"name": "End-to-End Tests", "description": "Tests complete user scenarios", "tests": [], "summary": {"total": 6, "passed": 2, "failed": 2, "errors": 2, "skipped": 0}, "duration": 7.751882553100586, "status": "FAILED"}, "Edge Cases and Failures": {"name": "Edge Cases and Failures", "description": "Tests edge cases and failure scenarios", "tests": [], "summary": {"total": 18, "passed": 15, "failed": 2, "errors": 1, "skipped": 0}, "duration": 0.17383360862731934, "status": "FAILED"}, "State Consistency Tests": {"name": "State Consistency Tests", "description": "Tests state consistency across services", "tests": [], "summary": {"total": 9, "passed": 6, "failed": 1, "errors": 2, "skipped": 0}, "duration": 1.903733491897583, "status": "FAILED"}}}