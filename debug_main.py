#!/usr/bin/env python3
"""
Debug version of main trading system to identify issues
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

print("🔍 Debug Main - Starting...")

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))
print(f"✅ Added src to path: {src_dir}")

# Load environment variables
load_dotenv()
print("✅ Environment variables loaded")

# Test imports one by one
print("🔧 Testing imports...")

try:
    from logging_system.logger import setup_logger, get_logger
    print("✅ Logging system imported")
except Exception as e:
    print(f"❌ Logging system import failed: {e}")
    sys.exit(1)

try:
    from account_management.account_manager import AccountManager
    print("✅ Account manager imported")
except Exception as e:
    print(f"❌ Account manager import failed: {e}")
    sys.exit(1)

try:
    from signal_generation.signal_generator import SignalGenerator
    print("✅ Signal generator imported")
except Exception as e:
    print(f"❌ Signal generator import failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

try:
    from trade_management.trade_manager import TradeManager
    print("✅ Trade manager imported")
except Exception as e:
    print(f"❌ Trade manager import failed: {e}")
    sys.exit(1)

try:
    from scheduling.scheduler_coordinator import scheduler_coordinator
    print("✅ Scheduler coordinator imported")
except Exception as e:
    print(f"❌ Scheduler coordinator import failed: {e}")
    sys.exit(1)

print("🎉 All imports successful!")

async def test_initialization():
    """Test basic initialization"""
    print("🔧 Testing initialization...")
    
    try:
        # Setup logging
        logger = setup_logger()
        print("✅ Logger setup successful")
        
        # Test account manager
        account_manager = AccountManager()
        print("✅ Account manager created")
        
        if account_manager.load_accounts():
            print(f"✅ Loaded {len(account_manager.accounts)} accounts")
        else:
            print("❌ Failed to load accounts")
            return False
        
        # Test signal generator creation (this might be where it hangs)
        print("🔧 Creating signal generator...")
        signal_generator = SignalGenerator(account_manager)
        print("✅ Signal generator created")
        
        # Test trade manager creation
        print("🔧 Creating trade manager...")
        trade_manager = TradeManager(account_manager)
        print("✅ Trade manager created")
        
        print("🎉 All components initialized successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🚀 Starting debug main...")
    
    try:
        success = asyncio.run(test_initialization())
        if success:
            print("✅ Debug test completed successfully!")
        else:
            print("❌ Debug test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Debug main failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
