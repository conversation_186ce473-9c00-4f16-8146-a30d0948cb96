#!/usr/bin/env python3
"""
Debug Position Limits Issue
Trace exactly where the wrong max_pending_orders value is coming from
"""

import sys
import json
import os
sys.path.append('src')

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def debug_position_limits():
    """Debug the position limits issue"""
    print("🔍 DEBUGGING POSITION LIMITS ISSUE")
    print("=" * 60)
    
    # Load accounts
    account_manager = AccountManager()
    account_manager.load_accounts()
    
    # Get the demo1 account
    demo1_account = account_manager.get_account('demo1')
    
    if not demo1_account:
        print("❌ demo1 account not found!")
        return
    
    print("📋 ACCOUNT CONFIGURATION:")
    print(f"  Account ID: {demo1_account.account_id}")
    print(f"  Money Management: {demo1_account.money_management_type}")

    # Convert to dict for easier access
    demo1_dict = {
        'account_id': demo1_account.account_id,
        'account_number': demo1_account.account_number,
        'money_management': demo1_account.money_management_type,
        'money_management_settings': demo1_account.money_management_config
    }

    # Print money management settings
    mm_settings = demo1_account.money_management_config or {}
    print("\n💰 MONEY MANAGEMENT SETTINGS:")
    for key, value in mm_settings.items():
        if not key.startswith('_'):
            print(f"  {key}: {value}")
    
    # Check environment variables
    print("\n🌍 ENVIRONMENT VARIABLES:")
    env_vars = [
        'MAX_DAILY_TRADES_PER_ACCOUNT',
        'MAX_OPEN_POSITIONS', 
        'MAX_PENDING_ORDERS',
        'MAX_DAILY_LOSS_DOLLARS',
        'MAX_DRAWDOWN_PERCENT'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'NOT SET')
        print(f"  {var}: {value}")
    
    # Create signal generator and test risk settings
    signal_gen = SignalGenerator(account_manager)
    
    print("\n🔧 SIGNAL GENERATOR DEFAULTS:")
    print(f"  default_max_daily_trades: {signal_gen.default_max_daily_trades}")
    print(f"  default_max_open_positions: {signal_gen.default_max_open_positions}")
    print(f"  default_max_pending_orders: {signal_gen.default_max_pending_orders}")
    print(f"  default_max_daily_loss: {signal_gen.default_max_daily_loss}")
    print(f"  default_max_drawdown_percent: {signal_gen.default_max_drawdown_percent}")
    
    # Test the _get_account_risk_settings function
    print("\n🎯 RISK SETTINGS RESOLUTION:")
    risk_settings = signal_gen._get_account_risk_settings(demo1_dict)
    
    print("  Final risk settings:")
    for key, value in risk_settings.items():
        print(f"    {key}: {value}")
    
    # Test specific values
    print("\n🔍 DETAILED ANALYSIS:")
    
    # Check max_pending_orders specifically
    mm_pending = mm_settings.get('max_pending_orders', 'NOT SET')
    default_pending = signal_gen.default_max_pending_orders
    final_pending = risk_settings.get('max_pending_orders', 'NOT SET')
    
    print(f"  max_pending_orders in accounts.json: {mm_pending}")
    print(f"  default_max_pending_orders from env: {default_pending}")
    print(f"  final max_pending_orders: {final_pending}")
    
    # Check the logic
    if mm_pending != 'NOT SET':
        expected = mm_pending
    else:
        expected = default_pending
    
    print(f"  Expected value: {expected}")
    print(f"  Actual value: {final_pending}")
    print(f"  Match: {'✅' if expected == final_pending else '❌'}")
    
    # Check if there's a bug in the get() method
    print("\n🧪 TESTING GET METHOD:")
    test_get = mm_settings.get('max_pending_orders', signal_gen.default_max_pending_orders)
    print(f"  Direct get() result: {test_get}")
    print(f"  Type: {type(test_get)}")
    
    # Print raw money management settings
    print("\n📄 RAW MONEY MANAGEMENT SETTINGS:")
    print(json.dumps(mm_settings, indent=2))

if __name__ == "__main__":
    debug_position_limits()
