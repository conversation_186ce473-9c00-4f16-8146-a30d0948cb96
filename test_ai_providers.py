#!/usr/bin/env python3
"""
Test AI Providers - Multi-Provider Testing Script
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_integration.ai_provider_factory import AIProviderFactory, ai_provider_manager
from ai_integration.config_validator import ai_config_validator
from ai_integration.base_ai_provider import AIProviderType, AIProviderConfig
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()


async def test_configuration_validation():
    """Test AI configuration validation"""
    print("\n🔍 TESTING CONFIGURATION VALIDATION")
    print("=" * 50)
    
    try:
        # Validate environment configuration
        validation_result = ai_config_validator.validate_environment()
        
        print(f"✅ Configuration Valid: {validation_result.is_valid}")
        
        if validation_result.errors:
            print("\n❌ ERRORS:")
            for error in validation_result.errors:
                print(f"   • {error}")
        
        if validation_result.warnings:
            print("\n⚠️ WARNINGS:")
            for warning in validation_result.warnings:
                print(f"   • {warning}")
        
        # Get configuration summary
        summary = ai_config_validator.get_configuration_summary()
        print(f"\n📋 CONFIGURATION SUMMARY:")
        print(f"   Selected Provider: {summary['selected_provider']}")
        print(f"   Available Providers: {summary['available_providers']}")
        print(f"   Model: {summary['configuration'].get('model', 'N/A')}")
        print(f"   Temperature: {summary['configuration']['temperature']}")
        print(f"   Max Tokens: {summary['configuration']['max_tokens']}")
        
        return validation_result.is_valid
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


async def test_provider_creation():
    """Test AI provider creation and basic functionality"""
    print("\n🏭 TESTING PROVIDER CREATION")
    print("=" * 50)
    
    try:
        # Test getting current provider
        provider = ai_provider_manager.get_provider()
        print(f"✅ Current provider created: {provider.config.provider_type.value}")
        print(f"   Model: {provider.config.model}")
        
        # Test provider info
        info = ai_provider_manager.get_provider_info()
        print(f"   Available providers: {info['available_providers']}")
        print(f"   Fallback providers: {info['fallback_providers']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Provider creation failed: {e}")
        return False


async def test_provider_connectivity():
    """Test AI provider connectivity"""
    print("\n🌐 TESTING PROVIDER CONNECTIVITY")
    print("=" * 50)
    
    try:
        # Test primary provider
        provider = await ai_provider_manager.get_provider_with_fallback()
        
        async with provider:
            print(f"🔗 Testing {provider.config.provider_type.value} connectivity...")
            
            is_connected = await provider.validate_connection()
            if is_connected:
                print(f"✅ {provider.config.provider_type.value} connection successful")
            else:
                print(f"❌ {provider.config.provider_type.value} connection failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Connectivity test failed: {e}")
        return False


async def test_ai_trading_decision():
    """Test AI trading decision generation"""
    print("\n🤖 TESTING AI TRADING DECISIONS")
    print("=" * 50)
    
    try:
        provider = await ai_provider_manager.get_provider_with_fallback()
        
        # Test trading decision prompt
        test_prompt = """
        You are analyzing EURUSD on M15 timeframe for a trend following strategy.
        
        Current Market Data:
        - Symbol: EURUSD
        - Current Price: 1.0850
        - Trend: Upward
        - RSI: 65
        - Moving Average: Price above 20 MA
        - Volume: Normal
        
        Account Information:
        - Balance: $1000
        - Risk per trade: 2%
        - Max open positions: 3
        
        Please provide a trading decision in JSON format.
        """
        
        async with provider:
            print(f"📡 Sending test trading decision request to {provider.config.provider_type.value}...")
            
            response = await provider.generate_trading_decision(test_prompt)
            
            if response and isinstance(response, dict):
                print("✅ Trading decision received:")
                print(f"   Action: {response.get('action', 'N/A')}")
                print(f"   Confidence: {response.get('confidence', 0):.2%}")
                print(f"   Risk Level: {response.get('risk_level', 'N/A')}")
                print(f"   Provider: {response.get('provider', 'N/A')}")
                print(f"   Model: {response.get('model', 'N/A')}")
                
                if response.get('reasoning'):
                    print(f"   Reasoning: {response['reasoning'][:100]}...")
                
                return True
            else:
                print("❌ Invalid response format")
                return False
        
    except Exception as e:
        print(f"❌ Trading decision test failed: {e}")
        return False


async def test_raw_completion():
    """Test raw AI completion"""
    print("\n📝 TESTING RAW COMPLETION")
    print("=" * 50)
    
    try:
        provider = await ai_provider_manager.get_provider_with_fallback()
        
        test_prompt = "Explain the concept of risk management in trading in one sentence."
        
        async with provider:
            print(f"📡 Sending raw completion request to {provider.config.provider_type.value}...")
            
            response = await provider.get_completion(test_prompt)
            
            if response and isinstance(response, str):
                print("✅ Raw completion received:")
                print(f"   Response: {response[:200]}...")
                return True
            else:
                print("❌ No response received")
                return False
        
    except Exception as e:
        print(f"❌ Raw completion test failed: {e}")
        return False


async def test_model_availability():
    """Test model availability"""
    print("\n📚 TESTING MODEL AVAILABILITY")
    print("=" * 50)
    
    try:
        provider = ai_provider_manager.get_provider()
        
        models = provider.get_available_models()
        print(f"✅ Available models for {provider.config.provider_type.value}:")
        for i, model in enumerate(models[:10], 1):  # Show first 10 models
            print(f"   {i}. {model}")
        
        if len(models) > 10:
            print(f"   ... and {len(models) - 10} more models")
        
        return True
        
    except Exception as e:
        print(f"❌ Model availability test failed: {e}")
        return False


async def test_fallback_mechanism():
    """Test fallback mechanism (if multiple providers available)"""
    print("\n🔄 TESTING FALLBACK MECHANISM")
    print("=" * 50)
    
    try:
        info = ai_provider_manager.get_provider_info()
        
        if len(info['available_providers']) < 2:
            print("⚠️ Only one provider available - skipping fallback test")
            return True
        
        print(f"🔄 Testing fallback with {len(info['available_providers'])} providers available")
        
        # Test getting provider with fallback
        provider = await ai_provider_manager.get_provider_with_fallback()
        
        async with provider:
            print(f"✅ Fallback mechanism working - using {provider.config.provider_type.value}")
            return True
        
    except Exception as e:
        print(f"❌ Fallback mechanism test failed: {e}")
        return False


async def run_all_tests():
    """Run all AI provider tests"""
    print("🚀 STARTING AI PROVIDER TESTS")
    print("=" * 60)
    
    tests = [
        ("Configuration Validation", test_configuration_validation),
        ("Provider Creation", test_provider_creation),
        ("Provider Connectivity", test_provider_connectivity),
        ("AI Trading Decision", test_ai_trading_decision),
        ("Raw Completion", test_raw_completion),
        ("Model Availability", test_model_availability),
        ("Fallback Mechanism", test_fallback_mechanism)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! AI provider system is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the configuration and try again.")
        return False


if __name__ == "__main__":
    asyncio.run(run_all_tests())
