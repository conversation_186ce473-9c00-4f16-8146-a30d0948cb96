#!/usr/bin/env python3
print("Hello from Python!")
print("Testing basic functionality...")

import sys
import os
from pathlib import Path

print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")

# Check if src directory exists
src_dir = Path("src")
print(f"Source directory exists: {src_dir.exists()}")

if src_dir.exists():
    print("Source directory contents:")
    for item in src_dir.iterdir():
        print(f"  - {item.name}")

print("Test completed!")
