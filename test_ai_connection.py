#!/usr/bin/env python3
"""
Test AI API Connection with Real API Key
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from logging_system.logger import setup_logger, get_logger

async def test_qwen_api():
    """Test Qwen API connection"""
    print("🔍 Testing Qwen AI API Connection...")
    
    # Load environment variables
    load_dotenv()
    
    # Setup logging
    logger = setup_logger()
    
    try:
        # Import AI provider factory
        from ai_integration.ai_provider_factory import get_ai_provider_with_fallback

        # Create client
        client = await get_ai_provider_with_fallback()
        print("✓ AI client created successfully")
        
        # Test with a simple prompt
        test_prompt = """
        You are an AI trading assistant. Please respond with a simple JSON object containing:
        {
            "status": "connected",
            "message": "AI API connection successful",
            "timestamp": "current_time"
        }
        
        Keep the response short and in valid JSON format.
        """
        
        print("📡 Sending test request to Qwen API...")
        
        # Make API call
        response = await client.generate_trading_decision(test_prompt)
        
        if response:
            print("✅ API Response received:")
            print(f"Response: {response[:200]}...")
            return True
        else:
            print("❌ No response from API")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        logger.error(f"Qwen API test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🤖 AI API Connection Test")
    print("=" * 40)

    # Load environment variables first
    load_dotenv()

    # Check API key
    api_key = os.getenv('QWEN_API_KEY')
    if not api_key:
        print("❌ QWEN_API_KEY not found in environment")
        return False

    print(f"✓ API Key found: {api_key[:10]}...{api_key[-4:]}")
    
    # Test API connection
    success = await test_qwen_api()
    
    if success:
        print("\n🎉 AI API connection successful!")
        print("Your trading system can now make AI-driven decisions.")
    else:
        print("\n❌ AI API connection failed.")
        print("Please check your API key and internet connection.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
