#!/usr/bin/env python3
"""
Test OpenRouter Integration Fix
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_openrouter_basic():
    """Test basic OpenRouter functionality"""
    print("🧪 TESTING OPENROUTER BASIC FUNCTIONALITY")
    print("=" * 50)
    
    try:
        from ai_integration.ai_provider_factory import get_ai_provider_with_fallback
        
        # Get AI provider (should be OpenRouter based on .env)
        provider = await get_ai_provider_with_fallback()
        print(f"✅ Provider created: {provider.config.provider_type.value}")
        print(f"   Model: {provider.config.model}")
        print(f"   API URL: {provider.config.api_url}")
        
        # Test connection validation
        print("\n🔗 Testing connection validation...")
        async with provider:
            is_connected = await provider.validate_connection()
            if is_connected:
                print("✅ Connection validation successful")
            else:
                print("❌ Connection validation failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_openrouter_completion():
    """Test OpenRouter completion"""
    print("\n📝 TESTING OPENROUTER COMPLETION")
    print("=" * 50)
    
    try:
        from ai_integration.ai_provider_factory import get_ai_provider_with_fallback
        
        provider = await get_ai_provider_with_fallback()
        
        test_prompt = "What is 2+2? Respond with just the number."
        
        async with provider:
            print("📡 Sending completion request...")
            response = await provider.get_completion(test_prompt)
            
            if response:
                print(f"✅ Completion received: {response[:100]}...")
                return True
            else:
                print("❌ No completion received")
                return False
        
    except Exception as e:
        print(f"❌ Completion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_openrouter_trading_decision():
    """Test OpenRouter trading decision"""
    print("\n🤖 TESTING OPENROUTER TRADING DECISION")
    print("=" * 50)
    
    try:
        from ai_integration.ai_provider_factory import get_ai_provider_with_fallback
        
        provider = await get_ai_provider_with_fallback()
        
        trading_prompt = """
        Analyze EURUSD for a simple trading decision.
        Current price: 1.0850
        Trend: Upward
        
        Respond with JSON format:
        {
            "action": "BUY",
            "confidence": 0.7,
            "reasoning": "Simple test reasoning",
            "risk_level": "MEDIUM"
        }
        """
        
        async with provider:
            print("📡 Sending trading decision request...")
            response = await provider.generate_trading_decision(trading_prompt)
            
            if response and isinstance(response, dict):
                print("✅ Trading decision received:")
                print(f"   Action: {response.get('action', 'N/A')}")
                print(f"   Confidence: {response.get('confidence', 0):.2%}")
                print(f"   Risk Level: {response.get('risk_level', 'N/A')}")
                print(f"   Provider: {response.get('provider', 'N/A')}")
                print(f"   Model: {response.get('model', 'N/A')}")
                
                if response.get('reasoning'):
                    print(f"   Reasoning: {response['reasoning'][:100]}...")
                
                return True
            else:
                print(f"❌ Invalid trading decision response: {type(response)}")
                print(f"   Response: {response}")
                return False
        
    except Exception as e:
        print(f"❌ Trading decision test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_session_management():
    """Test session management robustness"""
    print("\n🔄 TESTING SESSION MANAGEMENT")
    print("=" * 50)
    
    try:
        from ai_integration.ai_provider_factory import get_ai_provider_with_fallback
        
        # Test multiple sequential requests
        provider = await get_ai_provider_with_fallback()
        
        async with provider:
            print("📡 Testing multiple sequential requests...")
            
            for i in range(3):
                print(f"   Request {i+1}/3...")
                response = await provider.get_completion(f"Say 'Test {i+1}' and nothing else.")
                if not response:
                    print(f"❌ Request {i+1} failed")
                    return False
                print(f"   ✅ Request {i+1} successful: {response[:50]}...")
        
        print("✅ Session management test passed")
        return True
        
    except Exception as e:
        print(f"❌ Session management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_all_tests():
    """Run all OpenRouter tests"""
    print("🚀 STARTING OPENROUTER INTEGRATION TESTS")
    print("=" * 60)
    
    tests = [
        ("Basic Functionality", test_openrouter_basic),
        ("Completion", test_openrouter_completion),
        ("Trading Decision", test_openrouter_trading_decision),
        ("Session Management", test_session_management)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All OpenRouter tests passed! Integration is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        return False


if __name__ == "__main__":
    asyncio.run(run_all_tests())
