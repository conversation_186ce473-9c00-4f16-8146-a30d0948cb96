#!/usr/bin/env python3
"""
Clean up old log files to free up disk space
Keep only the most recent logs and compress older ones
"""

import os
import shutil
from pathlib import Path
from datetime import datetime, timedelta

def cleanup_logs():
    """Clean up old log files"""
    print("🧹 CLEANING UP OLD LOG FILES")
    print("=" * 40)
    
    logs_dir = Path("logs")
    if not logs_dir.exists():
        print("❌ Logs directory not found")
        return
    
    # Get all log files
    log_files = list(logs_dir.glob("*.log*"))
    
    # Separate current logs from archived logs
    current_logs = [f for f in log_files if not f.name.endswith('.zip')]
    archived_logs = [f for f in log_files if f.name.endswith('.zip')]
    
    print(f"📊 Found {len(current_logs)} current logs and {len(archived_logs)} archived logs")
    
    # Calculate sizes
    total_size = sum(f.stat().st_size for f in log_files)
    print(f"💾 Total log files size: {total_size / (1024*1024):.1f} MB")
    
    # Keep only last 7 days of archived logs
    cutoff_date = datetime.now() - timedelta(days=7)
    old_archives = []
    
    for archive in archived_logs:
        try:
            # Extract date from filename (format: filename.YYYY-MM-DD_HH-MM-SS_XXXXXX.log.zip)
            parts = archive.name.split('.')
            if len(parts) >= 3:
                date_part = parts[1]  # YYYY-MM-DD_HH-MM-SS_XXXXXX
                date_str = date_part.split('_')[0]  # YYYY-MM-DD
                file_date = datetime.strptime(date_str, '%Y-%m-%d')
                
                if file_date < cutoff_date:
                    old_archives.append(archive)
        except Exception as e:
            print(f"⚠️  Could not parse date from {archive.name}: {e}")
    
    # Delete old archives
    deleted_size = 0
    for old_archive in old_archives:
        try:
            file_size = old_archive.stat().st_size
            old_archive.unlink()
            deleted_size += file_size
            print(f"🗑️  Deleted: {old_archive.name} ({file_size / (1024*1024):.1f} MB)")
        except Exception as e:
            print(f"❌ Failed to delete {old_archive.name}: {e}")
    
    print(f"\n✅ Cleanup completed!")
    print(f"📊 Deleted {len(old_archives)} old archive files")
    print(f"💾 Freed up {deleted_size / (1024*1024):.1f} MB of disk space")
    
    # Show remaining files
    remaining_files = list(logs_dir.glob("*.log*"))
    remaining_size = sum(f.stat().st_size for f in remaining_files)
    print(f"📁 Remaining: {len(remaining_files)} files ({remaining_size / (1024*1024):.1f} MB)")

if __name__ == "__main__":
    cleanup_logs()
