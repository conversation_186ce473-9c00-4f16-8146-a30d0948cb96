"""
AI Provider Factory and Manager for Multi-Provider Support
"""

import os
from typing import Dict, Any, Optional, List
from enum import Enum

from .base_ai_provider import BaseAIProvider, AIProviderType, AIProviderConfig
from logging_system.logger import get_logger

logger = get_logger(__name__)


class AIProviderFactory:
    """Factory for creating AI provider instances"""
    
    _providers = {}  # Registry of available providers
    
    @classmethod
    def register_provider(cls, provider_type: AIProviderType, provider_class):
        """Register a new AI provider"""
        cls._providers[provider_type] = provider_class
        logger.info(f"🤖 Registered AI provider: {provider_type.value}")
    
    @classmethod
    def create_provider(cls, config: AIProviderConfig) -> BaseAIProvider:
        """Create AI provider instance based on configuration"""
        provider_class = cls._providers.get(config.provider_type)
        
        if not provider_class:
            available = list(cls._providers.keys())
            raise ValueError(f"Unknown AI provider: {config.provider_type}. Available: {available}")
        
        logger.info(f"🤖 Creating AI provider: {config.provider_type.value} with model: {config.model}")
        return provider_class(config)
    
    @classmethod
    def get_available_providers(cls) -> List[AIProviderType]:
        """Get list of available provider types"""
        return list(cls._providers.keys())


class AIProviderManager:
    """Manager for AI provider configuration and selection"""
    
    def __init__(self):
        self._current_provider: Optional[BaseAIProvider] = None
        self._config: Optional[AIProviderConfig] = None
        self._fallback_configs: List[AIProviderConfig] = []
    
    def load_config_from_env(self) -> AIProviderConfig:
        """Load AI provider configuration from environment variables"""
        
        # Determine provider type
        provider_name = os.getenv('AI_PROVIDER', 'qwen').lower()
        
        try:
            provider_type = AIProviderType(provider_name)
        except ValueError:
            logger.warning(f"Unknown AI provider '{provider_name}', defaulting to Qwen")
            provider_type = AIProviderType.QWEN
        
        # Load provider-specific configuration
        if provider_type == AIProviderType.QWEN:
            config = self._load_qwen_config()
        elif provider_type == AIProviderType.OPENROUTER:
            config = self._load_openrouter_config()
        else:
            raise ValueError(f"Unsupported provider type: {provider_type}")
        
        # Load fallback configurations
        self._load_fallback_configs()
        
        logger.info(f"🤖 Loaded AI configuration: {provider_type.value} with model: {config.model}")
        return config
    
    def _load_qwen_config(self) -> AIProviderConfig:
        """Load Qwen provider configuration"""
        api_key = os.getenv('QWEN_API_KEY')
        if not api_key:
            raise ValueError("QWEN_API_KEY environment variable is required for Qwen provider")
        
        return AIProviderConfig(
            provider_type=AIProviderType.QWEN,
            api_key=api_key,
            api_url=os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1'),
            model=os.getenv('AI_MODEL', 'qwen-max-2025-01-25'),
            max_concurrent_requests=int(os.getenv('MAX_CONCURRENT_REQUESTS', '5')),
            timeout=int(os.getenv('AI_TIMEOUT', '30')),
            temperature=float(os.getenv('AI_TEMPERATURE', '0.3')),
            max_tokens=int(os.getenv('AI_MAX_TOKENS', '2000')),
            top_p=float(os.getenv('AI_TOP_P', '0.8'))
        )
    
    def _load_openrouter_config(self) -> AIProviderConfig:
        """Load OpenRouter provider configuration"""
        api_key = os.getenv('OPENROUTER_API_KEY')
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is required for OpenRouter provider")
        
        return AIProviderConfig(
            provider_type=AIProviderType.OPENROUTER,
            api_key=api_key,
            api_url=os.getenv('OPENROUTER_API_URL', 'https://openrouter.ai/api/v1'),
            model=os.getenv('OPENROUTER_MODEL', 'anthropic/claude-3.5-sonnet'),
            max_concurrent_requests=int(os.getenv('MAX_CONCURRENT_REQUESTS', '5')),
            timeout=int(os.getenv('AI_TIMEOUT', '30')),
            temperature=float(os.getenv('AI_TEMPERATURE', '0.3')),
            max_tokens=int(os.getenv('AI_MAX_TOKENS', '2000')),
            top_p=float(os.getenv('AI_TOP_P', '0.8')),
            # OpenRouter specific parameters
            site_url=os.getenv('OPENROUTER_SITE_URL', 'https://fulltrade-ai.com'),
            app_name=os.getenv('OPENROUTER_APP_NAME', 'FullTrade AI Trading System')
        )
    
    def _load_fallback_configs(self):
        """Load fallback provider configurations"""
        self._fallback_configs = []
        
        # Try to load both providers as fallbacks
        try:
            if os.getenv('QWEN_API_KEY'):
                qwen_config = self._load_qwen_config()
                if qwen_config.provider_type != self._config.provider_type:
                    self._fallback_configs.append(qwen_config)
        except Exception as e:
            logger.debug(f"Could not load Qwen fallback: {e}")
        
        try:
            if os.getenv('OPENROUTER_API_KEY'):
                openrouter_config = self._load_openrouter_config()
                if openrouter_config.provider_type != self._config.provider_type:
                    self._fallback_configs.append(openrouter_config)
        except Exception as e:
            logger.debug(f"Could not load OpenRouter fallback: {e}")
        
        if self._fallback_configs:
            fallback_names = [config.provider_type.value for config in self._fallback_configs]
            logger.info(f"🔄 Loaded fallback providers: {fallback_names}")
    
    def get_provider(self, force_reload: bool = False) -> BaseAIProvider:
        """Get current AI provider instance"""
        
        if self._current_provider is None or force_reload:
            if self._config is None or force_reload:
                self._config = self.load_config_from_env()
            
            self._current_provider = AIProviderFactory.create_provider(self._config)
        
        return self._current_provider
    
    async def get_provider_with_fallback(self) -> BaseAIProvider:
        """Get AI provider with automatic fallback on failure"""
        
        # Try primary provider first
        try:
            provider = self.get_provider()
            if await provider.validate_connection():
                return provider
            else:
                logger.warning(f"🔄 Primary provider {self._config.provider_type.value} connection failed")
        except Exception as e:
            logger.error(f"🔄 Primary provider {self._config.provider_type.value} failed: {e}")
        
        # Try fallback providers
        for fallback_config in self._fallback_configs:
            try:
                logger.info(f"🔄 Trying fallback provider: {fallback_config.provider_type.value}")
                fallback_provider = AIProviderFactory.create_provider(fallback_config)
                
                if await fallback_provider.validate_connection():
                    logger.info(f"✅ Fallback provider {fallback_config.provider_type.value} connected successfully")
                    return fallback_provider
                else:
                    logger.warning(f"🔄 Fallback provider {fallback_config.provider_type.value} connection failed")
            except Exception as e:
                logger.error(f"🔄 Fallback provider {fallback_config.provider_type.value} failed: {e}")
        
        # If all providers fail, return the primary provider anyway (it will handle errors gracefully)
        logger.error("🚨 All AI providers failed, returning primary provider with error handling")
        return self.get_provider()
    
    def get_config(self) -> Optional[AIProviderConfig]:
        """Get current provider configuration"""
        return self._config
    
    def set_config(self, config: AIProviderConfig):
        """Set provider configuration"""
        self._config = config
        self._current_provider = None  # Force recreation on next get_provider call
        logger.info(f"🤖 AI provider configuration updated: {config.provider_type.value}")
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about current provider and available options"""
        info = {
            'current_provider': self._config.provider_type.value if self._config else None,
            'current_model': self._config.model if self._config else None,
            'available_providers': [p.value for p in AIProviderFactory.get_available_providers()],
            'fallback_providers': [c.provider_type.value for c in self._fallback_configs],
            'config': self._config.to_dict() if self._config else None
        }
        
        return info


# Global AI provider manager instance
ai_provider_manager = AIProviderManager()


def get_ai_provider() -> BaseAIProvider:
    """Convenience function to get current AI provider"""
    return ai_provider_manager.get_provider()


async def get_ai_provider_with_fallback() -> BaseAIProvider:
    """Convenience function to get AI provider with fallback"""
    return await ai_provider_manager.get_provider_with_fallback()


def register_default_providers():
    """Register default AI providers"""
    try:
        from .qwen_client import QwenClient
        AIProviderFactory.register_provider(AIProviderType.QWEN, QwenClient)
    except ImportError as e:
        logger.warning(f"Could not register Qwen provider: {e}")

    try:
        from .openrouter_client import OpenRouterClient
        AIProviderFactory.register_provider(AIProviderType.OPENROUTER, OpenRouterClient)
    except ImportError as e:
        logger.warning(f"Could not register OpenRouter provider: {e}")


# Register providers on module import
register_default_providers()
