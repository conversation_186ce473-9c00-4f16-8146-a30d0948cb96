"""
AI-Driven Trade Management System
"""

import asyncio
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import schedule
import time
from dotenv import load_dotenv
import MetaTrader5 as mt5

# Load environment variables
load_dotenv()

from ai_integration.ai_provider_factory import get_ai_provider_with_fallback
from ai_integration.prompt_builder import PromptBuilder
from mt5_integration.mt5_client import MT5Client
from account_management.account_manager import AccountManager
from money_management.base_strategy import AccountInfo
from logging_system.logger import get_logger, trading_logger
from validation.trade_validator import TradeValidator, ValidationResult
from mt5_integration.session_manager import session_manager
from risk_management.unified_risk_manager import unified_risk_manager

logger = get_logger(__name__)

class TradeManager:
    """Manages existing trades using AI decisions"""

    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        self.prompt_builder = PromptBuilder()
        self.mt5_client = MT5Client()  # Will be updated with AI client when available
        self.trade_validator = TradeValidator()
        self.running = False
        self.management_interval = int(os.getenv('TRADE_MANAGEMENT_INTERVAL_MINUTES', '30'))
        self.ai_client = None  # Will be set when AI client is available
        
    async def start(self):
        """Start the trade management system"""
        try:
            logger.info("Starting trade management system...")
            
            # Initialize MT5
            if not self.mt5_client.initialize():
                logger.error("Failed to initialize MT5")
                return False
            
            self.running = True
            
            # Schedule trade management
            schedule.every(self.management_interval).minutes.do(self._schedule_trade_management)
            
            # Start scheduler loop
            while self.running:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
            
            logger.info("Trade management system stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error starting trade manager: {e}")
            return False
    
    def stop(self):
        """Stop the trade management system"""
        self.running = False
        self.mt5_client.shutdown()
        logger.info("Trade management system stop requested")
    
    def _schedule_trade_management(self):
        """Schedule trade management (called by scheduler)"""
        try:
            asyncio.create_task(self.manage_trades())
        except Exception as e:
            logger.error(f"Error scheduling trade management: {e}")
    
    async def manage_trades(self):
        """Manage all open trades across accounts"""
        try:
            logger.info("Starting trade management cycle...")
            trading_logger.log_system_event(
                "TRADE_MANAGEMENT_START",
                "Starting trade management cycle for all accounts"
            )

            # Note: MT5 initialization is handled by the session manager

            # Get all accounts
            accounts = self.account_manager.get_all_accounts()
            trading_logger.log_system_event(
                "TRADE_MANAGEMENT_ACCOUNTS",
                f"Managing trades for {len(accounts)} accounts"
            )

            ai_client = await get_ai_provider_with_fallback()
            async with ai_client:
                # Update MT5Client with AI client for error handling if not already done
                if not self.ai_client:
                    self.ai_client = ai_client
                    self.mt5_client = MT5Client(ai_client=ai_client)
                    logger.info("🤖 TradeManager MT5Client updated with AI error handling capabilities")

                for account in accounts:
                    await self._manage_account_trades(account, ai_client)

            logger.info("Trade management cycle completed")
            trading_logger.log_system_event(
                "TRADE_MANAGEMENT_COMPLETE",
                "Trade management cycle completed successfully"
            )

        except Exception as e:
            logger.error(f"Error in trade management: {e}")
            trading_logger.log_system_event(
                "TRADE_MANAGEMENT_ERROR",
                f"Trade management cycle failed: {e}",
                "ERROR"
            )
    
    async def _manage_account_trades(self, account, ai_client):
        """Manage trades for a specific account using session management"""
        try:
            # Use session manager for account operations
            async with session_manager.account_session(account, self.mt5_client) as mt5_client:
                logger.info(f"🔒 Using managed session for trade management - account {account.account_id}")

                # Check for missing position closures first
                await self._check_missing_position_closures(account, mt5_client)

                # Get open positions
                positions = mt5_client.get_positions()

                # Get pending orders
                pending_orders = mt5_client.get_pending_orders()

                # Ensure we have valid lists
                if not isinstance(positions, list):
                    logger.error(f"Invalid positions data type: {type(positions)}")
                    positions = []

                if not isinstance(pending_orders, list):
                    logger.error(f"Invalid pending_orders data type: {type(pending_orders)}")
                    pending_orders = []

                if not positions and not pending_orders:
                    logger.debug(f"No open positions or pending orders for account {account.account_id}")
                    return

                logger.info(f"Managing {len(positions)} positions and {len(pending_orders)} pending orders for account {account.account_id}")

                # Group positions by symbol for efficiency
                positions_by_symbol = {}
                for position in positions:
                    try:
                        symbol = position['symbol']
                        if symbol not in positions_by_symbol:
                            positions_by_symbol[symbol] = []
                        positions_by_symbol[symbol].append(position)
                    except (TypeError, KeyError) as e:
                        logger.error(f"Invalid position data structure: {position}, error: {e}")
                        continue

                # Group pending orders by symbol for efficiency
                orders_by_symbol = {}
                for order in pending_orders:
                    try:
                        symbol = order['symbol']
                        if symbol not in orders_by_symbol:
                            orders_by_symbol[symbol] = []
                        orders_by_symbol[symbol].append(order)
                    except (TypeError, KeyError) as e:
                        logger.error(f"Invalid order data structure: {order}, error: {e}")
                        continue

                # Get all symbols that have either positions or orders
                all_symbols = set(positions_by_symbol.keys()) | set(orders_by_symbol.keys())

                # Process each symbol
                for symbol in all_symbols:
                    symbol_positions = positions_by_symbol.get(symbol, [])
                    symbol_orders = orders_by_symbol.get(symbol, [])

                    await self._manage_symbol_positions(
                        account, symbol, symbol_positions, ai_client, mt5_client
                    )

                    # Manage pending orders for this symbol
                    if symbol_orders:
                        await self._manage_symbol_orders(
                            account, symbol, symbol_orders, ai_client, mt5_client
                        )
            
        except Exception as e:
            logger.error(f"Error managing trades for account {account.account_id}: {e}")
    
    async def _manage_symbol_positions(
        self,
        account,
        symbol: str,
        positions: List[Dict[str, Any]],
        ai_client,
        mt5_client
    ):
        """Manage positions for a specific symbol"""
        try:
            # Get symbol configuration for this account
            symbol_config = None
            for config in account.symbols:
                if config['symbol'] == symbol:
                    symbol_config = config
                    break

            if not symbol_config:
                logger.warning(f"Symbol {symbol} not configured for account {account.account_id}, but managing existing positions for safety")
                # Create a default config for unconfigured symbols to manage existing positions
                symbol_config = {'symbol': symbol, 'timeframe': 'M15'}

            # Skip if no actual positions to manage
            if not positions:
                logger.debug(f"No positions to manage for {symbol}")
                return
            
            # Get strategy and money management with proper types and configs
            from strategies.base_strategy import StrategyType
            from money_management.base_strategy import MoneyManagementType

            # Convert string types to enums
            strategy_type_map = {
                'trend_following': StrategyType.TREND_FOLLOWING,
                'mean_reversion': StrategyType.MEAN_REVERSION,
                'breakout': StrategyType.BREAKOUT,
                'scalping': StrategyType.MEAN_REVERSION,  # Default scalping to mean reversion
            }

            mm_type_map = {
                'fixed_volume': MoneyManagementType.FIXED_VOLUME,
                'percent_risk': MoneyManagementType.PERCENT_RISK,
                'martingale': MoneyManagementType.MARTINGALE,
                'anti_martingale': MoneyManagementType.ANTI_MARTINGALE,
                'fixed_lot': MoneyManagementType.FIXED_VOLUME,  # Alias
            }

            strategy_type_enum = strategy_type_map.get(account.strategy_type, StrategyType.MEAN_REVERSION)
            mm_type_enum = mm_type_map.get(account.money_management_type, MoneyManagementType.FIXED_VOLUME)

            # Get default configs and merge with account config
            strategy_config = self.account_manager.strategy_factory.get_default_config(strategy_type_enum)
            strategy_config.update(account.money_management_settings.get('strategy_config', {}))  # FIXED: Use standardized field name

            mm_config = self.account_manager.money_management_factory.get_default_config(mm_type_enum)
            mm_config.update(account.money_management_settings)  # FIXED: Use standardized field name

            strategy = self.account_manager.strategy_factory.create_strategy(strategy_type_enum, strategy_config)
            money_management = self.account_manager.money_management_factory.create_strategy(mm_type_enum, mm_config)
            
            if not strategy or not money_management:
                logger.error(f"Failed to create strategy/MM for account {account.account_id}")
                return
            
            # Get current market data
            market_data_dict = mt5_client.get_market_data(
                symbol, symbol_config['timeframe'], 50
            )
            if not market_data_dict:
                logger.error(f"Failed to get market data for {symbol}")
                return

            # Get account info
            account_balance = mt5_client.get_account_info()
            if not account_balance:
                logger.error(f"Failed to get account info")
                return
            
            account_info = AccountInfo(
                balance=account_balance.balance,
                equity=account_balance.equity,
                margin=account_balance.margin,
                free_margin=account_balance.free_margin,
                margin_level=account_balance.margin_level,
                currency=account_balance.currency,
                leverage=account_balance.leverage
            )
            
            # Get trade history for context
            trade_history = mt5_client.get_trade_history(7)  # Last 7 days
            strategy_trades = [
                trade for trade in trade_history 
                if trade.get('magic_number') == strategy.magic_number
            ]
            
            # Process each position
            for position in positions:
                await self._manage_single_position(
                    account, position, symbol, market_data_dict,
                    strategy, money_management, account_info,
                    strategy_trades, ai_client, mt5_client
                )
            
        except Exception as e:
            logger.error(f"Error managing positions for {symbol}: {e}")
    
    async def _manage_single_position(
        self,
        account,
        position: Dict[str, Any],
        symbol: str,
        market_data: Dict[str, Any],
        strategy,
        money_management,
        account_info: AccountInfo,
        trade_history: List[Dict[str, Any]],
        ai_client,
        mt5_client
    ):
        """Manage a single position using AI"""
        try:
            # Log position monitoring
            trading_logger.log_position_monitoring(
                account.account_id,
                symbol,
                position.get('ticket', 0),
                market_data.get('current_price', 0),
                position.get('profit', 0),
                "AI_ANALYSIS"
            )

            # Build management prompt
            management_prompt = self._build_management_prompt(
                position, symbol, market_data, strategy, money_management,
                account_info, trade_history
            )
            
            # Get AI decision
            start_time = time.time()
            ai_response = await ai_client.generate_trading_decision(management_prompt)
            processing_time = time.time() - start_time
            
            # Log AI decision
            trading_logger.log_ai_decision(
                account.account_id,
                symbol,
                f"{strategy.strategy_type.value}_MANAGEMENT",
                len(management_prompt),
                ai_response,
                processing_time
            )
            
            # Execute management decision
            await self._execute_management_decision(
                account, position, ai_response, mt5_client
            )
            
        except Exception as e:
            logger.error(f"Error managing position {position.get('ticket')}: {e}")
            trading_logger.log_ai_error(
                account.account_id,
                symbol,
                str(e)
            )
    
    def _build_management_prompt(
        self,
        position: Dict[str, Any],
        symbol: str,
        market_data: Dict[str, Any],
        strategy,
        money_management,
        account_info: AccountInfo,
        trade_history: List[Dict[str, Any]]
    ) -> str:
        """Build AI prompt for trade management"""
        
        # Calculate position metrics
        current_price = market_data['current_price']
        entry_price = position['price_open']
        position_type = position['type']
        
        if position_type == 'BUY':
            unrealized_pips = (current_price - entry_price) / market_data.get('pip_size', 0.0001)
        else:
            unrealized_pips = (entry_price - current_price) / market_data.get('pip_size', 0.0001)
        
        # Calculate position age
        position_time = position.get('time', datetime.now())
        if isinstance(position_time, datetime):
            position_age_hours = (datetime.now() - position_time).total_seconds() / 3600
        else:
            position_age_hours = 0
        
        prompt = f"""
TRADE MANAGEMENT ANALYSIS

CURRENT POSITION:
- Ticket: {position['ticket']}
- Symbol: {symbol}
- Type: {position_type}
- Volume: {position['volume']}
- Entry Price: {entry_price}
- Current Price: {current_price}
- Unrealized P&L: ${position.get('profit', 0):.2f}
- Unrealized Pips: {unrealized_pips:.1f}
- Stop Loss: {position.get('sl', 'None')}
- Take Profit: {position.get('tp', 'None')}
- Position Age: {position_age_hours:.1f} hours
- Swap: ${position.get('swap', 0):.2f}

ACCOUNT INFORMATION:
- Balance: ${account_info.balance:.2f}
- Equity: ${account_info.equity:.2f}
- Margin Level: {account_info.margin_level:.1f}%

CURRENT MARKET CONDITIONS:
- Current Price: {current_price}
- Spread: {market_data.get('spread', 0)} pips
- Volatility: {market_data.get('volatility', 0):.5f}

RECENT MARKET DATA (Last 10 candles):
"""
        
        # Add recent candles
        recent_candles = market_data['candles'][-10:] if market_data.get('candles') else []
        for i, candle in enumerate(recent_candles):
            prompt += f"Candle {i+1}: O:{candle.get('open', 0)} H:{candle.get('high', 0)} L:{candle.get('low', 0)} C:{candle.get('close', 0)}\n"
        
        prompt += f"""

STRATEGY CONTEXT:
- Strategy: {strategy.strategy_type.value}
- Money Management: {money_management.strategy_type.value}

RECENT PERFORMANCE (Last 10 trades):
"""
        
        # Add recent trade performance
        recent_trades = trade_history[-10:] if trade_history else []
        total_profit = sum(trade.get('profit', 0) for trade in recent_trades)
        winning_trades = sum(1 for trade in recent_trades if trade.get('profit', 0) > 0)
        
        prompt += f"- Total Recent Trades: {len(recent_trades)}\n"
        prompt += f"- Winning Trades: {winning_trades}\n"
        prompt += f"- Win Rate: {(winning_trades/len(recent_trades)*100) if recent_trades else 0:.1f}%\n"
        prompt += f"- Total Profit: ${total_profit:.2f}\n"
        
        prompt += """

MANAGEMENT DECISION REQUIRED:

Based on the current position status, market conditions, and strategy requirements, decide on the best management action:

1. HOLD - Keep the position unchanged
2. CLOSE - Close the position immediately
3. MODIFY_SL - Modify stop loss (provide new SL level)
4. MODIFY_TP - Modify take profit (provide new TP level)
5. MODIFY_BOTH - Modify both SL and TP

Consider:
- Is the position still aligned with the strategy?
- Should stop loss be moved to breakeven or trailed?
- Is it time to take partial profits?
- Are there signs of reversal or continuation?
- Risk management requirements

Respond in JSON format:
{
    "action": "HOLD|CLOSE|MODIFY_SL|MODIFY_TP|MODIFY_BOTH",
    "confidence": 0.0-1.0,
    "new_stop_loss": number or null,
    "new_take_profit": number or null,
    "reasoning": "detailed explanation for the decision",
    "risk_level": "LOW|MEDIUM|HIGH"
}
"""
        
        return prompt
    
    async def _execute_management_decision(
        self,
        account,
        position: Dict[str, Any],
        ai_response: Dict[str, Any],
        mt5_client
    ):
        """Execute the AI management decision"""
        try:
            action = ai_response.get('action', 'HOLD')
            ticket = position['ticket']
            
            if action == 'CLOSE':
                success = mt5_client.close_position(ticket)
                if success:
                    logger.info(f"Position {ticket} closed successfully")
                else:
                    logger.error(f"Failed to close position {ticket}")
            
            elif action in ['MODIFY_SL', 'MODIFY_TP', 'MODIFY_BOTH']:
                new_sl = ai_response.get('new_stop_loss')
                new_tp = ai_response.get('new_take_profit')

                # Validate modification parameters
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    logger.info(f"🔍 VALIDATION: Modification attempt {retry_count + 1}/{max_retries} for position {ticket}")

                    # Validate the modification
                    validation_result = self.trade_validator.validate_position_modification(
                        ticket=ticket,
                        new_stop_loss=new_sl,
                        new_take_profit=new_tp
                    )

                    if validation_result.result == ValidationResult.INVALID:
                        logger.error(f"❌ VALIDATION: Position modification validation failed for ticket {ticket}")
                        for error in validation_result.errors:
                            logger.error(f"❌ VALIDATION: {error.error_type}: {error.message}")
                        break

                    # Check if no changes are needed (prevents MT5 error 10025)
                    if validation_result.corrected_params and validation_result.corrected_params.get("no_changes_needed"):
                        logger.info(f"✅ Position {ticket} - no changes needed, skipping modification")
                        success = True
                        break

                    # Use corrected parameters if available
                    final_sl = new_sl
                    final_tp = new_tp

                    if validation_result.corrected_params:
                        if 'stop_loss' in validation_result.corrected_params:
                            final_sl = validation_result.corrected_params['stop_loss']
                            logger.info(f"🔍 VALIDATION: Corrected stop loss from {new_sl} to {final_sl}")
                        if 'take_profit' in validation_result.corrected_params:
                            final_tp = validation_result.corrected_params['take_profit']
                            logger.info(f"🔍 VALIDATION: Corrected take profit from {new_tp} to {final_tp}")

                    # Attempt modification with validated/corrected parameters
                    success = mt5_client.modify_position(
                        ticket,
                        stop_loss=final_sl,
                        take_profit=final_tp
                    )

                    if success:
                        logger.info(f"✅ Position {ticket} modified successfully")
                        break
                    else:
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"⚠️ Position modification failed, retrying... ({retry_count}/{max_retries})")
                            await asyncio.sleep(1)  # Wait 1 second before retry
                        else:
                            logger.error(f"❌ Failed to modify position {ticket} after {max_retries} attempts")
            
            elif action == 'HOLD':
                logger.debug(f"Holding position {ticket} unchanged")
            
            else:
                logger.warning(f"Unknown management action: {action}")
            
        except Exception as e:
            logger.error(f"Error executing management decision: {e}")

    async def _manage_symbol_orders(
        self,
        account,
        symbol: str,
        orders: List[Dict[str, Any]],
        ai_client,
        mt5_client
    ):
        """Manage pending orders for a specific symbol"""
        try:
            logger.info(f"Managing {len(orders)} pending orders for {symbol}")

            # Get symbol configuration for this account
            symbol_config = None
            for config in account.symbols:
                if config['symbol'] == symbol:
                    symbol_config = config
                    break

            if not symbol_config:
                logger.warning(f"Symbol {symbol} not configured for account {account.account_id}, but managing existing orders for safety")
                # Create a default config for unconfigured symbols to manage existing orders
                symbol_config = {'symbol': symbol, 'timeframe': 'M15'}

            # Skip if no actual orders to manage
            if not orders:
                logger.debug(f"No orders to manage for {symbol}")
                return

            # Get strategy and money management with proper types and configs
            from strategies.base_strategy import StrategyType
            from money_management.base_strategy import MoneyManagementType

            # Convert string types to enums
            strategy_type_map = {
                'trend_following': StrategyType.TREND_FOLLOWING,
                'mean_reversion': StrategyType.MEAN_REVERSION,
                'breakout': StrategyType.BREAKOUT,
                'scalping': StrategyType.MEAN_REVERSION,  # Default scalping to mean reversion
            }

            mm_type_map = {
                'fixed_volume': MoneyManagementType.FIXED_VOLUME,
                'percent_risk': MoneyManagementType.PERCENT_RISK,
                'martingale': MoneyManagementType.MARTINGALE,
                'anti_martingale': MoneyManagementType.ANTI_MARTINGALE,
                'fixed_lot': MoneyManagementType.FIXED_VOLUME,  # Alias
            }

            strategy_type_enum = strategy_type_map.get(account.strategy_type, StrategyType.MEAN_REVERSION)
            mm_type_enum = mm_type_map.get(account.money_management_type, MoneyManagementType.FIXED_VOLUME)

            # Get default configs and merge with account config
            strategy_config = self.account_manager.strategy_factory.get_default_config(strategy_type_enum)
            strategy_config.update(account.money_management_settings.get('strategy_config', {}))  # FIXED: Use standardized field name

            mm_config = self.account_manager.money_management_factory.get_default_config(mm_type_enum)
            mm_config.update(account.money_management_settings)  # FIXED: Use standardized field name

            strategy = self.account_manager.strategy_factory.create_strategy(strategy_type_enum, strategy_config)
            money_management = self.account_manager.money_management_factory.create_strategy(mm_type_enum, mm_config)

            if not strategy or not money_management:
                logger.error(f"Failed to create strategy/MM for account {account.account_id}")
                return

            # Get current market data
            market_data_dict = mt5_client.get_market_data(
                symbol, symbol_config['timeframe'], 50
            )
            if not market_data_dict:
                logger.error(f"Failed to get market data for {symbol}")
                return

            # Get account info
            account_balance = mt5_client.get_account_info()
            if not account_balance:
                logger.error(f"Failed to get account info")
                return

            account_info = AccountInfo(
                balance=account_balance.balance,
                equity=account_balance.equity,
                margin=account_balance.margin,
                free_margin=account_balance.free_margin,
                margin_level=account_balance.margin_level,
                currency=account_balance.currency,
                leverage=account_balance.leverage
            )

            # Get trade history for context
            trade_history = mt5_client.get_trade_history(7)  # Last 7 days
            strategy_trades = [
                trade for trade in trade_history
                if trade.get('magic_number') == strategy.magic_number
            ]

            # Process each pending order
            for order in orders:
                await self._manage_single_order(
                    account, order, symbol, market_data_dict,
                    strategy, money_management, account_info,
                    strategy_trades, ai_client, mt5_client
                )

        except Exception as e:
            logger.error(f"Error managing orders for {symbol}: {e}")

    async def _manage_single_order(
        self,
        account,
        order: Dict[str, Any],
        symbol: str,
        market_data: Dict[str, Any],
        strategy,
        money_management,
        account_info: AccountInfo,
        trade_history: List[Dict[str, Any]],
        ai_client,
        mt5_client
    ):
        """Manage a single pending order using AI"""
        try:
            # Log order monitoring
            trading_logger.log_system_event(
                "ORDER_MONITORING",
                f"Analyzing pending order {order.get('ticket', 0)} for {symbol}",
                "INFO"
            )

            # Build management prompt for pending order
            management_prompt = self._build_order_management_prompt(
                account, order, symbol, market_data, strategy, money_management,
                account_info, trade_history
            )

            # Get AI decision
            start_time = time.time()
            ai_response = await ai_client.generate_trading_decision(management_prompt)
            processing_time = time.time() - start_time

            # Log AI decision
            trading_logger.log_ai_decision(
                account.account_id,
                symbol,
                f"{strategy.strategy_type.value}_ORDER_MANAGEMENT",
                len(management_prompt),
                ai_response,
                processing_time
            )

            # Execute order management decision
            await self._execute_order_management_decision(
                account, order, ai_response, mt5_client
            )

        except Exception as e:
            logger.error(f"Error managing order {order.get('ticket')}: {e}")
            trading_logger.log_ai_error(
                account.account_id,
                symbol,
                str(e)
            )

    def _build_order_management_prompt(
        self,
        account,
        order: Dict[str, Any],
        symbol: str,
        market_data: Dict[str, Any],
        strategy,
        money_management,
        account_info: AccountInfo,
        trade_history: List[Dict[str, Any]]
    ) -> str:
        """Build AI prompt for pending order management"""

        # Calculate order metrics
        current_price = market_data['current_price']
        order_price = order['price_open']
        order_type = order['type']

        # Calculate distance from current price
        if order_type in [2, 4]:  # BUY_LIMIT or BUY_STOP
            distance_pips = (order_price - current_price) / market_data.get('pip_size', 0.0001)
        else:  # SELL_LIMIT or SELL_STOP
            distance_pips = (current_price - order_price) / market_data.get('pip_size', 0.0001)

        # Calculate order age
        order_time = order.get('time_setup', datetime.now().timestamp())
        if isinstance(order_time, datetime):
            order_age_hours = (datetime.now() - order_time).total_seconds() / 3600
        elif isinstance(order_time, (int, float)):
            order_age_hours = (datetime.now().timestamp() - order_time) / 3600
        else:
            order_age_hours = 0

        # Get order type string
        order_type_map = {
            2: "BUY_LIMIT",
            3: "SELL_LIMIT",
            4: "BUY_STOP",
            5: "SELL_STOP"
        }
        order_type_str = order_type_map.get(order_type, "UNKNOWN")

        # Build comprehensive prompt
        prompt = f"""
PENDING ORDER MANAGEMENT ANALYSIS

ACCOUNT INFORMATION:
- Account ID: {account.account_id}
- Balance: ${account_info.balance:.2f}
- Equity: ${account_info.equity:.2f}
- Margin Level: {account_info.margin_level:.1f}%
- Currency: {account_info.currency}

PENDING ORDER DETAILS:
- Ticket: {order.get('ticket', 'N/A')}
- Symbol: {symbol}
- Type: {order_type_str}
- Volume: {order.get('volume', 0):.2f}
- Order Price: {order_price:.5f}
- Stop Loss: {order.get('sl', 0):.5f}
- Take Profit: {order.get('tp', 0):.5f}
- Magic Number: {order.get('magic', 0)}
- Age: {order_age_hours:.1f} hours

CURRENT MARKET CONDITIONS:
- Current Price: {current_price:.5f}
- Distance from Order: {distance_pips:.1f} pips
- Spread: {market_data.get('spread', 0):.1f} pips
- Volatility: {market_data.get('volatility', 0):.5f}
- Market Trend: {"Bullish" if len(market_data.get('candles', [])) > 0 and market_data['candles'][-1]['close'] > market_data['candles'][-10]['close'] else "Bearish"}

STRATEGY CONTEXT:
- Strategy: {strategy.strategy_type.value}
- Risk Management: {money_management.strategy_type.value}
- Recent Strategy Trades: {len(trade_history)}

RECENT MARKET DATA:
"""

        # Add recent candle data
        candles = market_data.get('candles', [])
        if candles:
            for i, candle in enumerate(candles[-5:], 1):
                prompt += f"- Candle {i}: O:{candle['open']:.5f} H:{candle['high']:.5f} L:{candle['low']:.5f} C:{candle['close']:.5f}\n"

        prompt += f"""

ORDER MANAGEMENT DECISION REQUIRED:

Based on the current market conditions, order status, and strategy requirements, decide on the best management action:

1. KEEP - Keep the order unchanged
2. CANCEL - Cancel the order completely
3. MODIFY_PRICE - Modify the order price (provide new price level)
4. MODIFY_SL - Modify stop loss (provide new SL level)
5. MODIFY_TP - Modify take profit (provide new TP level)
6. MODIFY_ALL - Modify price, SL, and TP

Consider:
- Is the order still relevant given current market conditions?
- Has the market moved significantly since order placement?
- Should the order price be adjusted to current market levels?
- Are the stop loss and take profit levels still appropriate?
- Is the order too old and should be cancelled?
- Risk management requirements

Respond in JSON format:
{{
    "action": "KEEP|CANCEL|MODIFY_PRICE|MODIFY_SL|MODIFY_TP|MODIFY_ALL",
    "confidence": 0.0-1.0,
    "new_price": number or null,
    "new_stop_loss": number or null,
    "new_take_profit": number or null,
    "reasoning": "detailed explanation for the decision",
    "risk_level": "LOW|MEDIUM|HIGH"
}}
"""

        return prompt

    async def _execute_order_management_decision(
        self,
        account,
        order: Dict[str, Any],
        ai_response: Dict[str, Any],
        mt5_client
    ):
        """Execute the AI order management decision"""
        try:
            action = ai_response.get('action', 'KEEP')
            ticket = order['ticket']

            if action == 'CANCEL':
                success = mt5_client.cancel_order(ticket)
                if success:
                    logger.info(f"Order {ticket} cancelled successfully")
                    trading_logger.log_system_event(
                        "ORDER_CANCELLED",
                        f"AI cancelled pending order {ticket}",
                        "INFO"
                    )
                else:
                    logger.error(f"Failed to cancel order {ticket}")

            elif action in ['MODIFY_PRICE', 'MODIFY_SL', 'MODIFY_TP', 'MODIFY_ALL']:
                new_price = ai_response.get('new_price')
                new_sl = ai_response.get('new_stop_loss')
                new_tp = ai_response.get('new_take_profit')

                # Validate modification parameters
                if action == 'MODIFY_PRICE' and new_price is None:
                    logger.error(f"MODIFY_PRICE action requires new_price parameter")
                    return
                elif action == 'MODIFY_SL' and new_sl is None:
                    logger.error(f"MODIFY_SL action requires new_stop_loss parameter")
                    return
                elif action == 'MODIFY_TP' and new_tp is None:
                    logger.error(f"MODIFY_TP action requires new_take_profit parameter")
                    return

                # Attempt modification with enhanced error handling
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    try:
                        success = await mt5_client.modify_order(
                            ticket=ticket,
                            price=new_price if action in ['MODIFY_PRICE', 'MODIFY_ALL'] else None,
                            stop_loss=new_sl if action in ['MODIFY_SL', 'MODIFY_ALL'] else None,
                            take_profit=new_tp if action in ['MODIFY_TP', 'MODIFY_ALL'] else None
                        )

                        if success:
                            logger.info(f"✅ Order {ticket} modified successfully on attempt {retry_count + 1}")
                            trading_logger.log_system_event(
                                "ORDER_MODIFIED",
                                f"AI modified pending order {ticket}: {action} (attempt {retry_count + 1})",
                                "INFO"
                            )
                            break
                        else:
                            retry_count += 1
                            if retry_count < max_retries:
                                logger.warning(f"⚠️ Order {ticket} modification failed, retrying ({retry_count}/{max_retries})")
                                await asyncio.sleep(2)  # Wait 2 seconds before retry
                            else:
                                logger.error(f"❌ Failed to modify order {ticket} after {max_retries} attempts")
                                # Log the failure for analysis
                                trading_logger.log_system_event(
                                    "ORDER_MODIFICATION_FAILED",
                                    f"Failed to modify order {ticket} after {max_retries} attempts: {action}",
                                    "ERROR"
                                )
                    except Exception as e:
                        retry_count += 1
                        logger.error(f"❌ Exception during order {ticket} modification attempt {retry_count}: {e}")
                        if retry_count >= max_retries:
                            break
                        await asyncio.sleep(2)

            elif action == 'KEEP':
                logger.debug(f"Keeping order {ticket} unchanged")

            else:
                logger.warning(f"Unknown order management action: {action}")

        except Exception as e:
            logger.error(f"Error executing order management decision: {e}")

    async def _check_missing_position_closures(self, account, mt5_client):
        """Check for positions that were closed but not logged properly"""
        try:

            # Get recent deals (last 4 hours) to check for closed positions
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=4)

            deals = mt5.history_deals_get(start_time, end_time)
            if not deals:
                return

            # Filter deals for this account and look for position closures
            for deal in deals:
                if deal.profit != 0 and deal.entry == mt5.DEAL_ENTRY_OUT:  # Position closure
                    # Check if this closure was logged
                    # Safely convert time to datetime
                    if isinstance(deal.time, datetime):
                        deal_time = deal.time
                    elif isinstance(deal.time, (int, float)):
                        deal_time = datetime.fromtimestamp(deal.time)
                    else:
                        deal_time = datetime.now()

                    # Log significant losses that might have been missed
                    if deal.profit < -1.0:  # Loss > $1
                        logger.warning(f"🔍 DETECTED UNLOGGED LOSS: Account {account.account_id} - "
                                     f"Position {deal.position_id} closed at {deal_time.strftime('%H:%M:%S')} "
                                     f"with loss ${deal.profit:.2f}")

                        # Log this closure retroactively
                        trading_logger.log_trade_close(
                            account.account_id,
                            deal.position_id,
                            deal.symbol,
                            deal.volume,
                            0,  # Entry price not available in deal
                            deal.price,
                            deal.profit,
                            "Auto-SL/TP (Retroactive Log)"
                        )

                        # Log system event for balance discrepancy investigation
                        trading_logger.log_system_event(
                            "RETROACTIVE_CLOSURE_DETECTED",
                            f"Account {account.account_id}: Detected unlogged position closure "
                            f"Position {deal.position_id} - Loss: ${deal.profit:.2f}",
                            "WARNING"
                        )

                    elif deal.profit > 1.0:  # Profit > $1
                        logger.info(f"🔍 DETECTED UNLOGGED PROFIT: Account {account.account_id} - "
                                   f"Position {deal.position_id} closed at {deal_time.strftime('%H:%M:%S')} "
                                   f"with profit ${deal.profit:.2f}")

                        # Log this closure retroactively
                        trading_logger.log_trade_close(
                            account.account_id,
                            deal.position_id,
                            deal.symbol,
                            deal.volume,
                            0,  # Entry price not available in deal
                            deal.price,
                            deal.profit,
                            "Auto-TP (Retroactive Log)"
                        )

        except Exception as e:
            logger.error(f"Error checking missing position closures for {account.account_id}: {e}")

    def stop(self):
        """Stop the trade management system"""
        self.running = False
        logger.info("Trade management system stop requested")
