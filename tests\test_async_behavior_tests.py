#!/usr/bin/env python3
"""
Comprehensive Async Behavior and Race Condition Tests
Tests async behavior verification and race condition detection
"""

import unittest
import asyncio
import sys
import os
import time
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all components
from ai_integration.qwen_client import QwenClient
from signal_generation.signal_generator import SignalGenerator
from trade_management.trade_manager import TradeManager


class TestAsyncBehavior(unittest.TestCase):
    """Test async behavior of the trading system"""
    
    def setUp(self):
        """Set up test data"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """Clean up"""
        self.loop.close()
    
    @patch('aiohttp.ClientSession.post')
    def test_qwen_client_concurrent_requests(self, mock_post):
        """Test QwenClient handling concurrent requests"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={
            'choices': [{
                'message': {
                    'content': '''
                    {
                        "action": "HOLD",
                        "confidence": 0.5,
                        "entry_price": null,
                        "stop_loss": null,
                        "take_profit": null,
                        "reasoning": "Concurrent test response",
                        "risk_level": "LOW"
                    }
                    '''
                }
            }]
        })
        
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async def run_concurrent_requests():
            async with QwenClient() as client:
                # Create multiple concurrent requests
                tasks = []
                for i in range(10):
                    task = client.generate_trading_decision(f"Test prompt {i}")
                    tasks.append(task)
                
                # Wait for all requests to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify all requests completed successfully
                for result in results:
                    self.assertIsInstance(result, dict)
                    self.assertEqual(result['action'], 'HOLD')
                    self.assertEqual(result['confidence'], 0.5)
                
                return len(results)
        
        # Run the test
        result_count = self.loop.run_until_complete(run_concurrent_requests())
        self.assertEqual(result_count, 10)
    
    def test_qwen_client_semaphore_limiting(self):
        """Test QwenClient semaphore limits concurrent requests"""
        client = QwenClient()
        
        # Verify semaphore is created with correct limit
        self.assertIsNotNone(client.semaphore)
        self.assertEqual(client.semaphore._value, 5)  # Default max_concurrent_requests
    
    @patch('signal_generation.signal_generator.MT5Client')
    @patch('signal_generation.signal_generator.QwenClient')
    def test_signal_generator_async_processing(self, mock_qwen_client, mock_mt5_client):
        """Test signal generator async processing"""
        # Mock account manager
        account_manager = Mock()
        account_manager.get_all_accounts.return_value = [
            Mock(
                account_id="test_account_1",
                strategy_type="trend_following",
                money_management_type="percent_risk",
                symbols=[{"symbol": "EURUSD", "timeframe": "H1"}],
                trading_enabled=True
            ),
            Mock(
                account_id="test_account_2",
                strategy_type="mean_reversion",
                money_management_type="fixed_volume",
                symbols=[{"symbol": "GBPUSD", "timeframe": "M15"}],
                trading_enabled=True
            )
        ]
        
        # Mock MT5 client
        mock_mt5_instance = Mock()
        mock_mt5_instance.initialize.return_value = True
        mock_mt5_instance.get_market_data.return_value = {
            'symbol': 'EURUSD',
            'candles': [],
            'current_price': 1.1000,
            'spread': 1.5,
            'volume': 1000,
            'volatility': 0.0015
        }
        mock_mt5_client.return_value = mock_mt5_instance
        
        # Mock Qwen client
        mock_qwen_instance = Mock()
        mock_qwen_instance.generate_trading_decision = AsyncMock(return_value={
            'action': 'HOLD',
            'confidence': 0.5,
            'reasoning': 'Test response'
        })
        mock_qwen_client.return_value.__aenter__.return_value = mock_qwen_instance
        
        signal_generator = SignalGenerator(account_manager)
        
        async def test_async_signal_processing():
            # Process multiple account groups concurrently
            tasks = []
            for i in range(3):
                task = signal_generator._process_account_group(
                    accounts=[account_manager.get_all_accounts()[0]],
                    strategy_type="trend_following",
                    money_management_type="percent_risk"
                )
                tasks.append(task)
            
            # Wait for all tasks to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            
            return True
        
        result = self.loop.run_until_complete(test_async_signal_processing())
        self.assertTrue(result)


class TestRaceConditionDetection(unittest.TestCase):
    """Test race condition detection and prevention"""
    
    def setUp(self):
        """Set up test data"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """Clean up"""
        self.loop.close()
    
    def test_concurrent_signal_generation_timing(self):
        """Test timing conflicts in concurrent signal generation"""
        # Mock account manager
        account_manager = Mock()
        account_manager.get_all_accounts.return_value = [
            Mock(account_id=f"test_account_{i}", trading_enabled=True)
            for i in range(5)
        ]
        
        signal_generator = SignalGenerator(account_manager)
        
        # Track timing of signal generation attempts
        timing_results = []
        
        async def timed_signal_check(account_id: str, delay: float):
            """Simulate signal generation with timing"""
            await asyncio.sleep(delay)
            start_time = time.time()
            
            # Simulate signal generation logic
            signal_key = f"EURUSD_H1_trend_following_percent_risk_{account_id}"
            should_generate = signal_generator._should_generate_signal(signal_key)
            
            end_time = time.time()
            timing_results.append({
                'account_id': account_id,
                'start_time': start_time,
                'end_time': end_time,
                'should_generate': should_generate,
                'signal_key': signal_key
            })
            
            return should_generate
        
        async def test_concurrent_timing():
            # Create concurrent signal generation attempts
            tasks = []
            for i in range(5):
                # Stagger the start times slightly
                delay = i * 0.01  # 10ms apart
                task = timed_signal_check(f"account_{i}", delay)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            return results
        
        results = self.loop.run_until_complete(test_concurrent_timing())
        
        # Verify timing results
        self.assertEqual(len(timing_results), 5)
        self.assertEqual(len(results), 5)
        
        # Check for potential race conditions (overlapping execution times)
        for i in range(len(timing_results) - 1):
            current = timing_results[i]
            next_item = timing_results[i + 1]
            
            # Verify proper sequencing
            self.assertLessEqual(current['start_time'], next_item['start_time'])
    
    def test_signal_interval_enforcement(self):
        """Test signal interval enforcement prevents race conditions"""
        account_manager = Mock()
        signal_generator = SignalGenerator(account_manager)
        
        signal_key = "EURUSD_H1_test_strategy_test_mm"
        
        # First call should allow signal generation
        first_check = signal_generator._should_generate_signal(signal_key)
        self.assertTrue(first_check)
        
        # Update last signal time
        signal_generator.last_signal_time[signal_key] = datetime.now()
        
        # Immediate second call should be blocked
        second_check = signal_generator._should_generate_signal(signal_key)
        self.assertFalse(second_check)
        
        # Simulate time passing (mock the interval check)
        old_time = datetime.now() - timedelta(minutes=signal_generator.min_signal_interval + 1)
        signal_generator.last_signal_time[signal_key] = old_time
        
        # Third call after interval should be allowed
        third_check = signal_generator._should_generate_signal(signal_key)
        self.assertTrue(third_check)
    
    @patch('trade_management.trade_manager.MT5Client')
    def test_concurrent_trade_management(self, mock_mt5_client):
        """Test concurrent trade management operations"""
        # Mock account manager
        account_manager = Mock()
        account_manager.get_all_accounts.return_value = [
            Mock(
                account_id=f"test_account_{i}",
                account_number=12345 + i,
                trading_enabled=True
            )
            for i in range(3)
        ]
        
        # Mock MT5 client
        mock_mt5_instance = Mock()
        mock_mt5_instance.login.return_value = True
        mock_mt5_instance.get_positions.return_value = []
        mock_mt5_instance.get_orders.return_value = []
        mock_mt5_instance.get_account_info.return_value = Mock(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        mock_mt5_client.return_value = mock_mt5_instance
        
        trade_manager = TradeManager(account_manager)
        
        # Track concurrent operations
        operation_times = []
        
        async def timed_account_management(account):
            """Time trade management for an account"""
            start_time = time.time()
            
            # Mock QwenClient for this test
            mock_qwen = Mock()
            mock_qwen.generate_trading_decision = AsyncMock(return_value={
                'action': 'HOLD',
                'confidence': 0.5,
                'reasoning': 'Test response'
            })
            
            await trade_manager._manage_account_trades(account, mock_qwen)
            
            end_time = time.time()
            operation_times.append({
                'account_id': account.account_id,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time
            })
        
        async def test_concurrent_trade_management():
            accounts = account_manager.get_all_accounts()
            tasks = [timed_account_management(account) for account in accounts]
            
            await asyncio.gather(*tasks, return_exceptions=True)
            return len(operation_times)
        
        result_count = self.loop.run_until_complete(test_concurrent_trade_management())
        
        # Verify all accounts were processed
        self.assertEqual(result_count, 3)
        
        # Verify no excessive overlap (potential race conditions)
        for operation in operation_times:
            self.assertGreater(operation['duration'], 0)
            self.assertLess(operation['duration'], 10)  # Should complete within 10 seconds


class TestAsyncErrorHandling(unittest.TestCase):
    """Test async error handling and recovery"""
    
    def setUp(self):
        """Set up test data"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """Clean up"""
        self.loop.close()
    
    @patch('aiohttp.ClientSession.post')
    def test_qwen_client_timeout_handling(self, mock_post):
        """Test QwenClient timeout handling"""
        # Mock timeout exception
        mock_post.side_effect = asyncio.TimeoutError("Request timeout")
        
        async def test_timeout():
            async with QwenClient() as client:
                result = await client.generate_trading_decision("Test prompt")
                
                # Should return error response on timeout
                self.assertEqual(result['action'], 'HOLD')
                self.assertEqual(result['confidence'], 0.0)
                self.assertIn('timeout', result.get('error', '').lower())
                
                return result
        
        result = self.loop.run_until_complete(test_timeout())
        self.assertIsInstance(result, dict)
    
    @patch('aiohttp.ClientSession.post')
    def test_qwen_client_exception_handling(self, mock_post):
        """Test QwenClient general exception handling"""
        # Mock general exception
        mock_post.side_effect = Exception("Network error")
        
        async def test_exception():
            async with QwenClient() as client:
                result = await client.generate_trading_decision("Test prompt")
                
                # Should return error response on exception
                self.assertEqual(result['action'], 'HOLD')
                self.assertEqual(result['confidence'], 0.0)
                self.assertIn('error', result)
                
                return result
        
        result = self.loop.run_until_complete(test_exception())
        self.assertIsInstance(result, dict)


if __name__ == '__main__':
    # Run tests with detailed output
    unittest.main(verbosity=2)
