#!/usr/bin/env python3
"""
AI Response Cache System
Intelligent caching for AI responses to reduce API costs and improve performance
"""

import hashlib
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from performance.cache_manager import cache_manager, CacheType
from logging_system.logging_standards import create_logger, LogLevel, PerformanceTimer

logger = create_logger("AICache")

@dataclass
class AIPromptSignature:
    """Signature for AI prompts to enable intelligent caching"""
    strategy_type: str
    money_management_type: str
    symbol: str
    timeframe: str
    market_conditions_hash: str  # Hash of key market indicators
    account_balance_range: str   # Categorized balance range
    risk_settings_hash: str      # Hash of risk management settings

class AIResponseCache:
    """Intelligent caching system for AI responses"""
    
    def __init__(self):
        self.similarity_threshold = 0.85  # Threshold for considering prompts similar
        self.balance_ranges = [
            (0, 100), (100, 500), (500, 1000), (1000, 5000), 
            (5000, 10000), (10000, 50000), (50000, float('inf'))
        ]
    
    def _categorize_balance(self, balance: float) -> str:
        """Categorize account balance into ranges for caching"""
        for min_bal, max_bal in self.balance_ranges:
            if min_bal <= balance < max_bal:
                return f"{min_bal}-{max_bal}"
        return "unknown"
    
    def _hash_market_conditions(self, market_data: Dict[str, Any]) -> str:
        """Create hash of key market conditions for caching"""
        # Extract key indicators that affect trading decisions
        key_indicators = {
            'volatility_level': self._categorize_volatility(market_data.get('volatility', 0)),
            'trend_direction': self._detect_trend_direction(market_data.get('candles', [])),
            'spread_level': self._categorize_spread(market_data.get('spread', 0)),
            'volume_level': self._categorize_volume(market_data.get('volume', 0))
        }
        
        return hashlib.md5(json.dumps(key_indicators, sort_keys=True).encode()).hexdigest()[:16]
    
    def _categorize_volatility(self, volatility: float) -> str:
        """Categorize volatility into levels"""
        if volatility < 0.0001:
            return "very_low"
        elif volatility < 0.0005:
            return "low"
        elif volatility < 0.001:
            return "medium"
        elif volatility < 0.002:
            return "high"
        else:
            return "very_high"
    
    def _detect_trend_direction(self, candles: List[Dict]) -> str:
        """Detect basic trend direction from recent candles"""
        if len(candles) < 10:
            return "unknown"
        
        recent_closes = [candle['close'] for candle in candles[-10:]]
        
        # Simple trend detection
        if recent_closes[-1] > recent_closes[0] * 1.001:  # 0.1% threshold
            return "uptrend"
        elif recent_closes[-1] < recent_closes[0] * 0.999:
            return "downtrend"
        else:
            return "sideways"
    
    def _categorize_spread(self, spread: float) -> str:
        """Categorize spread into levels"""
        if spread < 1:
            return "tight"
        elif spread < 3:
            return "normal"
        elif spread < 5:
            return "wide"
        else:
            return "very_wide"
    
    def _categorize_volume(self, volume: int) -> str:
        """Categorize volume into levels"""
        if volume < 100:
            return "low"
        elif volume < 1000:
            return "normal"
        elif volume < 5000:
            return "high"
        else:
            return "very_high"
    
    def _hash_risk_settings(self, risk_settings: Dict[str, Any]) -> str:
        """Create hash of risk management settings"""
        # Extract key risk parameters
        key_settings = {
            'risk_percent': round(risk_settings.get('risk_percent', 2.0), 1),
            'max_daily_trades': risk_settings.get('max_daily_trades', 10),
            'max_open_positions': risk_settings.get('max_open_positions', 5),
            'max_daily_loss': round(risk_settings.get('max_daily_loss', 5.0), 1)
        }
        
        return hashlib.md5(json.dumps(key_settings, sort_keys=True).encode()).hexdigest()[:16]
    
    def create_prompt_signature(
        self,
        strategy_type: str,
        money_management_type: str,
        symbol: str,
        timeframe: str,
        market_data: Dict[str, Any],
        account_balance: float,
        risk_settings: Dict[str, Any]
    ) -> AIPromptSignature:
        """Create a signature for the AI prompt to enable caching"""
        
        return AIPromptSignature(
            strategy_type=strategy_type,
            money_management_type=money_management_type,
            symbol=symbol,
            timeframe=timeframe,
            market_conditions_hash=self._hash_market_conditions(market_data),
            account_balance_range=self._categorize_balance(account_balance),
            risk_settings_hash=self._hash_risk_settings(risk_settings)
        )
    
    async def get_cached_response(self, signature: AIPromptSignature) -> Optional[Dict[str, Any]]:
        """Get cached AI response if available"""
        
        # Try exact match first
        exact_response = await cache_manager.get(
            CacheType.AI_RESPONSE,
            strategy_type=signature.strategy_type,
            money_management_type=signature.money_management_type,
            symbol=signature.symbol,
            timeframe=signature.timeframe,
            market_conditions=signature.market_conditions_hash,
            balance_range=signature.account_balance_range,
            risk_settings=signature.risk_settings_hash
        )
        
        if exact_response:
            logger.log_performance_metric(
                "ai_cache_exact_hit", 
                1, 
                context={
                    'strategy': signature.strategy_type,
                    'symbol': signature.symbol,
                    'timeframe': signature.timeframe
                }
            )
            return exact_response
        
        # Try similar match (relaxed market conditions)
        similar_response = await self._find_similar_cached_response(signature)
        if similar_response:
            logger.log_performance_metric(
                "ai_cache_similar_hit", 
                1, 
                context={
                    'strategy': signature.strategy_type,
                    'symbol': signature.symbol,
                    'timeframe': signature.timeframe
                }
            )
            return similar_response
        
        logger.log_performance_metric(
            "ai_cache_miss", 
            1, 
            context={
                'strategy': signature.strategy_type,
                'symbol': signature.symbol,
                'timeframe': signature.timeframe
            }
        )
        return None
    
    async def _find_similar_cached_response(self, signature: AIPromptSignature) -> Optional[Dict[str, Any]]:
        """Find similar cached response with relaxed matching"""
        # For now, try matching without market conditions hash
        # This allows reuse of responses when market conditions are similar but not identical
        
        return await cache_manager.get(
            CacheType.AI_RESPONSE,
            strategy_type=signature.strategy_type,
            money_management_type=signature.money_management_type,
            symbol=signature.symbol,
            timeframe=signature.timeframe,
            balance_range=signature.account_balance_range,
            risk_settings=signature.risk_settings_hash,
            # Omit market_conditions for similar match
            similar_match=True
        )
    
    async def cache_response(
        self, 
        signature: AIPromptSignature, 
        response: Dict[str, Any],
        confidence_threshold: float = 0.7
    ) -> bool:
        """Cache AI response if it meets quality criteria"""
        
        # Only cache high-confidence responses
        confidence = response.get('confidence', 0)
        if confidence < confidence_threshold:
            logger.log_performance_metric(
                "ai_cache_skip_low_confidence", 
                1, 
                context={'confidence': confidence, 'threshold': confidence_threshold}
            )
            return False
        
        # Cache exact match
        await cache_manager.set(
            CacheType.AI_RESPONSE,
            response,
            ttl_override=1800,  # 30 minutes for AI responses
            strategy_type=signature.strategy_type,
            money_management_type=signature.money_management_type,
            symbol=signature.symbol,
            timeframe=signature.timeframe,
            market_conditions=signature.market_conditions_hash,
            balance_range=signature.account_balance_range,
            risk_settings=signature.risk_settings_hash
        )
        
        # Also cache for similar matching (without market conditions)
        await cache_manager.set(
            CacheType.AI_RESPONSE,
            response,
            ttl_override=900,  # 15 minutes for similar matches
            strategy_type=signature.strategy_type,
            money_management_type=signature.money_management_type,
            symbol=signature.symbol,
            timeframe=signature.timeframe,
            balance_range=signature.account_balance_range,
            risk_settings=signature.risk_settings_hash,
            similar_match=True
        )
        
        logger.log_performance_metric(
            "ai_response_cached", 
            1, 
            context={
                'strategy': signature.strategy_type,
                'symbol': signature.symbol,
                'confidence': confidence
            }
        )
        return True
    
    async def invalidate_symbol_cache(self, symbol: str):
        """Invalidate all cached responses for a symbol (e.g., after major news)"""
        # This would require a more sophisticated cache key structure
        # For now, we'll rely on TTL expiration
        logger.log_performance_metric("ai_cache_symbol_invalidation", 1, context={'symbol': symbol})
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get AI cache statistics"""
        overall_stats = cache_manager.get_stats()
        ai_stats = overall_stats.get('type_breakdown', {}).get('ai_response', {})
        
        return {
            'ai_cache_entries': ai_stats.get('count', 0),
            'ai_cache_size_mb': ai_stats.get('size_bytes', 0) / (1024 * 1024),
            'overall_hit_rate': overall_stats.get('hit_rate', 0),
            'estimated_api_cost_savings': self._estimate_cost_savings(overall_stats.get('hits', 0))
        }
    
    def _estimate_cost_savings(self, cache_hits: int) -> float:
        """Estimate cost savings from cache hits"""
        # Assume average AI API call costs $0.002
        estimated_cost_per_call = 0.002
        return cache_hits * estimated_cost_per_call

# Global AI response cache
ai_response_cache = AIResponseCache()
