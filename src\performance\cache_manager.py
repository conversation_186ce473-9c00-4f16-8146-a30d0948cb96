#!/usr/bin/env python3
"""
Performance Cache Manager
Provides intelligent caching for market data, AI responses, and other expensive operations
"""

import asyncio
import time
import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum

from logging_system.logging_standards import create_logger, LogLevel, PerformanceTimer

logger = create_logger("CacheManager")

class CacheType(Enum):
    """Types of cached data"""
    MARKET_DATA = "market_data"
    AI_RESPONSE = "ai_response"
    ACCOUNT_INFO = "account_info"
    SYMBOL_INFO = "symbol_info"
    TRADE_HISTORY = "trade_history"
    STRATEGY_CALCULATION = "strategy_calculation"

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    data: Any
    cache_type: CacheType
    created_at: datetime
    expires_at: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0

class CacheManager:
    """Intelligent cache manager with TTL and size limits"""
    
    def __init__(self, max_size_mb: int = 100, default_ttl_seconds: int = 300):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.default_ttl = default_ttl_seconds
        self.cache: Dict[str, CacheEntry] = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_size_bytes': 0
        }
        self._lock = asyncio.Lock()
        
        # Cache TTL settings by type
        self.ttl_settings = {
            CacheType.MARKET_DATA: 60,      # 1 minute for market data
            CacheType.AI_RESPONSE: 1800,    # 30 minutes for AI responses
            CacheType.ACCOUNT_INFO: 300,    # 5 minutes for account info
            CacheType.SYMBOL_INFO: 3600,    # 1 hour for symbol info
            CacheType.TRADE_HISTORY: 600,   # 10 minutes for trade history
            CacheType.STRATEGY_CALCULATION: 900  # 15 minutes for strategy calculations
        }
        
        logger.log_system_operation("CACHE_INIT", f"Cache manager initialized with {max_size_mb}MB limit")
    
    def _generate_cache_key(self, cache_type: CacheType, **kwargs) -> str:
        """Generate a unique cache key"""
        # Sort kwargs for consistent key generation
        sorted_kwargs = sorted(kwargs.items())
        key_data = f"{cache_type.value}:{json.dumps(sorted_kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _calculate_size(self, data: Any) -> int:
        """Estimate the size of data in bytes"""
        try:
            if isinstance(data, (str, bytes)):
                return len(data.encode() if isinstance(data, str) else data)
            elif isinstance(data, (dict, list)):
                return len(json.dumps(data, default=str).encode())
            else:
                return len(str(data).encode())
        except:
            return 1024  # Default estimate
    
    async def get(self, cache_type: CacheType, **kwargs) -> Optional[Any]:
        """Get data from cache"""
        async with self._lock:
            key = self._generate_cache_key(cache_type, **kwargs)
            
            if key not in self.cache:
                self.cache_stats['misses'] += 1
                logger.log_performance_metric("cache_miss", 1, context={'cache_type': cache_type.value, 'key': key[:16]})
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if datetime.now() > entry.expires_at:
                del self.cache[key]
                self.cache_stats['total_size_bytes'] -= entry.size_bytes
                self.cache_stats['misses'] += 1
                logger.log_performance_metric("cache_expired", 1, context={'cache_type': cache_type.value})
                return None
            
            # Update access statistics
            entry.access_count += 1
            entry.last_accessed = datetime.now()
            self.cache_stats['hits'] += 1
            
            logger.log_performance_metric("cache_hit", 1, context={'cache_type': cache_type.value, 'access_count': entry.access_count})
            return entry.data
    
    async def set(self, cache_type: CacheType, data: Any, ttl_override: Optional[int] = None, **kwargs) -> bool:
        """Set data in cache"""
        async with self._lock:
            key = self._generate_cache_key(cache_type, **kwargs)
            
            # Calculate TTL
            ttl = ttl_override or self.ttl_settings.get(cache_type, self.default_ttl)
            expires_at = datetime.now() + timedelta(seconds=ttl)
            
            # Calculate size
            size_bytes = self._calculate_size(data)
            
            # Check if we need to evict entries
            await self._ensure_space(size_bytes)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                data=data,
                cache_type=cache_type,
                created_at=datetime.now(),
                expires_at=expires_at,
                size_bytes=size_bytes
            )
            
            # Remove old entry if exists
            if key in self.cache:
                old_entry = self.cache[key]
                self.cache_stats['total_size_bytes'] -= old_entry.size_bytes
            
            # Add new entry
            self.cache[key] = entry
            self.cache_stats['total_size_bytes'] += size_bytes
            
            logger.log_performance_metric(
                "cache_set", 
                1, 
                context={
                    'cache_type': cache_type.value, 
                    'size_bytes': size_bytes,
                    'ttl_seconds': ttl
                }
            )
            return True
    
    async def _ensure_space(self, required_bytes: int):
        """Ensure there's enough space in cache"""
        if self.cache_stats['total_size_bytes'] + required_bytes <= self.max_size_bytes:
            return
        
        # Sort entries by last accessed time (LRU eviction)
        entries_by_access = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_accessed or x[1].created_at
        )
        
        bytes_to_free = (self.cache_stats['total_size_bytes'] + required_bytes) - self.max_size_bytes
        bytes_freed = 0
        
        for key, entry in entries_by_access:
            if bytes_freed >= bytes_to_free:
                break
            
            del self.cache[key]
            bytes_freed += entry.size_bytes
            self.cache_stats['total_size_bytes'] -= entry.size_bytes
            self.cache_stats['evictions'] += 1
            
            logger.log_performance_metric(
                "cache_eviction", 
                1, 
                context={'cache_type': entry.cache_type.value, 'size_bytes': entry.size_bytes}
            )
    
    async def invalidate(self, cache_type: Optional[CacheType] = None, **kwargs):
        """Invalidate cache entries"""
        async with self._lock:
            if cache_type and kwargs:
                # Invalidate specific entry
                key = self._generate_cache_key(cache_type, **kwargs)
                if key in self.cache:
                    entry = self.cache[key]
                    del self.cache[key]
                    self.cache_stats['total_size_bytes'] -= entry.size_bytes
                    logger.log_performance_metric("cache_invalidate", 1, context={'cache_type': cache_type.value})
            elif cache_type:
                # Invalidate all entries of a specific type
                keys_to_remove = [k for k, v in self.cache.items() if v.cache_type == cache_type]
                for key in keys_to_remove:
                    entry = self.cache[key]
                    del self.cache[key]
                    self.cache_stats['total_size_bytes'] -= entry.size_bytes
                logger.log_performance_metric("cache_invalidate_type", len(keys_to_remove), context={'cache_type': cache_type.value})
            else:
                # Clear all cache
                self.cache.clear()
                self.cache_stats['total_size_bytes'] = 0
                logger.log_performance_metric("cache_clear_all", 1)
    
    async def cleanup_expired(self):
        """Remove expired entries"""
        async with self._lock:
            now = datetime.now()
            expired_keys = [k for k, v in self.cache.items() if now > v.expires_at]
            
            for key in expired_keys:
                entry = self.cache[key]
                del self.cache[key]
                self.cache_stats['total_size_bytes'] -= entry.size_bytes
            
            if expired_keys:
                logger.log_performance_metric("cache_cleanup", len(expired_keys))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_entries = len(self.cache)
        hit_rate = self.cache_stats['hits'] / (self.cache_stats['hits'] + self.cache_stats['misses']) if (self.cache_stats['hits'] + self.cache_stats['misses']) > 0 else 0
        
        # Group by cache type
        type_stats = {}
        for entry in self.cache.values():
            cache_type = entry.cache_type.value
            if cache_type not in type_stats:
                type_stats[cache_type] = {'count': 0, 'size_bytes': 0}
            type_stats[cache_type]['count'] += 1
            type_stats[cache_type]['size_bytes'] += entry.size_bytes
        
        return {
            'total_entries': total_entries,
            'total_size_mb': self.cache_stats['total_size_bytes'] / (1024 * 1024),
            'max_size_mb': self.max_size_bytes / (1024 * 1024),
            'hit_rate': hit_rate,
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'evictions': self.cache_stats['evictions'],
            'type_breakdown': type_stats
        }

# Decorator for automatic caching
def cached(cache_type: CacheType, ttl_override: Optional[int] = None):
    """Decorator to automatically cache function results"""
    def decorator(func: Callable):
        async def async_wrapper(*args, **kwargs):
            # Generate cache key from function arguments
            cache_key_data = {
                'function': func.__name__,
                'args': str(args),
                'kwargs': kwargs
            }
            
            # Try to get from cache
            cached_result = await cache_manager.get(cache_type, **cache_key_data)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            with PerformanceTimer(logger, f"function_{func.__name__}"):
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
            
            # Cache the result
            await cache_manager.set(cache_type, result, ttl_override, **cache_key_data)
            return result
        
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(async_wrapper(*args, **kwargs))
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# Global cache manager instance
cache_manager = CacheManager()

# Market Data Optimization
class MarketDataBatcher:
    """Batches market data requests to reduce redundant API calls"""

    def __init__(self):
        self.pending_requests: Dict[str, List[Callable]] = {}
        self.request_lock = asyncio.Lock()

    async def get_market_data_batch(self, symbol: str, timeframe: str, mt5_client, accounts: List[Dict]) -> Dict[str, Any]:
        """Get market data with batching to avoid duplicate requests"""
        request_key = f"{symbol}_{timeframe}"

        # Check cache first
        cached_data = await cache_manager.get(
            CacheType.MARKET_DATA,
            symbol=symbol,
            timeframe=timeframe
        )

        if cached_data:
            logger.log_performance_metric("market_data_cache_hit", 1, context={'symbol': symbol, 'timeframe': timeframe})
            return cached_data

        async with self.request_lock:
            # Check if request is already pending
            if request_key in self.pending_requests:
                logger.log_performance_metric("market_data_batch_wait", 1, context={'symbol': symbol, 'timeframe': timeframe})
                # Wait for the pending request to complete
                while request_key in self.pending_requests:
                    await asyncio.sleep(0.1)

                # Try cache again after waiting
                cached_data = await cache_manager.get(
                    CacheType.MARKET_DATA,
                    symbol=symbol,
                    timeframe=timeframe
                )
                if cached_data:
                    return cached_data

            # Mark request as pending
            self.pending_requests[request_key] = []

        try:
            # Make the actual request
            with PerformanceTimer(logger, f"market_data_request_{symbol}_{timeframe}"):
                market_data = mt5_client.get_market_data(symbol, timeframe, 200)

            if market_data:
                # Cache the result
                await cache_manager.set(
                    CacheType.MARKET_DATA,
                    market_data,
                    ttl_override=60,  # 1 minute TTL for market data
                    symbol=symbol,
                    timeframe=timeframe
                )
                logger.log_performance_metric("market_data_fetched", 1, context={'symbol': symbol, 'timeframe': timeframe})

            return market_data

        finally:
            # Remove from pending requests
            async with self.request_lock:
                if request_key in self.pending_requests:
                    del self.pending_requests[request_key]

# Global market data batcher
market_data_batcher = MarketDataBatcher()

# Cleanup task
async def start_cache_cleanup_task():
    """Start background task to clean up expired cache entries"""
    while True:
        try:
            await cache_manager.cleanup_expired()
            await asyncio.sleep(300)  # Clean up every 5 minutes
        except Exception as e:
            logger.log_system_operation("CACHE_CLEANUP_ERROR", f"Error during cache cleanup: {e}", LogLevel.ERROR)
