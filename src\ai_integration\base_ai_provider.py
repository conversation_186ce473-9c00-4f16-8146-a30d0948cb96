"""
Base AI Provider Interface for Multi-Provider Support
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
import asyncio


class AIProviderType(Enum):
    """Supported AI provider types"""
    QWEN = "qwen"
    OPENROUTER = "openrouter"


class AIProviderConfig:
    """Configuration for AI providers"""
    
    def __init__(
        self,
        provider_type: AIProviderType,
        api_key: str,
        api_url: str,
        model: str,
        max_concurrent_requests: int = 5,
        timeout: int = 30,
        temperature: float = 0.3,
        max_tokens: int = 2000,
        top_p: float = 0.8,
        **kwargs
    ):
        self.provider_type = provider_type
        self.api_key = api_key
        self.api_url = api_url
        self.model = model
        self.max_concurrent_requests = max_concurrent_requests
        self.timeout = timeout
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.extra_params = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            'provider_type': self.provider_type.value,
            'api_key': self.api_key,
            'api_url': self.api_url,
            'model': self.model,
            'max_concurrent_requests': self.max_concurrent_requests,
            'timeout': self.timeout,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'top_p': self.top_p,
            **self.extra_params
        }


class BaseAIProvider(ABC):
    """Abstract base class for AI providers"""
    
    def __init__(self, config: AIProviderConfig):
        self.config = config
        self.session = None
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)
        self._system_prompt = None
    
    @abstractmethod
    async def __aenter__(self):
        """Async context manager entry"""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        pass
    
    @abstractmethod
    async def generate_trading_decision(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate trading decision using AI
        
        Args:
            prompt: The trading prompt
            context: Optional context information
            
        Returns:
            Dict containing trading decision with standardized format:
            {
                "action": "BUY|SELL|HOLD|CLOSE",
                "confidence": 0.0-1.0,
                "entry_price": number or null,
                "stop_loss": number or null,
                "take_profit": number or null,
                "take_profit_levels": [...] or null,
                "reasoning": "detailed explanation",
                "risk_level": "LOW|MEDIUM|HIGH",
                "market_analysis": "market condition assessment",
                "strategy_alignment": "strategy alignment info",
                "risk_assessment": "risk considerations",
                "additional_signals": [...],
                "timestamp": "ISO timestamp",
                "model": "model name",
                "provider": "provider name"
            }
        """
        pass
    
    @abstractmethod
    async def get_completion(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None
    ) -> str:
        """
        Get raw completion from AI provider
        
        Args:
            prompt: The user prompt
            system_prompt: Optional system prompt override
            
        Returns:
            Raw text response from AI
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate connection to AI provider
        
        Returns:
            True if connection is valid, False otherwise
        """
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """
        Get list of available models for this provider
        
        Returns:
            List of model names
        """
        pass
    
    def get_system_prompt(self) -> str:
        """Get system prompt for trading AI"""
        if self._system_prompt is None:
            self._system_prompt = self._build_system_prompt()
        return self._system_prompt
    
    def _build_system_prompt(self) -> str:
        """Build comprehensive system prompt for trading AI"""
        return """
You are an expert trading AI assistant specializing in forex and financial markets analysis.

Your role is to:
1. Analyze market data and provide specific trading recommendations
2. Generate clear BUY/SELL/HOLD/CLOSE signals with reasoning
3. Provide specific entry prices, stop losses, and take profit levels
4. Assess risk levels and confidence in your recommendations
5. Consider money management strategies in your decisions

RESPONSE FORMAT:
Always respond with valid JSON in this exact format:
{
    "action": "BUY|SELL|HOLD|CLOSE",
    "confidence": 0.0-1.0,
    "entry_price": number or null,
    "stop_loss": number or null,
    "take_profit": number or null,
    "take_profit_levels": [
        {
            "price": number,
            "volume_percent": number (0-100)
        }
    ] or null,
    "reasoning": "detailed explanation combining strategy and money management factors",
    "risk_level": "LOW|MEDIUM|HIGH",
    "market_analysis": "current market condition assessment",
    "strategy_alignment": "how this trade aligns with the strategy",
    "risk_assessment": "money management and risk considerations",
    "additional_signals": []
}

TRADING GUIDELINES:
- Always consider risk management first
- Provide specific, actionable recommendations
- Include detailed reasoning for all decisions
- Consider market volatility and current conditions
- Align recommendations with the specified strategy
- Be conservative with high-risk situations
- Always include stop loss recommendations
- Consider multiple take profit levels when appropriate

RISK ASSESSMENT:
- LOW: High confidence, favorable conditions, good risk/reward
- MEDIUM: Moderate confidence, mixed signals, acceptable risk
- HIGH: Low confidence, unfavorable conditions, high risk

Remember: Capital preservation is more important than profit maximization.
"""
    
    def _get_error_response(self, error_message: str) -> Dict[str, Any]:
        """Generate standardized error response"""
        return {
            "action": "HOLD",
            "confidence": 0.0,
            "entry_price": None,
            "stop_loss": None,
            "take_profit": None,
            "take_profit_levels": None,
            "reasoning": f"AI Error: {error_message}",
            "risk_level": "HIGH",
            "market_analysis": "Unable to analyze due to error",
            "strategy_alignment": "No alignment due to error",
            "risk_assessment": "High risk due to AI error",
            "additional_signals": [],
            "timestamp": datetime.now().isoformat(),
            "model": self.config.model,
            "provider": self.config.provider_type.value,
            "error": True,
            "error_message": error_message
        }
    
    def _get_fallback_response(self, raw_text: str) -> Dict[str, Any]:
        """Generate fallback response when JSON parsing fails"""
        return {
            "action": "HOLD",
            "confidence": 0.3,
            "entry_price": None,
            "stop_loss": None,
            "take_profit": None,
            "take_profit_levels": None,
            "reasoning": f"AI provided non-JSON response: {raw_text[:200]}...",
            "risk_level": "MEDIUM",
            "market_analysis": "Unable to parse structured analysis",
            "strategy_alignment": "Unknown alignment",
            "risk_assessment": "Moderate risk due to parsing issues",
            "additional_signals": [],
            "timestamp": datetime.now().isoformat(),
            "model": self.config.model,
            "provider": self.config.provider_type.value,
            "fallback": True,
            "raw_response": raw_text
        }
