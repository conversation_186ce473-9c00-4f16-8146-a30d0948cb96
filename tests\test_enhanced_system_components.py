#!/usr/bin/env python3
"""
Enhanced System Components Test Suite
Tests for new components: error recovery, performance optimization, standardized logging, and configuration validation
"""

import unittest
import asyncio
import sys
import os
import time
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Import components to test
from error_recovery.recovery_manager import ErrorRecoveryManager, FailureType, RecoveryStrategy, CircuitBreaker
from performance.cache_manager import CacheManager, CacheType, market_data_batcher
from performance.ai_cache import AIResponseCache, AIPromptSignature
from logging_system.logging_standards import StandardizedLogger, LogLevel, LogCategory
from account_management.config_validator import ConfigValidator

class TestErrorRecoveryManager(unittest.TestCase):
    """Test error recovery manager functionality"""
    
    def setUp(self):
        self.recovery_manager = ErrorRecoveryManager()
    
    def test_circuit_breaker_functionality(self):
        """Test circuit breaker open/close behavior"""
        cb = CircuitBreaker(threshold=3, timeout=1)
        
        # Initially closed
        self.assertTrue(cb.can_execute())
        self.assertEqual(cb.state, "CLOSED")
        
        # Record failures
        for _ in range(3):
            cb.record_failure()
        
        # Should be open now
        self.assertFalse(cb.can_execute())
        self.assertEqual(cb.state, "OPEN")
        
        # Wait for timeout
        time.sleep(1.1)
        
        # Should allow test execution (HALF_OPEN)
        self.assertTrue(cb.can_execute())
        
        # Record success to close
        cb.record_success()
        self.assertEqual(cb.state, "CLOSED")
    
    async def test_execute_with_recovery_success(self):
        """Test successful operation execution"""
        async def mock_operation():
            return "success"
        
        result = await self.recovery_manager.execute_with_recovery(
            mock_operation,
            "test_operation",
            FailureType.NETWORK
        )
        
        self.assertEqual(result, "success")
    
    async def test_execute_with_recovery_retry(self):
        """Test retry mechanism on failures"""
        call_count = 0
        
        async def mock_failing_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success_after_retries"
        
        result = await self.recovery_manager.execute_with_recovery(
            mock_failing_operation,
            "test_retry_operation",
            FailureType.NETWORK
        )
        
        self.assertEqual(result, "success_after_retries")
        self.assertEqual(call_count, 3)
    
    def test_failure_statistics(self):
        """Test failure statistics collection"""
        # Add some test failures
        for i in range(5):
            self.recovery_manager.failure_history.append(
                self.recovery_manager.FailureRecord(
                    timestamp=datetime.now(),
                    failure_type=FailureType.NETWORK,
                    operation=f"test_op_{i}",
                    error_message="Test error",
                    context={}
                )
            )
        
        stats = self.recovery_manager.get_failure_statistics(24)
        self.assertEqual(stats['total_failures'], 5)
        self.assertIn('network', stats['failures_by_type'])

class TestCacheManager(unittest.TestCase):
    """Test cache manager functionality"""
    
    def setUp(self):
        self.cache_manager = CacheManager(max_size_mb=1, default_ttl_seconds=60)
    
    async def test_cache_set_and_get(self):
        """Test basic cache set and get operations"""
        test_data = {"test": "data", "value": 123}
        
        # Set data in cache
        success = await self.cache_manager.set(
            CacheType.MARKET_DATA,
            test_data,
            symbol="EURUSD",
            timeframe="M15"
        )
        self.assertTrue(success)
        
        # Get data from cache
        cached_data = await self.cache_manager.get(
            CacheType.MARKET_DATA,
            symbol="EURUSD",
            timeframe="M15"
        )
        self.assertEqual(cached_data, test_data)
    
    async def test_cache_expiration(self):
        """Test cache TTL expiration"""
        test_data = {"test": "expiring_data"}
        
        # Set data with short TTL
        await self.cache_manager.set(
            CacheType.MARKET_DATA,
            test_data,
            ttl_override=1,  # 1 second
            symbol="EURUSD",
            timeframe="M15"
        )
        
        # Should be available immediately
        cached_data = await self.cache_manager.get(
            CacheType.MARKET_DATA,
            symbol="EURUSD",
            timeframe="M15"
        )
        self.assertEqual(cached_data, test_data)
        
        # Wait for expiration
        await asyncio.sleep(1.1)
        
        # Should be None after expiration
        expired_data = await self.cache_manager.get(
            CacheType.MARKET_DATA,
            symbol="EURUSD",
            timeframe="M15"
        )
        self.assertIsNone(expired_data)
    
    async def test_cache_eviction(self):
        """Test cache size-based eviction"""
        # Fill cache with data
        for i in range(10):
            large_data = {"data": "x" * 10000, "id": i}  # ~10KB each
            await self.cache_manager.set(
                CacheType.MARKET_DATA,
                large_data,
                symbol=f"TEST{i}",
                timeframe="M15"
            )
        
        # Check that eviction occurred
        stats = self.cache_manager.get_stats()
        self.assertGreater(stats['evictions'], 0)
    
    def test_cache_statistics(self):
        """Test cache statistics reporting"""
        stats = self.cache_manager.get_stats()
        
        required_keys = ['total_entries', 'total_size_mb', 'max_size_mb', 'hit_rate', 'hits', 'misses', 'evictions']
        for key in required_keys:
            self.assertIn(key, stats)

class TestAIResponseCache(unittest.TestCase):
    """Test AI response cache functionality"""
    
    def setUp(self):
        self.ai_cache = AIResponseCache()
    
    def test_prompt_signature_creation(self):
        """Test AI prompt signature creation"""
        market_data = {
            'volatility': 0.0005,
            'spread': 2.0,
            'volume': 500,
            'candles': [
                {'close': 1.1000}, {'close': 1.1010}, {'close': 1.1020},
                {'close': 1.1030}, {'close': 1.1040}, {'close': 1.1050},
                {'close': 1.1060}, {'close': 1.1070}, {'close': 1.1080},
                {'close': 1.1090}
            ]
        }
        
        risk_settings = {
            'risk_percent': 2.0,
            'max_daily_trades': 10,
            'max_open_positions': 5,
            'max_daily_loss': 5.0
        }
        
        signature = self.ai_cache.create_prompt_signature(
            strategy_type="trend_following",
            money_management_type="percent_risk",
            symbol="EURUSD",
            timeframe="M15",
            market_data=market_data,
            account_balance=1000.0,
            risk_settings=risk_settings
        )
        
        self.assertIsInstance(signature, AIPromptSignature)
        self.assertEqual(signature.strategy_type, "trend_following")
        self.assertEqual(signature.symbol, "EURUSD")
        self.assertEqual(signature.account_balance_range, "500-1000")
    
    def test_market_condition_categorization(self):
        """Test market condition categorization"""
        # Test volatility categorization
        self.assertEqual(self.ai_cache._categorize_volatility(0.00005), "very_low")
        self.assertEqual(self.ai_cache._categorize_volatility(0.0003), "low")
        self.assertEqual(self.ai_cache._categorize_volatility(0.0008), "medium")
        self.assertEqual(self.ai_cache._categorize_volatility(0.0015), "high")
        self.assertEqual(self.ai_cache._categorize_volatility(0.003), "very_high")
        
        # Test spread categorization
        self.assertEqual(self.ai_cache._categorize_spread(0.5), "tight")
        self.assertEqual(self.ai_cache._categorize_spread(2.0), "normal")
        self.assertEqual(self.ai_cache._categorize_spread(4.0), "wide")
        self.assertEqual(self.ai_cache._categorize_spread(6.0), "very_wide")
    
    def test_trend_detection(self):
        """Test trend direction detection"""
        # Uptrend candles
        uptrend_candles = [{'close': 1.1000 + i * 0.0010} for i in range(10)]
        trend = self.ai_cache._detect_trend_direction(uptrend_candles)
        self.assertEqual(trend, "uptrend")
        
        # Downtrend candles
        downtrend_candles = [{'close': 1.1100 - i * 0.0010} for i in range(10)]
        trend = self.ai_cache._detect_trend_direction(downtrend_candles)
        self.assertEqual(trend, "downtrend")
        
        # Sideways candles
        sideways_candles = [{'close': 1.1000 + (i % 2) * 0.0001} for i in range(10)]
        trend = self.ai_cache._detect_trend_direction(sideways_candles)
        self.assertEqual(trend, "sideways")

class TestStandardizedLogging(unittest.TestCase):
    """Test standardized logging functionality"""
    
    def setUp(self):
        self.logger = StandardizedLogger("TestModule")
    
    def test_log_message_formatting(self):
        """Test standardized log message formatting"""
        # This test would require capturing log output
        # For now, just test that methods don't raise exceptions
        
        self.logger.log_system_operation("TEST_OPERATION", "Test message", LogLevel.INFO)
        self.logger.log_trade_operation("TRADE_TEST", "Trade message", "test_account", "EURUSD")
        self.logger.log_ai_operation("AI_TEST", "AI message", "test_account", "EURUSD")
        self.logger.log_performance_metric("test_metric", 123.45, "ms")
    
    def test_performance_timer(self):
        """Test performance timer context manager"""
        from logging_system.logging_standards import PerformanceTimer
        
        with PerformanceTimer(self.logger, "test_operation") as timer:
            time.sleep(0.1)  # Simulate work
        
        # Timer should complete without errors

class TestConfigValidator(unittest.TestCase):
    """Test configuration validator functionality"""
    
    def setUp(self):
        self.validator = ConfigValidator()
    
    def test_valid_account_config(self):
        """Test validation of valid account configuration"""
        valid_config = {
            'account_id': 'test_account',
            'account_number': 12345,
            'password': 'test_password',
            'server': 'test_server',
            'strategy': 'trend_following',
            'money_management': 'percent_risk',
            'symbols': [
                {'symbol': 'EURUSD', 'timeframe': 'M15'}
            ],
            'money_management_settings': {
                'max_daily_trades': 10,
                'max_open_positions': 5,
                'max_pending_orders': 10,
                'max_daily_loss': 5.0
            }
        }
        
        issues = self.validator.validate_account_config(valid_config)
        self.assertEqual(len(issues), 0)
    
    def test_invalid_account_config(self):
        """Test validation of invalid account configuration"""
        invalid_config = {
            'account_id': 'test_account',
            # Missing required fields
            'symbols': "invalid_symbols_format",  # Should be list
            'money_management_settings': {
                'max_daily_trades': -5,  # Invalid value
                # Missing required fields
            }
        }
        
        issues = self.validator.validate_account_config(invalid_config)
        self.assertGreater(len(issues), 0)
    
    def test_config_standardization(self):
        """Test configuration standardization"""
        config_with_old_fields = {
            'account_id': 'test_account',
            'account_number': 12345,
            'password': 'test_password',
            'server': 'test_server',
            'strategy': 'trend_following',
            'money_management': 'percent_risk',
            'symbols': [{'symbol': 'EURUSD', 'timeframe': 'M15'}],
            'money_management_config': {  # Old field name
                'max_concurrent_positions': 5,  # Old field name
                'daily_trade_limit': 10  # Old field name
            }
        }
        
        changes = self.validator.standardize_account_config(config_with_old_fields)
        self.assertGreater(len(changes), 0)
        
        # Check that old fields were renamed
        self.assertIn('money_management_settings', config_with_old_fields)
        self.assertNotIn('money_management_config', config_with_old_fields)

class TestAsyncIntegration(unittest.TestCase):
    """Test async integration of enhanced components"""
    
    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        self.loop.close()
    
    def test_async_error_recovery_integration(self):
        """Test async error recovery with cache integration"""
        async def test_integration():
            recovery_manager = ErrorRecoveryManager()
            cache_manager = CacheManager()
            
            # Test operation that uses both error recovery and caching
            async def cached_operation():
                # Simulate getting data from cache
                cached_result = await cache_manager.get(CacheType.MARKET_DATA, symbol="EURUSD")
                if cached_result:
                    return cached_result
                
                # Simulate fetching new data
                new_data = {"price": 1.1000, "timestamp": datetime.now().isoformat()}
                await cache_manager.set(CacheType.MARKET_DATA, new_data, symbol="EURUSD")
                return new_data
            
            # Execute with error recovery
            result = await recovery_manager.execute_with_recovery(
                cached_operation,
                "test_cached_operation",
                FailureType.NETWORK
            )
            
            self.assertIsNotNone(result)
            self.assertIn("price", result)
        
        self.loop.run_until_complete(test_integration())

def run_enhanced_tests():
    """Run all enhanced system component tests"""
    print("🧪 Running Enhanced System Components Tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestErrorRecoveryManager,
        TestCacheManager,
        TestAIResponseCache,
        TestStandardizedLogging,
        TestConfigValidator,
        TestAsyncIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Return results
    return {
        'tests_run': result.testsRun,
        'failures': len(result.failures),
        'errors': len(result.errors),
        'success': result.wasSuccessful()
    }

if __name__ == "__main__":
    results = run_enhanced_tests()
    print(f"\n📊 Enhanced Tests Results:")
    print(f"   Tests Run: {results['tests_run']}")
    print(f"   Failures: {results['failures']}")
    print(f"   Errors: {results['errors']}")
    print(f"   Success: {results['success']}")
    
    if not results['success']:
        sys.exit(1)
