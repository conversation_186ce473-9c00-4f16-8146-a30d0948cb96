"""
AI-Driven <PERSON><PERSON><PERSON> for MT5 Trading Errors
Handles MT5 errors like 10016 (Invalid stops) by asking AI to decide what to do
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ErrorContext:
    """Context information for an MT5 error"""
    error_code: int
    error_message: str
    operation_type: str  # "place_order", "modify_order", "modify_position"
    symbol: str
    original_params: Dict[str, Any]
    market_data: Dict[str, Any]
    account_info: Dict[str, Any]

@dataclass
class ErrorResolution:
    """AI's decision on how to handle the error"""
    action: str  # "RETRY_WITH_ADJUSTED", "CANCEL_OPERATION", "RETRY_WITHOUT_STOPS", "WAIT_AND_RETRY"
    adjusted_params: Optional[Dict[str, Any]] = None
    reasoning: str = ""
    confidence: float = 0.0

class AIErrorHandler:
    """AI-driven error handler for MT5 trading errors"""
    
    def __init__(self, ai_client):
        self.ai_client = ai_client
        
    async def handle_mt5_error(self, error_context: ErrorContext) -> ErrorResolution:
        """Handle MT5 error using AI decision making"""
        
        if error_context.error_code == 10016:
            return await self._handle_invalid_stops_error(error_context)
        elif error_context.error_code == 10015:
            return await self._handle_invalid_price_error(error_context)
        elif error_context.error_code == 10025:
            return await self._handle_no_changes_error(error_context)
        else:
            return await self._handle_generic_error(error_context)
    
    async def _handle_invalid_stops_error(self, context: ErrorContext) -> ErrorResolution:
        """Handle MT5 error 10016 - Invalid stops"""
        
        prompt = f"""
You are an expert MT5 trading error handler. A trade operation failed with ERROR 10016 - INVALID STOPS.

OPERATION DETAILS:
- Operation: {context.operation_type}
- Symbol: {context.symbol}
- Error: {context.error_code} - {context.error_message}

ORIGINAL PARAMETERS:
{self._format_params(context.original_params)}

MARKET DATA:
- Current Bid: {context.market_data.get('bid', 'N/A')}
- Current Ask: {context.market_data.get('ask', 'N/A')}
- Spread: {context.market_data.get('spread', 'N/A')} pips
- Min Stop Level: {context.market_data.get('stops_level', 'N/A')} points
- Point Size: {context.market_data.get('point', 'N/A')}

ACCOUNT INFO:
- Balance: ${context.account_info.get('balance', 'N/A')}
- Free Margin: ${context.account_info.get('free_margin', 'N/A')}

ERROR 10016 ANALYSIS:
This error occurs when stop loss or take profit levels are too close to the current price or entry price.
The broker requires a minimum distance between price and stops.

YOUR TASK:
Analyze the situation and decide how to handle this error. Consider:
1. Are the stops too close to current market price?
2. Should we adjust the stop loss and/or take profit?
3. Should we proceed without stops (risky)?
4. Should we cancel the operation entirely?

AVAILABLE ACTIONS:
- RETRY_WITH_ADJUSTED: Adjust SL/TP to meet broker requirements
- CANCEL_OPERATION: Cancel the trade/modification entirely
- RETRY_WITHOUT_STOPS: Remove stops and proceed (high risk)
- WAIT_AND_RETRY: Wait for better market conditions

Provide your decision in JSON format:
{{
    "action": "RETRY_WITH_ADJUSTED|CANCEL_OPERATION|RETRY_WITHOUT_STOPS|WAIT_AND_RETRY",
    "adjusted_params": {{
        "stop_loss": new_sl_value_or_null,
        "take_profit": new_tp_value_or_null,
        "entry_price": new_entry_price_if_needed
    }},
    "reasoning": "Detailed explanation of your decision",
    "confidence": 0.85
}}

IMPORTANT: Be conservative with risk. If unsure, prefer CANCEL_OPERATION over risky adjustments.
"""
        
        try:
            response = await self.ai_client.get_completion(prompt)
            return self._parse_error_resolution(response)
        except Exception as e:
            logger.error(f"AI error handling failed: {e}")
            # Fallback to conservative approach
            return ErrorResolution(
                action="CANCEL_OPERATION",
                reasoning=f"AI error handling failed: {e}. Using conservative fallback.",
                confidence=0.5
            )
    
    async def _handle_invalid_price_error(self, context: ErrorContext) -> ErrorResolution:
        """Handle MT5 error 10015 - Invalid price"""
        
        prompt = f"""
You are an expert MT5 trading error handler. A trade operation failed with ERROR 10015 - INVALID PRICE.

OPERATION DETAILS:
- Operation: {context.operation_type}
- Symbol: {context.symbol}
- Error: {context.error_code} - {context.error_message}

ORIGINAL PARAMETERS:
{self._format_params(context.original_params)}

MARKET DATA:
- Current Bid: {context.market_data.get('bid', 'N/A')}
- Current Ask: {context.market_data.get('ask', 'N/A')}
- Spread: {context.market_data.get('spread', 'N/A')} pips

ERROR 10015 ANALYSIS:
This error occurs when the price is not properly formatted or is outside acceptable range.
Common causes: wrong decimal places, stale price, or price too far from market.

YOUR TASK:
Decide how to handle this price error:

AVAILABLE ACTIONS:
- RETRY_WITH_ADJUSTED: Use current market price
- CANCEL_OPERATION: Cancel the operation
- WAIT_AND_RETRY: Wait for better market conditions

Provide your decision in JSON format with adjusted_params if needed.
"""
        
        try:
            response = await self.ai_client.get_completion(prompt)
            return self._parse_error_resolution(response)
        except Exception as e:
            logger.error(f"AI error handling failed: {e}")
            return ErrorResolution(
                action="CANCEL_OPERATION",
                reasoning=f"AI error handling failed: {e}",
                confidence=0.5
            )
    
    async def _handle_no_changes_error(self, context: ErrorContext) -> ErrorResolution:
        """Handle MT5 error 10025 - No changes"""
        
        # This is not really an error - the modification was identical to current values
        return ErrorResolution(
            action="CANCEL_OPERATION",
            reasoning="No changes needed - modification values identical to current values",
            confidence=1.0
        )
    
    async def _handle_generic_error(self, context: ErrorContext) -> ErrorResolution:
        """Handle other MT5 errors"""
        
        return ErrorResolution(
            action="CANCEL_OPERATION",
            reasoning=f"Unknown error {context.error_code}: {context.error_message}",
            confidence=0.5
        )
    
    def _format_params(self, params: Dict[str, Any]) -> str:
        """Format parameters for display in prompt"""
        formatted = []
        for key, value in params.items():
            if value is not None:
                formatted.append(f"- {key}: {value}")
        return "\n".join(formatted)
    
    def _parse_error_resolution(self, response: str) -> ErrorResolution:
        """Parse AI response into ErrorResolution"""
        try:
            import json
            data = json.loads(response)
            
            return ErrorResolution(
                action=data.get("action", "CANCEL_OPERATION"),
                adjusted_params=data.get("adjusted_params"),
                reasoning=data.get("reasoning", "No reasoning provided"),
                confidence=float(data.get("confidence", 0.5))
            )
        except Exception as e:
            logger.error(f"Failed to parse AI response: {e}")
            return ErrorResolution(
                action="CANCEL_OPERATION",
                reasoning=f"Failed to parse AI response: {e}",
                confidence=0.5
            )
