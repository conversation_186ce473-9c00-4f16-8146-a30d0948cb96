#!/usr/bin/env python3
"""
Verify that AI strategy selection is now working correctly in the trading system
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import AccountManager
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

async def test_ai_strategy_selection_integration():
    """Test that AI strategy selection is properly integrated into the signal generation workflow"""
    print("🔧 TESTING AI STRATEGY SELECTION INTEGRATION FIX")
    print("=" * 70)
    
    try:
        # Load test accounts configuration
        config_path = "config/test_accounts.json"
        if not os.path.exists(config_path):
            config_path = "config/accounts.json"
        
        if not os.path.exists(config_path):
            print("❌ No accounts configuration found")
            return False
        
        print(f"📋 Loading accounts from: {config_path}")
        
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.load_accounts(config_path)
        
        if not account_manager.accounts:
            print("❌ No accounts loaded")
            return False
        
        print(f"✅ Loaded {len(account_manager.accounts)} accounts")
        
        # Initialize signal generator
        signal_generator = SignalGenerator(account_manager)
        
        # Test the signal generation process to see if AI strategy selection is called
        print("\n🎯 TESTING SIGNAL GENERATION WITH AI STRATEGY SELECTION")
        print("-" * 60)
        
        # This should trigger the AI strategy selection logic we just added
        print("🚀 Starting signal generation (this should show AI strategy selection logs)...")
        
        # Run a single signal generation cycle
        await signal_generator.generate_signals()
        
        print("\n" + "=" * 70)
        print("✅ SIGNAL GENERATION TEST COMPLETED")
        print("📊 Check the logs above for:")
        print("   - '🎯 AI STRATEGY SELECTION: Starting dynamic strategy selection' messages")
        print("   - '✅ AI SELECTED STRATEGY' confirmations")
        print("   - '🤖 STRATEGY SOURCE: AI-driven dynamic selection' logs")
        print("   - Strategy override or confirmation messages")
        print("\n💡 If you see these messages, the AI strategy selection is working!")
        print("💡 If you don't see them, there may be an issue with the integration.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        logger.error(f"Test error: {e}", exc_info=True)
        return False

async def test_strategy_selection_config():
    """Test different strategy selection configurations"""
    print("\n🔧 TESTING STRATEGY SELECTION CONFIGURATIONS")
    print("-" * 50)
    
    # Test configurations
    test_configs = [
        {
            "name": "Dynamic Mode Enabled",
            "config": {
                "strategy_selection": {
                    "mode": "dynamic",
                    "available_strategies": ["trend_following", "mean_reversion", "breakout"],
                    "selection_criteria": {
                        "volatility_threshold": 0.01,
                        "volume_threshold": 1000
                    }
                }
            }
        },
        {
            "name": "Static Mode (Default)",
            "config": {
                "strategy": "trend_following"
                # No strategy_selection config = static mode
            }
        }
    ]
    
    for test_config in test_configs:
        print(f"\n📋 Testing: {test_config['name']}")
        config = test_config['config']
        
        if 'strategy_selection' in config and config['strategy_selection'].get('mode') == 'dynamic':
            print("   ✅ Dynamic AI strategy selection should be active")
            print("   📊 Expected logs: AI strategy selection messages")
        else:
            print("   📋 Static strategy mode - using hardcoded strategy")
            print("   📊 Expected logs: 'STATIC STRATEGY MODE' messages")

if __name__ == "__main__":
    async def main():
        print("🤖 AI STRATEGY SELECTION INTEGRATION VERIFICATION")
        print("=" * 70)
        print("This test verifies that the AI strategy selection fix is working correctly.")
        print("The system should now show detailed AI strategy selection logs during signal generation.")
        print()
        
        # Test the integration
        success = await test_ai_strategy_selection_integration()
        
        # Test configuration scenarios
        await test_strategy_selection_config()
        
        print("\n" + "=" * 70)
        if success:
            print("✅ INTEGRATION TEST COMPLETED")
            print("📊 Review the logs above to confirm AI strategy selection is working")
        else:
            print("❌ INTEGRATION TEST FAILED")
            print("🔧 Check the error messages above for troubleshooting")
    
    asyncio.run(main())
