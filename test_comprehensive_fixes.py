#!/usr/bin/env python3
"""
Comprehensive test script to validate all the fixes made to the trading system:
1. Strategy selection logic flow
2. Market hours validation
3. Account limits enforcement
4. Enhanced logging
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import AccountManager
from risk_management.unified_risk_manager import UnifiedRiskManager
from strategy_selection.dynamic_strategy_selector import DynamicStrategySelector
from logging_system.logger import get_logger

logger = get_logger(__name__)

class TestComprehensiveFixes:
    """Test all the fixes made to the trading system"""
    
    def __init__(self):
        self.test_results = []
        
    async def run_all_tests(self):
        """Run all comprehensive tests"""
        print("🧪 COMPREHENSIVE FIXES VALIDATION")
        print("=" * 50)
        
        # Test 1: Strategy Selection Logic
        await self.test_strategy_selection_logic()
        
        # Test 2: Market Hours Validation
        await self.test_market_hours_validation()
        
        # Test 3: Account Limits Enforcement
        await self.test_account_limits_enforcement()
        
        # Test 4: Enhanced Logging
        await self.test_enhanced_logging()
        
        # Summary
        self.print_test_summary()
        
    async def test_strategy_selection_logic(self):
        """Test that strategy selection no longer produces conflicting logs"""
        print("\n📋 TEST 1: Strategy Selection Logic Flow")
        print("-" * 40)
        
        try:
            # Create test account with dynamic strategy selection
            test_account = {
                'account_id': 'test_dynamic',
                'strategy': 'trend_following',
                'strategy_selection': {
                    'mode': 'dynamic',
                    'available_strategies': ['trend_following', 'mean_reversion'],
                    'selection_criteria': {}
                }
            }
            
            # Test dynamic strategy selector directly
            selector = DynamicStrategySelector()
            
            # Mock market data
            from data_management.market_data import MarketData
            mock_market_data = MarketData(
                symbol='EURUSD',
                timeframe='M15',
                current_price=1.1000,
                spread=0.0001,
                volatility=0.001,
                volume=1000,
                candles=[]
            )
            
            # Mock account info
            from money_management.base_strategy import AccountInfo
            mock_account_info = AccountInfo(
                balance=100.0,
                equity=100.0,
                margin_level=0.0,
                free_margin=100.0,
                leverage=100
            )
            
            # Test strategy selection
            selected_strategy = selector.select_strategy(
                test_account, mock_market_data, mock_account_info
            )
            
            print(f"✅ Dynamic strategy selection works: {selected_strategy}")
            
            # Test static mode
            test_account['strategy_selection']['mode'] = 'static'
            selected_strategy_static = selector.select_strategy(
                test_account, mock_market_data, mock_account_info
            )
            
            print(f"✅ Static strategy selection works: {selected_strategy_static}")
            
            self.test_results.append(("Strategy Selection Logic", True, "No conflicting logs"))
            
        except Exception as e:
            print(f"❌ Strategy selection test failed: {e}")
            self.test_results.append(("Strategy Selection Logic", False, str(e)))
    
    async def test_market_hours_validation(self):
        """Test market hours validation with proper timezone handling"""
        print("\n🕐 TEST 2: Market Hours Validation")
        print("-" * 40)
        
        try:
            account_manager = AccountManager()
            signal_gen = SignalGenerator(account_manager)
            
            # Test current market hours
            is_open = signal_gen._is_market_open()
            current_time = datetime.now(timezone.utc)
            weekday = current_time.weekday()
            hour = current_time.hour
            
            print(f"Current UTC time: {current_time.strftime('%A, %Y-%m-%d %H:%M:%S %Z')}")
            print(f"Weekday: {weekday} (0=Monday, 6=Sunday), Hour: {hour}")
            print(f"Market open: {is_open}")
            
            # Test weekend trading override
            with patch.dict(os.environ, {'ENABLE_WEEKEND_TRADING': 'true'}):
                is_open_override = signal_gen._is_market_open()
                print(f"With weekend trading enabled: {is_open_override}")
            
            print("✅ Market hours validation works with proper timezone handling")
            self.test_results.append(("Market Hours Validation", True, "Proper UTC timezone handling"))
            
        except Exception as e:
            print(f"❌ Market hours test failed: {e}")
            self.test_results.append(("Market Hours Validation", False, str(e)))
    
    async def test_account_limits_enforcement(self):
        """Test that account limits are properly enforced"""
        print("\n🛡️ TEST 3: Account Limits Enforcement")
        print("-" * 40)
        
        try:
            # Create unified risk manager
            risk_manager = UnifiedRiskManager()
            
            # Test account configuration
            test_account_config = {
                'account_id': 'test_limits',
                'money_management_settings': {
                    'max_daily_trades': 3,
                    'max_open_positions': 1,
                    'max_pending_orders': 2,
                    'max_daily_loss': 5.0,
                    'max_drawdown_percent': 15.0
                }
            }
            
            # Initialize account limits
            await risk_manager.initialize_account_limits(test_account_config)
            
            # Check that limits were set correctly
            limits = risk_manager.account_limits.get('test_limits')
            if limits:
                print(f"✅ Account limits initialized:")
                print(f"   Max daily trades: {limits.max_daily_trades}")
                print(f"   Max open positions: {limits.max_open_positions}")
                print(f"   Max pending orders: {limits.max_pending_orders}")
                print(f"   Max daily loss: ${limits.max_daily_loss}")
                print(f"   Max drawdown: {limits.max_drawdown_percent}%")
                
                self.test_results.append(("Account Limits Enforcement", True, "Limits properly configured"))
            else:
                raise Exception("Account limits not initialized")
                
        except Exception as e:
            print(f"❌ Account limits test failed: {e}")
            self.test_results.append(("Account Limits Enforcement", False, str(e)))
    
    async def test_enhanced_logging(self):
        """Test that enhanced logging provides clear information"""
        print("\n📝 TEST 4: Enhanced Logging")
        print("-" * 40)
        
        try:
            # Test logging by checking if logger methods work
            logger.info("🧪 Testing enhanced logging functionality")
            logger.warning("⚠️ Testing warning level logging")
            logger.debug("🔍 Testing debug level logging")
            
            print("✅ Enhanced logging system functional")
            self.test_results.append(("Enhanced Logging", True, "Logging system working"))
            
        except Exception as e:
            print(f"❌ Enhanced logging test failed: {e}")
            self.test_results.append(("Enhanced Logging", False, str(e)))
    
    def print_test_summary(self):
        """Print summary of all test results"""
        print("\n📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {details}")
            if success:
                passed += 1
        
        print(f"\nOverall Result: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - Fixes are working correctly!")
        else:
            print("⚠️ Some tests failed - Review the issues above")

async def main():
    """Main test function"""
    tester = TestComprehensiveFixes()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
