#!/usr/bin/env python3
"""
Test Individual Account Settings Handling
Verifies that each account's specific settings are preserved and used correctly
"""

import sys
import os
import json
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_account_settings_preservation():
    """Test that individual account settings are preserved during grouping"""
    print("🔧 Testing Individual Account Settings Preservation...")
    
    try:
        from utils.symbol_mapper import get_symbol_mapper
        from money_management.percent_risk import PercentRiskStrategy
        from money_management.fixed_volume import FixedVolumeStrategy
        
        # Simulate accounts with different settings but same strategy/MM type
        test_accounts = [
            {
                'account_id': 'Mine',
                'server': 'RoboForex-ECN',
                'strategy_type': 'trend_following',
                'money_management_type': 'percent_risk',
                'symbols': [{'symbol': 'EURUSD', 'timeframe': 'M15'}],
                'money_management_settings': {
                    'risk_percent': 0.5,  # Conservative 0.5%
                    'max_daily_trades': 3,
                    'max_volume_per_trade': 0.01
                }
            },
            {
                'account_id': 'Customer 1',
                'server': 'CapitalxtendLLC-MU',
                'strategy_type': 'trend_following',
                'money_management_type': 'percent_risk',
                'symbols': [{'symbol': 'EURUSD!', 'timeframe': 'M15'}],
                'money_management_settings': {
                    'risk_percent': 2.0,  # More aggressive 2.0%
                    'max_daily_trades': 5,
                    'max_volume_per_trade': 0.1
                }
            }
        ]
        
        # Test that each account creates its own money management instance with correct settings
        print("\n📋 Testing Money Management Settings:")
        
        for account in test_accounts:
            mm_settings = account['money_management_settings']
            mm_strategy = PercentRiskStrategy(mm_settings)
            
            print(f"  Account: {account['account_id']}")
            print(f"    Risk Percent: {mm_strategy.config.get('risk_percent')}%")
            print(f"    Max Daily Trades: {mm_strategy.config.get('max_daily_trades')}")
            print(f"    Max Volume: {mm_strategy.config.get('max_volume_per_trade')}")
            
            # Verify settings are correct
            expected_risk = mm_settings['risk_percent']
            actual_risk = mm_strategy.config.get('risk_percent')
            
            if actual_risk == expected_risk:
                print(f"    ✅ Risk percent correct: {actual_risk}%")
            else:
                print(f"    ❌ Risk percent incorrect: expected {expected_risk}%, got {actual_risk}%")
                return False
        
        print("\n🎉 ACCOUNT SETTINGS PRESERVATION TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Account settings test failed: {e}")
        return False

def test_symbol_mapping_per_account():
    """Test that symbol mapping works correctly for each account"""
    print("\n🔧 Testing Symbol Mapping Per Account...")
    
    try:
        from utils.symbol_mapper import get_symbol_mapper
        
        mapper = get_symbol_mapper()
        
        # Test accounts with different servers and symbol formats
        test_accounts = [
            {
                'account_id': 'Mine',
                'server': 'RoboForex-ECN',
                'symbols': [{'symbol': 'EURUSD', 'timeframe': 'M15'}]
            },
            {
                'account_id': 'Customer 1',
                'server': 'CapitalxtendLLC-MU',
                'symbols': [{'symbol': 'EURUSD!', 'timeframe': 'M15'}]
            }
        ]
        
        print("\n📋 Testing Symbol Mapping for Each Account:")
        
        for account in test_accounts:
            server = account['server']
            for symbol_config in account['symbols']:
                original_symbol = symbol_config['symbol']
                
                # Test broker symbol mapping
                broker_symbol = mapper.get_broker_symbol('EURUSD', server)
                standard_symbol = mapper.get_standard_symbol(original_symbol, server)
                
                print(f"  Account: {account['account_id']} ({server})")
                print(f"    Original Symbol: {original_symbol}")
                print(f"    Broker Symbol: {broker_symbol}")
                print(f"    Standard Symbol: {standard_symbol}")
                
                # Verify mappings are correct
                if server == 'RoboForex-ECN':
                    if broker_symbol == 'EURUSD' and standard_symbol == 'EURUSD':
                        print(f"    ✅ RoboForex mapping correct")
                    else:
                        print(f"    ❌ RoboForex mapping incorrect")
                        return False
                elif server == 'CapitalxtendLLC-MU':
                    if broker_symbol == 'EURUSD!' and standard_symbol == 'EURUSD':
                        print(f"    ✅ CapitalxtendLLC mapping correct")
                    else:
                        print(f"    ❌ CapitalxtendLLC mapping incorrect")
                        return False
        
        print("\n🎉 SYMBOL MAPPING PER ACCOUNT TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Symbol mapping per account test failed: {e}")
        return False

def test_position_size_calculation_differences():
    """Test that position sizes are calculated differently for each account"""
    print("\n🔧 Testing Position Size Calculation Differences...")
    
    try:
        from money_management.percent_risk import PercentRiskStrategy
        from money_management.base_strategy import AccountInfo
        
        # Create account info (same for both to isolate the settings difference)
        account_info = AccountInfo(
            balance=1000.0,  # $1000 account
            equity=1000.0,
            margin=100.0,
            free_margin=900.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }
        
        # Test different risk percentages with volume limits
        conservative_settings = {
            'risk_percent': 0.5,  # 0.5% risk
            'max_volume_per_trade': 0.01  # Conservative volume limit
        }
        aggressive_settings = {
            'risk_percent': 2.0,  # 2.0% risk
            'max_volume_per_trade': 1.0   # Higher volume limit to allow full risk
        }
        
        conservative_mm = PercentRiskStrategy(conservative_settings)
        aggressive_mm = PercentRiskStrategy(aggressive_settings)
        
        # Calculate position sizes for same trade setup
        entry_price = 1.1000
        stop_loss = 1.0950  # 50 pips stop loss
        
        conservative_params = conservative_mm.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=entry_price,
            stop_loss=stop_loss,
            trade_history=[],
            market_data=market_data
        )
        
        aggressive_params = aggressive_mm.calculate_position_size(
            account_info=account_info,
            symbol="EURUSD",
            entry_price=entry_price,
            stop_loss=stop_loss,
            trade_history=[],
            market_data=market_data
        )
        
        print("\n📋 Position Size Calculations:")
        print(f"  Conservative Account (0.5% risk):")
        print(f"    Volume: {conservative_params.volume}")
        print(f"    Risk Amount: ${conservative_params.risk_amount:.2f}")
        print(f"  Aggressive Account (2.0% risk):")
        print(f"    Volume: {aggressive_params.volume}")
        print(f"    Risk Amount: ${aggressive_params.risk_amount:.2f}")
        
        # Verify that aggressive account has larger position size
        if aggressive_params.volume > conservative_params.volume:
            print(f"    ✅ Aggressive account has larger position size")
        else:
            print(f"    ❌ Position sizes not calculated correctly")
            return False
        
        # Verify risk amounts are reasonable (may be limited by volume constraints)
        expected_conservative_risk = 1000.0 * 0.005  # 0.5% of $1000 = $5
        max_aggressive_risk = 1000.0 * 0.02          # 2.0% of $1000 = $20 (max possible)

        # Conservative should be close to expected
        if abs(conservative_params.risk_amount - expected_conservative_risk) < 1.0:
            print(f"    ✅ Conservative risk amount correct: ${conservative_params.risk_amount:.2f}")
        else:
            print(f"    ❌ Conservative risk amount incorrect: expected ${expected_conservative_risk:.2f}, got ${conservative_params.risk_amount:.2f}")
            return False

        # Aggressive should be higher than conservative (may be limited by volume constraints)
        if aggressive_params.risk_amount > conservative_params.risk_amount:
            print(f"    ✅ Aggressive risk amount higher: ${aggressive_params.risk_amount:.2f}")
        else:
            print(f"    ❌ Aggressive risk amount not higher than conservative")
            return False

        # Aggressive should not exceed maximum possible risk
        if aggressive_params.risk_amount <= max_aggressive_risk:
            print(f"    ✅ Aggressive risk amount within limits: ${aggressive_params.risk_amount:.2f} <= ${max_aggressive_risk:.2f}")
        else:
            print(f"    ❌ Aggressive risk amount exceeds maximum: ${aggressive_params.risk_amount:.2f} > ${max_aggressive_risk:.2f}")
            return False
        
        print("\n🎉 POSITION SIZE CALCULATION TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Position size calculation test failed: {e}")
        return False

def test_config_file_account_settings():
    """Test that the actual config file accounts have different settings"""
    print("\n🔧 Testing Config File Account Settings...")
    
    try:
        # Load the actual config file
        config_path = "config/accounts.json"
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        accounts = config.get('accounts', [])
        if len(accounts) < 2:
            print(f"❌ Need at least 2 accounts in config for testing")
            return False
        
        print(f"\n📋 Found {len(accounts)} accounts in config:")
        
        settings_different = False
        for i, account in enumerate(accounts):
            account_id = account.get('account_id', f'Account_{i}')
            server = account.get('server', 'Unknown')
            mm_settings = account.get('money_management_settings', {})
            
            print(f"  Account: {account_id} ({server})")
            print(f"    Risk Percent: {mm_settings.get('risk_percent', 'Not set')}%")
            print(f"    Max Daily Trades: {mm_settings.get('max_daily_trades', 'Not set')}")
            print(f"    Max Volume: {mm_settings.get('max_volume_per_trade', 'Not set')}")
            
            # Check if accounts have different settings
            if i > 0:
                prev_settings = accounts[i-1].get('money_management_settings', {})
                if mm_settings != prev_settings:
                    settings_different = True
        
        if settings_different:
            print(f"    ✅ Accounts have different settings (good for testing)")
        else:
            print(f"    ⚠️  All accounts have same settings (may not test individual handling)")
        
        print("\n🎉 CONFIG FILE ACCOUNT SETTINGS TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Config file test failed: {e}")
        return False

def main():
    """Run all individual account settings tests"""
    print("🚀 INDIVIDUAL ACCOUNT SETTINGS TESTS")
    print("=" * 60)
    
    tests = [
        ("Account Settings Preservation", test_account_settings_preservation),
        ("Symbol Mapping Per Account", test_symbol_mapping_per_account),
        ("Position Size Calculation Differences", test_position_size_calculation_differences),
        ("Config File Account Settings", test_config_file_account_settings),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("✅ ALL INDIVIDUAL ACCOUNT SETTINGS TESTS PASSED!")
        print("✅ Each account's settings are properly preserved and used!")
        print("\n🎯 EXPECTED BEHAVIOR:")
        print("  • Mine account uses 0.5% risk, max 3 trades, 0.01 volume")
        print("  • Customer accounts use 2.0% risk, max 5 trades, higher volumes")
        print("  • Each account uses its broker-specific symbol format")
        print("  • Position sizes calculated individually per account")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        print("❌ Individual account settings may not be handled correctly.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
