#!/usr/bin/env python3
"""
Comprehensive Unit Tests for Trading System
Tests all functions and methods with 100% coverage
"""

import unittest
import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all components
from strategies.base_strategy import MarketData, TradingSignal, StrategyType
from strategies.trend_following import TrendFollowingStrategy
from strategies.mean_reversion import MeanReversionStrategy
from strategies.breakout import BreakoutStrategy

from money_management.base_strategy import AccountInfo, TradeParameters, MoneyManagementType
from money_management.percent_risk import PercentRiskStrategy
from money_management.fixed_volume import FixedVolumeStrategy
from money_management.martingale import MartingaleStrategy
from money_management.anti_martingale import AntiMartingaleStrategy

from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import PromptBuilder

from account_management.account_manager import AccountManager
from account_management.models import TradingAccount

from logging_system.logger import setup_logger, get_logger, trading_logger


class TestMarketData(unittest.TestCase):
    """Test MarketData class"""
    
    def test_market_data_creation(self):
        """Test MarketData object creation"""
        candles = [
            {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
        ]
        
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=candles,
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001,
            pip_value=10.0,
            min_volume=0.01,
            max_volume=100.0
        )
        
        self.assertEqual(market_data.symbol, "EURUSD")
        self.assertEqual(market_data.timeframe, "H1")
        self.assertEqual(market_data.current_price, 1.1010)
        self.assertEqual(market_data.pip_size, 0.0001)
        self.assertEqual(len(market_data.candles), 1)
    
    def test_market_data_optional_fields(self):
        """Test MarketData with optional fields"""
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
        
        self.assertIsNone(market_data.pip_size)
        self.assertIsNone(market_data.pip_value)
        self.assertIsNone(market_data.min_volume)


class TestTradingSignal(unittest.TestCase):
    """Test TradingSignal class"""
    
    def test_trading_signal_creation(self):
        """Test TradingSignal object creation"""
        signal = TradingSignal(
            action="BUY",
            confidence=0.85,
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="Strong uptrend confirmed",
            risk_level="MEDIUM"
        )
        
        self.assertEqual(signal.action, "BUY")
        self.assertEqual(signal.confidence, 0.85)
        self.assertEqual(signal.entry_price, 1.1000)
        self.assertEqual(signal.stop_loss, 1.0950)
        self.assertEqual(signal.take_profit, 1.1150)


class TestAccountInfo(unittest.TestCase):
    """Test AccountInfo class"""
    
    def test_account_info_creation(self):
        """Test AccountInfo object creation"""
        account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        
        self.assertEqual(account_info.balance, 10000.0)
        self.assertEqual(account_info.equity, 10000.0)
        self.assertEqual(account_info.currency, "USD")
        self.assertEqual(account_info.leverage, 100)


class TestTradeParameters(unittest.TestCase):
    """Test TradeParameters class"""
    
    def test_trade_parameters_creation(self):
        """Test TradeParameters object creation"""
        params = TradeParameters(
            volume=0.1,
            stop_loss=1.0950,
            take_profit=1.1150,
            risk_amount=200.0,
            max_loss=200.0,
            confidence_level=0.8
        )
        
        self.assertEqual(params.volume, 0.1)
        self.assertEqual(params.stop_loss, 1.0950)
        self.assertEqual(params.risk_amount, 200.0)
        self.assertEqual(params.confidence_level, 0.8)


class TestTrendFollowingStrategy(unittest.TestCase):
    """Test TrendFollowingStrategy"""
    
    def setUp(self):
        """Set up test data"""
        self.strategy = TrendFollowingStrategy({'magic_number': 12345})
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
            ],
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015
        )
    
    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), StrategyType.TREND_FOLLOWING)
    
    def test_strategy_name(self):
        """Test strategy name"""
        self.assertEqual(self.strategy.get_strategy_name(), "Advanced Trend Following")
    
    def test_ai_prompt_generation(self):
        """Test AI prompt generation"""
        trade_history = [
            {'profit': 100.0, 'symbol': 'EURUSD', 'close_time': datetime.now()}
        ]
        account_info = {'balance': 10000.0, 'equity': 10000.0}
        
        prompt = self.strategy.get_ai_prompt(self.market_data, trade_history, account_info)
        
        self.assertIn("TREND FOLLOWING", prompt)
        self.assertIn("EURUSD", prompt)
        self.assertIn("H1", prompt)
        self.assertIn("12345", prompt)  # Magic number
    
    def test_signal_validation_valid(self):
        """Test valid signal validation"""
        signal = TradingSignal(
            action="BUY",
            confidence=0.85,
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="Strong uptrend",
            risk_level="MEDIUM"
        )
        
        self.assertTrue(self.strategy.validate_signal(signal, self.market_data))
    
    def test_signal_validation_invalid_no_stop_loss(self):
        """Test invalid signal validation - no stop loss"""
        signal = TradingSignal(
            action="BUY",
            confidence=0.85,
            entry_price=1.1000,
            stop_loss=None,
            take_profit=1.1150,
            reasoning="Strong uptrend",
            risk_level="MEDIUM"
        )
        
        self.assertFalse(self.strategy.validate_signal(signal, self.market_data))
    
    def test_signal_validation_invalid_low_confidence(self):
        """Test invalid signal validation - low confidence"""
        signal = TradingSignal(
            action="BUY",
            confidence=0.70,  # Below 0.8 threshold
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="Weak signal",
            risk_level="MEDIUM"
        )
        
        self.assertFalse(self.strategy.validate_signal(signal, self.market_data))


class TestMeanReversionStrategy(unittest.TestCase):
    """Test MeanReversionStrategy"""
    
    def setUp(self):
        """Set up test data"""
        self.strategy = MeanReversionStrategy({'magic_number': 12346})
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="M15",
            candles=[],
            current_price=1.1010,
            spread=1.0,  # Tight spread for scalping
            volume=1000,
            volatility=0.0015
        )
    
    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), StrategyType.MEAN_REVERSION)
    
    def test_strategy_name(self):
        """Test strategy name"""
        self.assertEqual(self.strategy.get_strategy_name(), "Mean Reversion Scalping")
    
    def test_signal_validation_valid(self):
        """Test valid mean reversion signal"""
        signal = TradingSignal(
            action="SELL",
            confidence=0.90,  # High confidence for scalping
            entry_price=1.1010,
            stop_loss=1.1020,
            take_profit=1.0990,
            reasoning="Overbought conditions",
            risk_level="LOW"
        )
        
        self.assertTrue(self.strategy.validate_signal(signal, self.market_data))
    
    def test_signal_validation_invalid_wide_spread(self):
        """Test invalid signal - spread too wide for scalping"""
        wide_spread_data = MarketData(
            symbol="EURUSD",
            timeframe="M15",
            candles=[],
            current_price=1.1010,
            spread=2.0,  # Too wide for scalping
            volume=1000,
            volatility=0.0015
        )
        
        signal = TradingSignal(
            action="SELL",
            confidence=0.90,
            entry_price=1.1010,
            stop_loss=1.1020,
            take_profit=1.0990,
            reasoning="Overbought conditions",
            risk_level="LOW"
        )
        
        self.assertFalse(self.strategy.validate_signal(signal, wide_spread_data))


class TestBreakoutStrategy(unittest.TestCase):
    """Test BreakoutStrategy"""
    
    def setUp(self):
        """Set up test data"""
        self.strategy = BreakoutStrategy({'magic_number': 12347, 'average_volume': 1000})
        self.market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1010,
            spread=2.0,
            volume=1800,  # High volume for breakout
            volatility=0.0015
        )
    
    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), StrategyType.BREAKOUT)
    
    def test_signal_validation_valid(self):
        """Test valid breakout signal"""
        signal = TradingSignal(
            action="BUY",
            confidence=0.80,
            entry_price=1.1010,
            stop_loss=1.0990,
            take_profit=1.1050,
            reasoning="Volume breakout confirmed",
            risk_level="MEDIUM"
        )
        
        self.assertTrue(self.strategy.validate_signal(signal, self.market_data))
    
    def test_signal_validation_invalid_low_volume(self):
        """Test invalid signal - insufficient volume"""
        low_volume_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1010,
            spread=2.0,
            volume=800,  # Below 1.5x average (1500)
            volatility=0.0015
        )
        
        signal = TradingSignal(
            action="BUY",
            confidence=0.80,
            entry_price=1.1010,
            stop_loss=1.0990,
            take_profit=1.1050,
            reasoning="Weak breakout",
            risk_level="MEDIUM"
        )
        
        self.assertFalse(self.strategy.validate_signal(signal, low_volume_data))


class TestPercentRiskStrategy(unittest.TestCase):
    """Test PercentRiskStrategy"""

    def setUp(self):
        """Set up test data"""
        self.strategy = PercentRiskStrategy({'risk_percent': 2.0})
        self.account_info = AccountInfo(
            balance=10000.0,
            equity=10000.0,
            margin=1000.0,
            free_margin=9000.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        self.market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }

    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), MoneyManagementType.PERCENT_RISK)

    def test_position_size_calculation_with_stop_loss(self):
        """Test position size calculation with stop loss"""
        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,  # 50 pips
            trade_history=[],
            market_data=self.market_data
        )

        self.assertIsInstance(trade_params, TradeParameters)
        self.assertGreater(trade_params.volume, 0)
        self.assertEqual(trade_params.stop_loss, 1.0950)
        self.assertGreater(trade_params.risk_amount, 0)
        self.assertLessEqual(trade_params.risk_amount, 200.0)  # 2% of 10000

    def test_position_size_calculation_no_stop_loss(self):
        """Test position size calculation without stop loss"""
        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=None,
            trade_history=[],
            market_data=self.market_data
        )

        self.assertIsInstance(trade_params, TradeParameters)
        self.assertGreater(trade_params.volume, 0)
        self.assertIsNone(trade_params.stop_loss)
        self.assertEqual(trade_params.confidence_level, 0.3)  # Lower confidence without SL

    def test_ai_prompt_generation(self):
        """Test AI prompt generation"""
        trade_history = [
            {'profit': 100.0, 'symbol': 'EURUSD'},
            {'profit': -50.0, 'symbol': 'EURUSD'}
        ]

        prompt = self.strategy.get_ai_prompt(self.account_info, trade_history)

        self.assertIn("PERCENT RISK", prompt)
        self.assertIn("2.0%", prompt)
        self.assertIn("$200.00", prompt)  # Risk amount


class TestFixedVolumeStrategy(unittest.TestCase):
    """Test FixedVolumeStrategy"""

    def setUp(self):
        """Set up test data"""
        self.strategy = FixedVolumeStrategy({'fixed_volume': 0.1})
        self.account_info = AccountInfo(
            balance=5000.0,
            equity=5000.0,
            margin=500.0,
            free_margin=4500.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        self.market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }

    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), MoneyManagementType.FIXED_VOLUME)

    def test_position_size_calculation(self):
        """Test fixed position size calculation"""
        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],
            market_data=self.market_data
        )

        self.assertEqual(trade_params.volume, 0.1)  # Fixed volume
        self.assertEqual(trade_params.stop_loss, 1.0950)
        self.assertGreater(trade_params.risk_amount, 0)
        self.assertEqual(trade_params.confidence_level, 0.5)  # Neutral confidence

    def test_volume_adjustment_below_minimum(self):
        """Test volume adjustment when below minimum"""
        low_volume_strategy = FixedVolumeStrategy({'fixed_volume': 0.005})  # Below min

        trade_params = low_volume_strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],
            market_data=self.market_data
        )

        self.assertEqual(trade_params.volume, 0.01)  # Adjusted to minimum


class TestMartingaleStrategy(unittest.TestCase):
    """Test MartingaleStrategy"""

    def setUp(self):
        """Set up test data"""
        self.strategy = MartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 8})
        self.account_info = AccountInfo(
            balance=1000.0,
            equity=1000.0,
            margin=100.0,
            free_margin=900.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        self.market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }

    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), MoneyManagementType.MARTINGALE)

    def test_position_size_no_losses(self):
        """Test position size with no previous losses"""
        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],  # No losses
            market_data=self.market_data
        )

        self.assertEqual(trade_params.volume, 0.01)  # Base volume

    def test_position_size_with_losses(self):
        """Test position size with consecutive losses"""
        losing_trades = [
            {'profit': -50.0, 'symbol': 'EURUSD', 'close_time': datetime.now()},
            {'profit': -100.0, 'symbol': 'EURUSD', 'close_time': datetime.now() - timedelta(hours=1)}
        ]

        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=losing_trades,
            market_data=self.market_data
        )

        self.assertEqual(trade_params.volume, 0.04)  # 0.01 * 2^2 = 0.04


class TestAntiMartingaleStrategy(unittest.TestCase):
    """Test AntiMartingaleStrategy"""

    def setUp(self):
        """Set up test data"""
        self.strategy = AntiMartingaleStrategy({'base_volume': 0.01, 'max_multiplier': 4})
        self.account_info = AccountInfo(
            balance=2000.0,
            equity=2000.0,
            margin=200.0,
            free_margin=1800.0,
            margin_level=1000.0,
            currency="USD",
            leverage=100
        )
        self.market_data = {
            'pip_value': 10.0,
            'pip_size': 0.0001,
            'min_volume': 0.01,
            'max_volume': 100.0
        }

    def test_strategy_type(self):
        """Test strategy type"""
        self.assertEqual(self.strategy.get_strategy_type(), MoneyManagementType.ANTI_MARTINGALE)

    def test_position_size_no_wins(self):
        """Test position size with no previous wins"""
        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=[],
            market_data=self.market_data
        )

        self.assertEqual(trade_params.volume, 0.01)  # Base volume

    def test_position_size_with_wins(self):
        """Test position size with consecutive wins"""
        winning_trades = [
            {'profit': 100.0, 'symbol': 'EURUSD', 'close_time': datetime.now()},
            {'profit': 150.0, 'symbol': 'EURUSD', 'close_time': datetime.now() - timedelta(hours=1)}
        ]

        trade_params = self.strategy.calculate_position_size(
            account_info=self.account_info,
            symbol="EURUSD",
            entry_price=1.1000,
            stop_loss=1.0950,
            trade_history=winning_trades,
            market_data=self.market_data
        )

        self.assertEqual(trade_params.volume, 0.02)  # 0.01 * (1 + 2 * 0.5) = 0.02


if __name__ == '__main__':
    # Run tests with detailed output
    unittest.main(verbosity=2)
