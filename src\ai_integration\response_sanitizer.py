"""
AI Response Sanitization and JSON Parsing Utilities
Critical for ensuring AI trading decisions are never lost due to formatting issues
"""

import json
import re
from typing import Dict, Any, Optional, Tuple
from logging_system.logger import get_logger

logger = get_logger(__name__)


class ResponseSanitizer:
    """Sanitizes AI responses to ensure reliable JSON parsing"""
    
    def __init__(self):
        # Control characters that commonly break JSON parsing
        self.control_chars_pattern = re.compile(r'[\x00-\x1f\x7f-\x9f]')
        
        # Common problematic characters and their replacements
        self.char_replacements = {
            '\n': '\\n',
            '\r': '\\r',
            '\t': '\\t',
            '\b': '\\b',
            '\f': '\\f',
            '"': '\\"',
            '\\': '\\\\',
        }
        
        # Pattern to find JSON objects in text
        self.json_pattern = re.compile(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', re.DOTALL)
        
        # Backup patterns for extracting trading data when JSON fails
        self.trading_patterns = {
            'action': re.compile(r'"action"\s*:\s*"([^"]+)"', re.IGNORECASE),
            'confidence': re.compile(r'"confidence"\s*:\s*([0-9.]+)', re.IGNORECASE),
            'reasoning': re.compile(r'"reasoning"\s*:\s*"([^"]+)"', re.IGNORECASE),
            'risk_level': re.compile(r'"risk_level"\s*:\s*"([^"]+)"', re.IGNORECASE),
            'entry_price': re.compile(r'"entry_price"\s*:\s*([0-9.]+|null)', re.IGNORECASE),
            'stop_loss': re.compile(r'"stop_loss"\s*:\s*([0-9.]+|null)', re.IGNORECASE),
            'take_profit': re.compile(r'"take_profit"\s*:\s*([0-9.]+|null)', re.IGNORECASE),
        }
    
    def sanitize_response(self, text: str) -> Tuple[str, bool]:
        """
        Sanitize AI response text for JSON parsing
        
        Returns:
            Tuple of (sanitized_text, was_sanitized)
        """
        if not text or not isinstance(text, str):
            return text, False
        
        original_text = text
        was_sanitized = False
        
        # Remove or replace control characters
        if self.control_chars_pattern.search(text):
            # Replace common control characters with their escaped versions
            for char, replacement in self.char_replacements.items():
                if char in text and char not in ['"', '\\']:  # Handle quotes and backslashes carefully
                    text = text.replace(char, replacement)
                    was_sanitized = True
            
            # Remove remaining control characters
            text = self.control_chars_pattern.sub('', text)
            was_sanitized = True
        
        # Fix common JSON formatting issues
        text, format_fixed = self._fix_json_formatting(text)
        if format_fixed:
            was_sanitized = True
        
        if was_sanitized:
            logger.info(f"🧹 Sanitized AI response - removed control characters and fixed formatting")
            logger.debug(f"Original length: {len(original_text)}, Sanitized length: {len(text)}")
        
        return text, was_sanitized
    
    def _fix_json_formatting(self, text: str) -> Tuple[str, bool]:
        """Fix common JSON formatting issues"""
        original_text = text
        was_fixed = False
        
        # Fix unescaped quotes in strings (common issue)
        # This is a simplified approach - more sophisticated parsing might be needed
        try:
            # Find potential JSON blocks
            json_matches = self.json_pattern.findall(text)
            if json_matches:
                for json_match in json_matches:
                    try:
                        # Try to parse as-is first
                        json.loads(json_match)
                    except json.JSONDecodeError:
                        # Try to fix common issues
                        fixed_json = self._fix_json_block(json_match)
                        if fixed_json != json_match:
                            text = text.replace(json_match, fixed_json)
                            was_fixed = True
        except Exception as e:
            logger.debug(f"Error in JSON formatting fix: {e}")
        
        return text, was_fixed
    
    def _fix_json_block(self, json_text: str) -> str:
        """Fix a specific JSON block"""
        try:
            # Fix trailing commas
            json_text = re.sub(r',(\s*[}\]])', r'\1', json_text)
            
            # Fix unescaped quotes in string values (basic approach)
            # This is a simplified fix - more complex scenarios might need better handling
            json_text = re.sub(r':\s*"([^"]*)"([^",}\]]*)"([^",}\]]*)"', r': "\1\2\3"', json_text)
            
            return json_text
        except Exception:
            return json_text
    
    def extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Extract and parse JSON from AI response text with multiple fallback strategies
        
        Returns:
            Parsed JSON dict or None if extraction fails
        """
        if not text:
            return None
        
        # Strategy 1: Direct JSON parsing after sanitization
        sanitized_text, was_sanitized = self.sanitize_response(text)
        
        try:
            # Try to parse the entire sanitized text
            return json.loads(sanitized_text)
        except json.JSONDecodeError:
            pass
        
        # Strategy 2: Find JSON blocks in the text
        json_matches = self.json_pattern.findall(sanitized_text)
        
        for json_match in json_matches:
            try:
                parsed = json.loads(json_match)
                if self._is_valid_trading_response(parsed):
                    logger.info("✅ Successfully extracted JSON from AI response using pattern matching")
                    return parsed
            except json.JSONDecodeError:
                continue
        
        # Strategy 3: Try to find and extract JSON between common delimiters
        json_delimiters = [
            (r'```json\s*(\{.*?\})\s*```', re.DOTALL),
            (r'```\s*(\{.*?\})\s*```', re.DOTALL),
            (r'(\{[^{}]*"action"[^{}]*\})', re.DOTALL | re.IGNORECASE),
        ]
        
        for pattern, flags in json_delimiters:
            matches = re.findall(pattern, sanitized_text, flags)
            for match in matches:
                try:
                    parsed = json.loads(match)
                    if self._is_valid_trading_response(parsed):
                        logger.info("✅ Successfully extracted JSON using delimiter pattern")
                        return parsed
                except json.JSONDecodeError:
                    continue
        
        # Strategy 4: Manual field extraction as last resort
        logger.warning("🔧 JSON parsing failed, attempting manual field extraction")
        return self._extract_fields_manually(sanitized_text)
    
    def _is_valid_trading_response(self, parsed_data: Dict[str, Any]) -> bool:
        """Check if parsed data contains valid trading response fields"""
        required_fields = ['action', 'confidence']
        return isinstance(parsed_data, dict) and all(field in parsed_data for field in required_fields)
    
    def _extract_fields_manually(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Manually extract trading fields from text when JSON parsing fails completely
        This is a critical fallback to ensure we never lose AI trading decisions
        """
        try:
            extracted = {}
            
            # Extract each field using regex patterns
            for field, pattern in self.trading_patterns.items():
                match = pattern.search(text)
                if match:
                    value = match.group(1)
                    
                    # Convert numeric fields
                    if field in ['confidence', 'entry_price', 'stop_loss', 'take_profit']:
                        try:
                            if value.lower() == 'null':
                                extracted[field] = None
                            else:
                                extracted[field] = float(value)
                        except ValueError:
                            extracted[field] = None
                    else:
                        extracted[field] = value
            
            # Only return if we have minimum required fields
            if 'action' in extracted and 'confidence' in extracted:
                # Add default values for missing fields
                extracted.setdefault('reasoning', 'Extracted from malformed JSON response')
                extracted.setdefault('risk_level', 'MEDIUM')
                extracted.setdefault('entry_price', None)
                extracted.setdefault('stop_loss', None)
                extracted.setdefault('take_profit', None)
                extracted.setdefault('take_profit_levels', None)
                extracted.setdefault('market_analysis', 'Unable to parse from response')
                extracted.setdefault('strategy_alignment', 'Unable to parse from response')
                extracted.setdefault('risk_assessment', 'Unable to parse from response')
                extracted.setdefault('additional_signals', [])
                
                logger.warning("🔧 Successfully extracted trading decision using manual field extraction")
                logger.info(f"Extracted fields: action={extracted.get('action')}, confidence={extracted.get('confidence')}")
                
                return extracted
            
            return None
            
        except Exception as e:
            logger.error(f"Manual field extraction failed: {e}")
            return None
    
    def create_intelligent_fallback(self, original_text: str, error_message: str) -> Dict[str, Any]:
        """
        Create an intelligent fallback response that tries to preserve any useful information
        from the original AI response even when parsing completely fails
        """
        from datetime import datetime
        
        # Try to extract any useful information from the text
        fallback_data = {
            "action": "HOLD",
            "confidence": 0.3,
            "entry_price": None,
            "stop_loss": None,
            "take_profit": None,
            "take_profit_levels": None,
            "reasoning": f"JSON parsing failed: {error_message}. Raw AI response available for manual review.",
            "risk_level": "MEDIUM",
            "market_analysis": "Unable to parse structured analysis from AI response",
            "strategy_alignment": "Unknown due to parsing error",
            "risk_assessment": "Moderate risk due to parsing issues",
            "additional_signals": [],
            "timestamp": datetime.now().isoformat(),
            "parsing_error": True,
            "error_message": error_message,
            "raw_response": original_text[:500] + "..." if len(original_text) > 500 else original_text
        }
        
        # Try to detect sentiment or action words in the raw text
        text_lower = original_text.lower()
        
        # Look for action indicators
        if any(word in text_lower for word in ['buy', 'long', 'bullish', 'upward']):
            fallback_data["action"] = "BUY"
            fallback_data["confidence"] = 0.4
            fallback_data["reasoning"] = f"Detected bullish sentiment in AI response despite parsing error: {error_message}"
        elif any(word in text_lower for word in ['sell', 'short', 'bearish', 'downward']):
            fallback_data["action"] = "SELL"
            fallback_data["confidence"] = 0.4
            fallback_data["reasoning"] = f"Detected bearish sentiment in AI response despite parsing error: {error_message}"
        elif any(word in text_lower for word in ['close', 'exit', 'stop']):
            fallback_data["action"] = "CLOSE"
            fallback_data["confidence"] = 0.5
            fallback_data["reasoning"] = f"Detected exit signal in AI response despite parsing error: {error_message}"
        
        # Look for risk indicators
        if any(word in text_lower for word in ['high risk', 'dangerous', 'volatile']):
            fallback_data["risk_level"] = "HIGH"
        elif any(word in text_lower for word in ['low risk', 'safe', 'stable']):
            fallback_data["risk_level"] = "LOW"
        
        logger.warning("🔧 Created intelligent fallback response with sentiment analysis")
        
        return fallback_data


# Global sanitizer instance
response_sanitizer = ResponseSanitizer()
