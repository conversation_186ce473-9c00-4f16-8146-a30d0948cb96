#!/usr/bin/env python3
"""
Check real-time account balances to investigate discrepancies
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

import MetaTrader5 as mt5
from account_management.account_manager import Account<PERSON>anager
from datetime import datetime, timedel<PERSON>

def main():
    print("🔍 CRITICAL BALANCE INVESTIGATION")
    print("=" * 50)
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ Failed to initialize MT5')
        return False
    
    try:
        # Load accounts
        account_manager = AccountManager()
        accounts = account_manager.get_all_accounts()
        
        print(f"📊 Checking {len(accounts)} accounts...")
        print()
        
        total_balance_discrepancy = 0.0
        
        for account in accounts:
            print(f"🔍 Account: {account.account_id} ({account.account_number})")
            print(f"   Server: {account.server}")
            
            try:
                # Login to account
                login_result = mt5.login(
                    login=account.account_number,
                    password=account.password,
                    server=account.server
                )
                
                if login_result:
                    account_info = mt5.account_info()
                    if account_info:
                        print(f"   ✅ REAL-TIME Balance: ${account_info.balance:.2f}")
                        print(f"   ✅ REAL-TIME Equity: ${account_info.equity:.2f}")
                        print(f"   ✅ REAL-TIME Profit: ${account_info.profit:.2f}")
                        print(f"   📊 Margin: ${account_info.margin:.2f}")
                        print(f"   📊 Free Margin: ${account_info.margin_free:.2f}")
                        print(f"   📊 Margin Level: {account_info.margin_level:.1f}%")
                        
                        # Check for significant losses
                        if account_info.profit < -5.0:
                            print(f"   🚨 SIGNIFICANT LOSS DETECTED: ${account_info.profit:.2f}")
                            total_balance_discrepancy += abs(account_info.profit)
                        
                        # Get recent positions
                        positions = mt5.positions_get()
                        if positions:
                            print(f"   📈 Open Positions: {len(positions)}")
                            total_unrealized = sum(pos.profit for pos in positions)
                            print(f"   💰 Total Unrealized P&L: ${total_unrealized:.2f}")
                            
                            for pos in positions:
                                print(f"      Position {pos.ticket}: {pos.symbol} {pos.type_str} {pos.volume} lots, P&L: ${pos.profit:.2f}")
                        else:
                            print(f"   📈 Open Positions: 0")
                        
                        # Get recent orders
                        orders = mt5.orders_get()
                        if orders:
                            print(f"   📋 Pending Orders: {len(orders)}")
                            for order in orders:
                                print(f"      Order {order.ticket}: {order.symbol} {order.type_str} {order.volume} lots @ {order.price_open}")
                        else:
                            print(f"   📋 Pending Orders: 0")
                        
                        # Get recent trade history (last 24 hours)
                        end_time = datetime.now()
                        start_time = end_time - timedelta(hours=24)
                        
                        deals = mt5.history_deals_get(start_time, end_time)
                        if deals:
                            print(f"   📜 Recent Deals (24h): {len(deals)}")
                            total_realized = sum(deal.profit for deal in deals if deal.profit != 0)
                            print(f"   💸 Total Realized P&L (24h): ${total_realized:.2f}")
                            
                            # Show significant losses
                            for deal in deals:
                                if deal.profit < -2.0:  # Show losses > $2
                                    deal_time = datetime.fromtimestamp(deal.time)
                                    print(f"      🔴 LOSS: {deal_time.strftime('%H:%M:%S')} - {deal.symbol} ${deal.profit:.2f}")
                        
                    else:
                        print(f"   ❌ Failed to get account info")
                else:
                    error = mt5.last_error()
                    print(f"   ❌ Failed to login: {error}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
            
            print("-" * 40)
        
        print(f"\n🚨 TOTAL BALANCE DISCREPANCY DETECTED: ${total_balance_discrepancy:.2f}")
        
        if total_balance_discrepancy > 5.0:
            print("⚠️  CRITICAL: Significant losses detected that may not be reflected in logs!")
            print("⚠️  This suggests:")
            print("   - Trade modification errors may be causing actual losses")
            print("   - Logging system may have gaps")
            print("   - Hidden trades or failed risk management")
        
        return True
        
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    main()
