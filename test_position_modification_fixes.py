#!/usr/bin/env python3
"""
Test script to verify the position modification fixes for MT5 error 10025 "No changes"
This script tests the new logic that prevents unnecessary modification attempts.
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from mt5_integration.mt5_client import MT5Client
from validation.trade_validator import TradeValidator, ValidationResult
from trade_management.trade_manager import TradeManager
from account_management.account_manager import AccountManager
from logging_system.logger import get_logger

logger = get_logger(__name__)

class MockMT5Result:
    """Mock MT5 result object"""
    def __init__(self, retcode, comment=""):
        self.retcode = retcode
        self.comment = comment

class MockPosition:
    """Mock position object"""
    def __init__(self, ticket, symbol, sl, tp, type_=0, price_open=1.0, price_current=1.0):
        self.ticket = ticket
        self.symbol = symbol
        self.sl = sl
        self.tp = tp
        self.type = type_
        self.price_open = price_open
        self.price_current = price_current

class MockSymbolInfo:
    """Mock symbol info object"""
    def __init__(self, digits=5, point=0.00001, trade_stops_level=10):
        self.digits = digits
        self.point = point
        self.trade_stops_level = trade_stops_level

async def test_no_changes_detection():
    """Test that the system correctly detects when no changes are needed"""
    print("\n" + "="*60)
    print("🧪 TESTING: No Changes Detection")
    print("="*60)
    
    # Mock MT5 functions
    with patch('mt5_integration.mt5_client.mt5') as mock_mt5:
        # Setup mock position with existing SL/TP
        mock_position = MockPosition(
            ticket=12345,
            symbol="EURUSD!",
            sl=1.16168,  # Current stop loss
            tp=1.16786,  # Current take profit
            type_=0  # Buy position
        )
        
        mock_mt5.positions_get.return_value = [mock_position]
        mock_mt5.symbol_info.return_value = MockSymbolInfo(digits=5)
        
        # Create MT5 client
        mt5_client = MT5Client()
        mt5_client.current_account = Mock()
        mt5_client.current_account.account_id = "test_account"
        
        # Test 1: Attempt to modify with identical values
        print("\n🔍 Test 1: Modifying with identical values")
        print(f"Current SL: {mock_position.sl}, Current TP: {mock_position.tp}")
        print(f"Requested SL: {mock_position.sl}, Requested TP: {mock_position.tp}")
        
        result = mt5_client.modify_position(
            ticket=12345,
            stop_loss=1.16168,  # Same as current
            take_profit=1.16786  # Same as current
        )
        
        # Should return True without calling order_send
        assert result == True, "Should return True for identical values"
        mock_mt5.order_send.assert_not_called()
        print("✅ Test 1 PASSED: No modification attempt made for identical values")
        
        # Reset mock
        mock_mt5.reset_mock()
        
        # Test 2: Attempt to modify with slightly different values (within tolerance)
        print("\n🔍 Test 2: Modifying with values within tolerance")
        result = mt5_client.modify_position(
            ticket=12345,
            stop_loss=1.161680001,  # Tiny difference
            take_profit=1.167860001  # Tiny difference
        )
        
        assert result == True, "Should return True for values within tolerance"
        mock_mt5.order_send.assert_not_called()
        print("✅ Test 2 PASSED: No modification attempt made for values within tolerance")
        
        # Reset mock
        mock_mt5.reset_mock()
        
        # Test 3: Attempt to modify with actually different values
        print("\n🔍 Test 3: Modifying with actually different values")
        mock_mt5.order_send.return_value = MockMT5Result(10009)  # Success (TRADE_RETCODE_DONE)
        
        result = mt5_client.modify_position(
            ticket=12345,
            stop_loss=1.16100,  # Actually different
            take_profit=1.16800   # Actually different
        )
        
        assert result == True, "Should return True for successful modification"
        mock_mt5.order_send.assert_called_once()
        print("✅ Test 3 PASSED: Modification attempt made for different values")

async def test_validation_system_improvements():
    """Test the improved validation system"""
    print("\n" + "="*60)
    print("🧪 TESTING: Validation System Improvements")
    print("="*60)
    
    with patch('validation.trade_validator.mt5') as mock_mt5:
        # Setup mock position
        mock_position = MockPosition(
            ticket=12345,
            symbol="EURUSD!",
            sl=1.16168,
            tp=1.16786,
            type_=0
        )
        
        mock_mt5.positions_get.return_value = [mock_position]
        mock_mt5.symbol_info.return_value = MockSymbolInfo(digits=5)
        
        # Create validator
        validator = TradeValidator()
        
        # Test 1: Validation with identical values
        print("\n🔍 Test 1: Validating identical values")
        result = validator.validate_position_modification(
            ticket=12345,
            new_stop_loss=1.16168,  # Same as current
            new_take_profit=1.16786  # Same as current
        )
        
        assert result.result == ValidationResult.VALID, "Should be valid"
        assert result.corrected_params is not None, "Should have corrected params"
        assert result.corrected_params.get("no_changes_needed") == True, "Should indicate no changes needed"
        print("✅ Test 1 PASSED: Validation correctly detected no changes needed")
        
        # Test 2: Validation with different values
        print("\n🔍 Test 2: Validating different values")
        result = validator.validate_position_modification(
            ticket=12345,
            new_stop_loss=1.16100,  # Different
            new_take_profit=1.16800  # Different
        )
        
        assert result.result == ValidationResult.VALID, "Should be valid"
        no_changes = result.corrected_params and result.corrected_params.get("no_changes_needed")
        assert not no_changes, "Should not indicate no changes needed"
        print("✅ Test 2 PASSED: Validation correctly detected changes needed")

async def test_trade_manager_integration():
    """Test the trade manager integration with the fixes"""
    print("\n" + "="*60)
    print("🧪 TESTING: Trade Manager Integration")
    print("="*60)
    
    # Create mocks
    mock_account_manager = Mock()
    mock_account = Mock()
    mock_account.account_id = "test_account"
    
    # Create trade manager
    trade_manager = TradeManager(mock_account_manager)
    
    # Mock the validator to return "no changes needed"
    with patch.object(trade_manager.trade_validator, 'validate_position_modification') as mock_validate:
        mock_validate.return_value = Mock(
            result=ValidationResult.VALID,
            errors=[],
            corrected_params={"no_changes_needed": True}
        )
        
        # Mock MT5 client
        mock_mt5_client = Mock()
        
        # Test position modification with no changes needed
        print("\n🔍 Test: Trade manager handling no changes needed")
        
        position = {"ticket": 12345}
        ai_response = {
            "action": "MODIFY_BOTH",
            "new_stop_loss": 1.16168,
            "new_take_profit": 1.16786
        }
        
        await trade_manager._execute_management_decision(
            mock_account, position, ai_response, mock_mt5_client
        )
        
        # Should not call modify_position on MT5 client
        mock_mt5_client.modify_position.assert_not_called()
        print("✅ Test PASSED: Trade manager correctly skipped modification when no changes needed")

async def test_error_10025_handling():
    """Test handling of MT5 error 10025"""
    print("\n" + "="*60)
    print("🧪 TESTING: Error 10025 Handling")
    print("="*60)
    
    with patch('mt5_integration.mt5_client.mt5') as mock_mt5:
        # Setup mock position
        mock_position = MockPosition(
            ticket=12345,
            symbol="EURUSD!",
            sl=1.16168,
            tp=1.16786,
            type_=0
        )
        
        mock_mt5.positions_get.return_value = [mock_position]
        mock_mt5.symbol_info.return_value = MockSymbolInfo(digits=5)
        
        # Mock order_send to return error 10025
        mock_mt5.order_send.return_value = MockMT5Result(10025, "No changes")
        
        # Create MT5 client
        mt5_client = MT5Client()
        mt5_client.current_account = Mock()
        mt5_client.current_account.account_id = "test_account"
        
        print("\n🔍 Test: Handling MT5 error 10025")
        
        # Force a modification attempt that would trigger error 10025
        # (by bypassing the pre-check somehow)
        with patch.object(mt5_client, '_normalize_price', side_effect=lambda s, p: p):
            result = mt5_client.modify_position(
                ticket=12345,
                stop_loss=1.16200,  # Different value to bypass pre-check
                take_profit=1.16800
            )
        
        # Should return True even with error 10025
        assert result == True, "Should return True for error 10025"
        print("✅ Test PASSED: Error 10025 handled gracefully")

async def main():
    """Run all tests"""
    print("🚀 Starting Position Modification Fixes Tests")
    print("="*80)
    
    try:
        await test_no_changes_detection()
        await test_validation_system_improvements()
        await test_trade_manager_integration()
        await test_error_10025_handling()
        
        print("\n" + "="*80)
        print("✅ ALL TESTS PASSED!")
        print("="*80)
        print("\n🎉 Position modification fixes are working correctly!")
        print("\nKey improvements verified:")
        print("1. ✅ Pre-modification value comparison prevents unnecessary attempts")
        print("2. ✅ Validation system detects when no changes are needed")
        print("3. ✅ Trade manager skips modifications when no changes needed")
        print("4. ✅ Error 10025 'No changes' is handled gracefully")
        print("5. ✅ System no longer retries unnecessarily for identical values")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
