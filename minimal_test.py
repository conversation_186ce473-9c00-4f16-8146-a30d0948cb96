#!/usr/bin/env python3
"""
Minimal test to identify the issue
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

print("🔍 Starting minimal test...")

async def test_async():
    print("✅ Async function working")
    return True

def main():
    print("🚀 Testing basic async functionality...")
    
    try:
        result = asyncio.run(test_async())
        print(f"✅ Async test result: {result}")
        
        print("🔧 Testing imports...")
        
        # Test basic imports one by one
        try:
            from logging_system.logger import setup_logger
            print("✅ Logger import successful")
        except Exception as e:
            print(f"❌ Logger import failed: {e}")
            return False
            
        try:
            from account_management.account_manager import AccountManager
            print("✅ Account manager import successful")
        except Exception as e:
            print(f"❌ Account manager import failed: {e}")
            return False
            
        print("✅ All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 Minimal test completed successfully!")
    else:
        print("❌ Minimal test failed!")
        sys.exit(1)
