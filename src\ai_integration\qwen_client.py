"""
Qwen AI API Client for Trading Decisions
"""

import os
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_ai_provider import BaseAIProvider, AIProviderConfig, AIProviderType
from .response_sanitizer import response_sanitizer
from logging_system.logger import get_logger

logger = get_logger(__name__)

class QwenClient(BaseAIProvider):
    """Client for Qwen AI API integration"""

    def __init__(self, config: Optional[AIProviderConfig] = None):
        # If no config provided, create from environment (backward compatibility)
        if config is None:
            config = self._create_config_from_env()

        super().__init__(config)

        # Use OpenAI-compatible endpoint format
        if not self.config.api_url.endswith('/chat/completions'):
            self.config.api_url = self.config.api_url.rstrip('/') + '/chat/completions'

    def _create_config_from_env(self) -> AIProviderConfig:
        """Create configuration from environment variables (backward compatibility)"""
        api_key = os.getenv('QWEN_API_KEY')
        if not api_key:
            raise ValueError("QWEN_API_KEY environment variable is required")

        return AIProviderConfig(
            provider_type=AIProviderType.QWEN,
            api_key=api_key,
            api_url=os.getenv('QWEN_API_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1'),
            model=os.getenv('AI_MODEL', 'qwen-max-2025-01-25'),
            max_concurrent_requests=int(os.getenv('MAX_CONCURRENT_REQUESTS', '5')),
            timeout=int(os.getenv('AI_TIMEOUT', '30')),
            temperature=float(os.getenv('AI_TEMPERATURE', '0.3')),
            max_tokens=int(os.getenv('AI_MAX_TOKENS', '2000')),
            top_p=float(os.getenv('AI_TOP_P', '0.8'))
        )

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session and not self.session.closed:
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing Qwen session: {e}")
            finally:
                self.session = None
    
    async def generate_trading_decision(
        self,
        prompt: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate trading decision using Qwen AI"""

        async with self.semaphore:
            try:
                headers = {
                    'Authorization': f'Bearer {self.config.api_key}',
                    'Content-Type': 'application/json'
                }

                # Use OpenAI-compatible format for international endpoint
                payload = {
                    'model': self.config.model,
                    'messages': [
                        {
                            'role': 'system',
                            'content': self.get_system_prompt()
                        },
                        {
                            'role': 'user',
                            'content': prompt
                        }
                    ],
                    'temperature': self.config.temperature,
                    'max_tokens': self.config.max_tokens,
                    'top_p': self.config.top_p
                }

                # Ensure session exists and is not closed
                if not self.session or self.session.closed:
                    self.session = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                    )

                async with self.session.post(self.config.api_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return self._parse_ai_response(result, context)
                    else:
                        error_text = await response.text()
                        logger.error(f"Qwen API error {response.status}: {error_text}")
                        return self._get_error_response(f"API Error: {response.status}")
                        
            except asyncio.TimeoutError:
                logger.error("Qwen API request timeout")
                return self._get_error_response("Request timeout")
            except Exception as e:
                logger.error(f"Error calling Qwen API: {e}")
                return self._get_error_response(str(e))
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for trading AI"""
        return """
You are an expert trading AI assistant specializing in forex and financial markets analysis.

Your role is to:
1. Analyze market data and provide specific trading recommendations
2. Generate clear BUY/SELL/HOLD/CLOSE signals with reasoning
3. Provide specific entry prices, stop losses, and take profit levels
4. Assess risk levels and confidence in your recommendations
5. Consider money management strategies in your decisions

Response Format Requirements:
Always respond with a JSON object containing:
{
    "action": "BUY|SELL|HOLD|CLOSE",
    "confidence": 0.0-1.0,
    "entry_price": number or null,
    "stop_loss": number or null,
    "take_profit": number or null,
    "reasoning": "detailed explanation",
    "risk_level": "LOW|MEDIUM|HIGH",
    "additional_signals": [
        {
            "action": "BUY|SELL|HOLD|CLOSE",
            "confidence": 0.0-1.0,
            "entry_price": number or null,
            "stop_loss": number or null,
            "take_profit": number or null,
            "reasoning": "explanation"
        }
    ]
}

Guidelines:
- Be conservative with high-risk strategies like Martingale
- Always provide stop losses for risk management
- Consider market volatility and spread conditions
- Avoid overtrading - quality over quantity
- Respect the money management strategy constraints
- Provide clear, actionable reasoning for each decision
"""
    
    def _parse_ai_response(self, response: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Parse AI response and extract trading signals with robust error handling"""
        try:
            # Extract the AI's response text (OpenAI-compatible format)
            choices = response.get('choices', [])
            if not choices:
                return self._get_error_response("No choices in response")

            message = choices[0].get('message', {})
            text = message.get('content', '')

            if not text:
                return self._get_error_response("Empty response from AI")

            logger.debug(f"🔍 Raw AI response length: {len(text)} characters")

            # Use robust JSON extraction with sanitization
            parsed_response = response_sanitizer.extract_json_from_text(text)

            if parsed_response:
                # Validate required fields
                required_fields = ['action', 'confidence']
                if all(field in parsed_response for field in required_fields):
                    # Add metadata
                    parsed_response['timestamp'] = datetime.now().isoformat()
                    parsed_response['model'] = self.config.model
                    parsed_response['provider'] = self.config.provider_type.value
                    parsed_response['raw_response'] = text

                    if context:
                        parsed_response['context'] = context

                    # Ensure all required fields have default values
                    parsed_response.setdefault('reasoning', 'AI provided trading decision')
                    parsed_response.setdefault('risk_level', 'MEDIUM')
                    parsed_response.setdefault('entry_price', None)
                    parsed_response.setdefault('stop_loss', None)
                    parsed_response.setdefault('take_profit', None)
                    parsed_response.setdefault('take_profit_levels', None)
                    parsed_response.setdefault('market_analysis', 'Market analysis provided')
                    parsed_response.setdefault('strategy_alignment', 'Strategy aligned')
                    parsed_response.setdefault('risk_assessment', 'Risk assessed')
                    parsed_response.setdefault('additional_signals', [])

                    logger.info(f"✅ Successfully parsed AI trading decision: {parsed_response['action']} (confidence: {parsed_response['confidence']:.2%})")
                    return parsed_response
                else:
                    logger.warning(f"⚠️ Missing required fields in AI response, using intelligent fallback")
                    return response_sanitizer.create_intelligent_fallback(text, "Missing required fields")
            else:
                logger.warning("⚠️ Could not extract JSON from AI response, creating intelligent fallback")
                return response_sanitizer.create_intelligent_fallback(text, "JSON extraction failed")

        except Exception as e:
            logger.error(f"❌ Critical error parsing Qwen response: {e}")
            return self._get_error_response(str(e))
    
    def _get_fallback_response(self, text: str) -> Dict[str, Any]:
        """Generate fallback response when parsing fails"""
        return {
            'action': 'HOLD',
            'confidence': 0.0,
            'entry_price': None,
            'stop_loss': None,
            'take_profit': None,
            'reasoning': f"AI response parsing failed. Raw response: {text[:200]}...",
            'risk_level': 'HIGH',
            'timestamp': datetime.now().isoformat(),
            'model': self.config.model,
            'provider': self.config.provider_type.value,
            'error': 'parsing_failed',
            'raw_response': text
        }
    
    async def get_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None
    ) -> str:
        """Get raw completion from Qwen AI"""

        async with self.semaphore:
            try:
                headers = {
                    'Authorization': f'Bearer {self.config.api_key}',
                    'Content-Type': 'application/json'
                }

                messages = []
                if system_prompt:
                    messages.append({
                        'role': 'system',
                        'content': system_prompt
                    })

                messages.append({
                    'role': 'user',
                    'content': prompt
                })

                payload = {
                    'model': self.config.model,
                    'messages': messages,
                    'temperature': self.config.temperature,
                    'max_tokens': self.config.max_tokens,
                    'top_p': self.config.top_p
                }

                # Ensure session exists and is not closed
                if not self.session or self.session.closed:
                    self.session = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                    )

                async with self.session.post(self.config.api_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        choices = result.get('choices', [])
                        if choices:
                            return choices[0].get('message', {}).get('content', '')
                        return ''
                    else:
                        error_text = await response.text()
                        logger.error(f"Qwen completion error {response.status}: {error_text}")
                        return f"Error: {response.status}"

            except Exception as e:
                logger.error(f"Error getting Qwen completion: {e}")
                return f"Error: {str(e)}"

    async def validate_connection(self) -> bool:
        """Validate connection to Qwen API"""
        try:
            # Ensure we have a session
            if not self.session or self.session.closed:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
                )

            test_prompt = "Respond with just 'OK' if you can read this message."
            response = await self.get_completion(test_prompt)

            # Check if response contains OK (case insensitive)
            if response and isinstance(response, str):
                return 'OK' in response.upper() or 'ok' in response.lower()
            return False
        except Exception as e:
            logger.error(f"Qwen connection validation failed: {e}")
            return False

    def get_available_models(self) -> List[str]:
        """Get list of available Qwen models"""
        return [
            'qwen-max-2025-01-25',
            'qwen-max',
            'qwen-plus',
            'qwen-turbo',
            'qwen-long',
            'qwen-coder-plus',
            'qwen-coder-turbo'
        ]
