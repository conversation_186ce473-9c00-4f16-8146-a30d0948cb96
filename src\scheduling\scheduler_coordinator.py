"""
Scheduler Coordinator
Ensures signal generation and trade management never overlap to prevent MT5 session conflicts
"""

import asyncio
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from enum import Enum

from logging_system.logger import get_logger

logger = get_logger(__name__)


class SystemState(Enum):
    """System operation states"""
    IDLE = "IDLE"
    SIGNAL_GENERATION = "SIGNAL_GENERATION"
    TRADE_MANAGEMENT = "TRADE_MANAGEMENT"


class SchedulerCoordinator:
    """Coordinates scheduling to prevent overlapping operations"""
    
    def __init__(self):
        self.current_state = SystemState.IDLE
        self.signal_generation_interval = int(os.getenv('SIGNAL_GENERATION_INTERVAL', '3600'))  # 1 hour
        self.trade_management_interval = int(os.getenv('TRADE_MANAGEMENT_INTERVAL', '5400'))   # 1.5 hours
        
        # Calculate offset to ensure no overlap
        self.trade_management_offset = 30 * 60  # 30 minutes offset
        
        self.last_signal_generation = None
        self.last_trade_management = None
        self.operation_lock = asyncio.Lock()
        
        logger.info(f"🕐 SCHEDULER: Signal generation interval: {self.signal_generation_interval}s ({self.signal_generation_interval/60:.1f} min)")
        logger.info(f"🕐 SCHEDULER: Trade management interval: {self.trade_management_interval}s ({self.trade_management_interval/60:.1f} min)")
        logger.info(f"🕐 SCHEDULER: Trade management offset: {self.trade_management_offset}s ({self.trade_management_offset/60:.1f} min)")
    
    async def can_run_signal_generation(self) -> bool:
        """Check if signal generation can run now"""
        async with self.operation_lock:
            now = datetime.now()
            
            # Check if another operation is running
            if self.current_state != SystemState.IDLE:
                logger.debug(f"🕐 SCHEDULER: Cannot run signal generation - system in state: {self.current_state.value}")
                return False
            
            # Check if enough time has passed since last signal generation
            if self.last_signal_generation:
                time_since_last = (now - self.last_signal_generation).total_seconds()
                if time_since_last < self.signal_generation_interval:
                    remaining = self.signal_generation_interval - time_since_last
                    logger.debug(f"🕐 SCHEDULER: Signal generation too soon - {remaining:.0f}s remaining")
                    return False
            
            # Check if trade management is scheduled to run soon
            if self.last_trade_management:
                next_trade_mgmt = self.last_trade_management + timedelta(seconds=self.trade_management_interval)
                time_to_trade_mgmt = (next_trade_mgmt - now).total_seconds()
                
                # Don't start signal generation if trade management is due within 10 minutes
                if 0 <= time_to_trade_mgmt <= 600:  # 10 minutes buffer
                    logger.debug(f"🕐 SCHEDULER: Signal generation blocked - trade management due in {time_to_trade_mgmt:.0f}s")
                    return False
            
            return True
    
    async def can_run_trade_management(self) -> bool:
        """Check if trade management can run now"""
        async with self.operation_lock:
            now = datetime.now()
            
            # Check if another operation is running
            if self.current_state != SystemState.IDLE:
                logger.debug(f"🕐 SCHEDULER: Cannot run trade management - system in state: {self.current_state.value}")
                return False
            
            # Check if enough time has passed since last trade management
            if self.last_trade_management:
                time_since_last = (now - self.last_trade_management).total_seconds()
                if time_since_last < self.trade_management_interval:
                    remaining = self.trade_management_interval - time_since_last
                    logger.debug(f"🕐 SCHEDULER: Trade management too soon - {remaining:.0f}s remaining")
                    return False
            
            # Check if signal generation is scheduled to run soon
            if self.last_signal_generation:
                next_signal_gen = self.last_signal_generation + timedelta(seconds=self.signal_generation_interval)
                time_to_signal_gen = (next_signal_gen - now).total_seconds()
                
                # Don't start trade management if signal generation is due within 10 minutes
                if 0 <= time_to_signal_gen <= 600:  # 10 minutes buffer
                    logger.debug(f"🕐 SCHEDULER: Trade management blocked - signal generation due in {time_to_signal_gen:.0f}s")
                    return False
            
            # Ensure minimum offset from signal generation
            if self.last_signal_generation:
                time_since_signal = (now - self.last_signal_generation).total_seconds()
                if time_since_signal < self.trade_management_offset:
                    remaining = self.trade_management_offset - time_since_signal
                    logger.debug(f"🕐 SCHEDULER: Trade management blocked - need {remaining:.0f}s offset from signal generation")
                    return False
            
            return True
    
    async def start_signal_generation(self) -> bool:
        """Mark signal generation as starting"""
        async with self.operation_lock:
            if self.current_state != SystemState.IDLE:
                logger.warning(f"🕐 SCHEDULER: Cannot start signal generation - system busy: {self.current_state.value}")
                return False
            
            self.current_state = SystemState.SIGNAL_GENERATION
            self.last_signal_generation = datetime.now()
            logger.info(f"🕐 SCHEDULER: Signal generation started at {self.last_signal_generation.strftime('%H:%M:%S')}")
            return True
    
    async def start_trade_management(self) -> bool:
        """Mark trade management as starting"""
        async with self.operation_lock:
            if self.current_state != SystemState.IDLE:
                logger.warning(f"🕐 SCHEDULER: Cannot start trade management - system busy: {self.current_state.value}")
                return False
            
            self.current_state = SystemState.TRADE_MANAGEMENT
            self.last_trade_management = datetime.now()
            logger.info(f"🕐 SCHEDULER: Trade management started at {self.last_trade_management.strftime('%H:%M:%S')}")
            return True
    
    async def finish_signal_generation(self):
        """Mark signal generation as finished"""
        async with self.operation_lock:
            if self.current_state == SystemState.SIGNAL_GENERATION:
                self.current_state = SystemState.IDLE
                logger.info(f"🕐 SCHEDULER: Signal generation finished at {datetime.now().strftime('%H:%M:%S')}")
            else:
                logger.warning(f"🕐 SCHEDULER: Unexpected state when finishing signal generation: {self.current_state.value}")
    
    async def finish_trade_management(self):
        """Mark trade management as finished"""
        async with self.operation_lock:
            if self.current_state == SystemState.TRADE_MANAGEMENT:
                self.current_state = SystemState.IDLE
                logger.info(f"🕐 SCHEDULER: Trade management finished at {datetime.now().strftime('%H:%M:%S')}")
            else:
                logger.warning(f"🕐 SCHEDULER: Unexpected state when finishing trade management: {self.current_state.value}")
    
    def get_next_signal_generation_time(self) -> Optional[datetime]:
        """Get the next scheduled signal generation time"""
        if self.last_signal_generation:
            return self.last_signal_generation + timedelta(seconds=self.signal_generation_interval)
        return datetime.now()
    
    def get_next_trade_management_time(self) -> Optional[datetime]:
        """Get the next scheduled trade management time"""
        if self.last_trade_management:
            return self.last_trade_management + timedelta(seconds=self.trade_management_interval)
        # If never run, start with offset from signal generation
        if self.last_signal_generation:
            return self.last_signal_generation + timedelta(seconds=self.trade_management_offset)
        return datetime.now() + timedelta(seconds=self.trade_management_offset)
    
    def get_status(self) -> dict:
        """Get current scheduler status"""
        now = datetime.now()
        
        next_signal = self.get_next_signal_generation_time()
        next_trade = self.get_next_trade_management_time()
        
        return {
            'current_state': self.current_state.value,
            'last_signal_generation': self.last_signal_generation.isoformat() if self.last_signal_generation else None,
            'last_trade_management': self.last_trade_management.isoformat() if self.last_trade_management else None,
            'next_signal_generation': next_signal.isoformat() if next_signal else None,
            'next_trade_management': next_trade.isoformat() if next_trade else None,
            'time_to_next_signal': (next_signal - now).total_seconds() if next_signal else None,
            'time_to_next_trade': (next_trade - now).total_seconds() if next_trade else None
        }

    async def reset(self):
        """Reset scheduler state for graceful shutdown"""
        async with self.operation_lock:
            logger.info("🔄 SCHEDULER: Resetting scheduler state for shutdown")
            self.current_state = SystemState.IDLE
            logger.info("✅ SCHEDULER: State reset complete")

    async def shutdown(self):
        """Shutdown scheduler coordinator gracefully"""
        await self.reset()
        logger.info("🛑 SCHEDULER: Scheduler coordinator shutdown complete")


# Global scheduler coordinator instance
scheduler_coordinator = SchedulerCoordinator()
