#!/usr/bin/env python3
"""
Run trade management once to get AI decisions for current trades and pending orders
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from trade_management.trade_manager import TradeManager
from account_management.account_manager import AccountManager
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

async def run_trade_management_analysis():
    """Run trade management analysis once"""
    print("🚀 RUNNING TRADE MANAGEMENT ANALYSIS")
    print("=" * 60)
    
    try:
        # Initialize account manager
        account_manager = AccountManager()
        
        # Initialize trade manager
        trade_manager = TradeManager(account_manager)
        
        print("✅ Trade manager initialized")
        
        # Initialize MT5
        if not trade_manager.mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        print("✅ MT5 initialized")
        
        # Get accounts
        accounts = account_manager.get_all_accounts()
        if not accounts:
            print("❌ No accounts found")
            return False
        
        print(f"📊 Found {len(accounts)} accounts")
        
        # Run trade management for all accounts
        print("\n🔄 Starting trade management analysis...")
        await trade_manager.manage_trades()
        
        print("\n✅ Trade management analysis completed")
        return True
        
    except Exception as e:
        print(f"❌ Error during trade management: {e}")
        logger.error(f"Trade management error: {e}", exc_info=True)
        return False
        
    finally:
        # Cleanup
        if 'trade_manager' in locals():
            trade_manager.mt5_client.shutdown()
        print("🔒 MT5 connection closed")

async def get_current_positions_and_orders():
    """Get current positions and orders for summary"""
    print("\n📊 CURRENT TRADING STATUS")
    print("=" * 60)
    
    from mt5_integration.mt5_client import MT5Client
    from account_management.models import TradingAccount
    
    mt5_client = MT5Client()
    
    try:
        if not mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return
        
        # Create account object (hardcoded for demo)
        account = TradingAccount(
            account_id="demo1",
            account_number=********,
            server="RoboForex-ECN",
            username="********",
            password="Daadb123",
            strategy_type="scalping",
            money_management_type="fixed_lot",
            symbols=["EURUSD", "GBPUSD", "USDJPY"],
            timeframes=["M15"]
        )
        
        if not mt5_client.login(account):
            print("❌ Failed to login")
            return
        
        # Get current positions
        positions = mt5_client.get_positions()
        print(f"\n📈 OPEN POSITIONS ({len(positions)}):")
        
        if positions:
            for i, pos in enumerate(positions, 1):
                profit_status = "🟢" if pos['profit'] > 0 else "🔴" if pos['profit'] < 0 else "⚪"
                print(f"   {i}. {pos['symbol']} {pos['type']} {pos['volume']} @ {pos['price_open']:.5f}")
                print(f"      Current: {pos.get('price_current', 'N/A')} | P&L: {profit_status} ${pos['profit']:.2f}")
                print(f"      SL: {pos.get('sl', 0):.5f} | TP: {pos.get('tp', 0):.5f}")
        else:
            print("   No open positions")
        
        # Get pending orders
        orders = mt5_client.get_pending_orders()
        print(f"\n📋 PENDING ORDERS ({len(orders)}):")
        
        if orders:
            order_types = {2: "BUY_LIMIT", 3: "SELL_LIMIT", 4: "BUY_STOP", 5: "SELL_STOP"}
            for i, order in enumerate(orders, 1):
                order_type = order_types.get(order['type'], f"TYPE_{order['type']}")
                print(f"   {i}. {order['symbol']} {order_type} {order['volume']} @ {order['price_open']:.5f}")
                print(f"      SL: {order.get('sl', 0):.5f} | TP: {order.get('tp', 0):.5f}")
                print(f"      Ticket: {order['ticket']}")
        else:
            print("   No pending orders")
        
        # Get account info
        account_info = mt5_client.get_account_info()
        if account_info:
            print(f"\n💰 ACCOUNT STATUS:")
            print(f"   Balance: ${account_info.balance:.2f}")
            print(f"   Equity: ${account_info.equity:.2f}")
            print(f"   Margin Level: {account_info.margin_level:.1f}%")
            print(f"   Free Margin: ${account_info.free_margin:.2f}")
        
    except Exception as e:
        print(f"❌ Error getting trading status: {e}")
        
    finally:
        mt5_client.shutdown()

async def main():
    """Main function"""
    # First show current status
    await get_current_positions_and_orders()
    
    # Then run trade management analysis
    success = await run_trade_management_analysis()
    
    if success:
        print("\n🎉 TRADE MANAGEMENT ANALYSIS COMPLETED")
        print("\nCheck the logs for detailed AI decisions on each position and pending order.")
        print("The AI analyzed market conditions and made recommendations for:")
        print("- Position management (hold, close, modify SL/TP)")
        print("- Pending order management (keep, cancel, modify price/SL/TP)")
    else:
        print("\n❌ TRADE MANAGEMENT ANALYSIS FAILED")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
