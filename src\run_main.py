
#!/usr/bin/env python3
"""
DEPRECATED: AI-Driven Trading System Entry Point (src/run_main.py)
This entry point has been consolidated into trading_system_main.py

Please use: python trading_system_main.py (from root directory)
"""

import sys
from pathlib import Path

def main():
    """Redirect to the unified entry point"""
    print("❌ DEPRECATED ENTRY POINT")
    print("=" * 50)
    print("This entry point (src/run_main.py) has been deprecated.")
    print("All entry points have been consolidated for consistency.")
    print()
    print("✅ Please use the unified entry point from root directory:")
    print("   cd ..")
    print("   python trading_system_main.py")
    print()
    print("🔧 This consolidation fixes architectural inconsistencies")
    print("   and ensures reliable system initialization.")
    print("=" * 50)

    # Check if the unified entry point exists
    unified_entry = Path(__file__).parent.parent / "trading_system_main.py"
    if unified_entry.exists():
        print("✅ Unified entry point found: ../trading_system_main.py")
    else:
        print("❌ Unified entry point not found!")

    sys.exit(1)

if __name__ == "__main__":
    main()
