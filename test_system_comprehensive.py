#!/usr/bin/env python3
"""
Comprehensive System Test
Tests all the fixes implemented for the trading system
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append('src')

from account_management.account_manager import AccountManager
from signal_generation.signal_generator import SignalGenerator
from mt5_integration.mt5_client import MT5<PERSON>lient
from logging_system.logger import get_logger

logger = get_logger(__name__)

class SystemTester:
    def __init__(self):
        self.account_manager = AccountManager()
        self.mt5_client = MT5Client()
        self.test_results = {}
        
    async def run_comprehensive_test(self):
        """Run comprehensive system test"""
        print("🧪 COMPREHENSIVE SYSTEM TEST")
        print("=" * 60)
        print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Test 1: Account Configuration Loading
        await self.test_account_configuration()
        
        # Test 2: Money Management Settings Validation
        await self.test_money_management_validation()
        
        # Test 3: Daily Limits Enforcement
        await self.test_daily_limits()
        
        # Test 4: Position Limits Checking
        await self.test_position_limits()
        
        # Test 5: Balance Threshold for Multiple TP
        await self.test_balance_threshold()
        
        # Test 6: MT5 Connection and Market Data
        await self.test_mt5_connection()
        
        # Test 7: Strategy Validation
        await self.test_strategy_validation()
        
        # Print summary
        self.print_test_summary()
        
    async def test_account_configuration(self):
        """Test account configuration loading"""
        print("📋 TEST 1: Account Configuration Loading")
        print("-" * 40)
        
        try:
            # Load accounts
            success = self.account_manager.load_accounts()
            if not success:
                self.test_results['account_loading'] = False
                print("❌ Failed to load accounts")
                return
            
            accounts = self.account_manager.get_all_accounts()
            if not accounts:
                self.test_results['account_loading'] = False
                print("❌ No accounts loaded")
                return
            
            print(f"✅ Loaded {len(accounts)} accounts")
            
            # Check demo account is present
            demo_account = None
            for account in accounts:
                if account.account_number == ********:
                    demo_account = account
                    break
            
            if demo_account:
                print(f"✅ Demo account found: {demo_account.account_id}")
                print(f"   Account: {demo_account.account_number}")
                print(f"   Server: {demo_account.server}")
                print(f"   Strategy: {demo_account.strategy_type}")
                print(f"   Money Management: {demo_account.money_management_type}")
                self.test_results['account_loading'] = True
            else:
                print("❌ Demo account (********) not found")
                self.test_results['account_loading'] = False
                
        except Exception as e:
            print(f"❌ Error loading accounts: {e}")
            self.test_results['account_loading'] = False
        
        print()
    
    async def test_money_management_validation(self):
        """Test money management settings validation"""
        print("💰 TEST 2: Money Management Settings Validation")
        print("-" * 40)
        
        try:
            accounts = self.account_manager.get_all_accounts()
            if not accounts:
                print("❌ No accounts to test")
                self.test_results['money_management'] = False
                return
            
            signal_gen = SignalGenerator(self.account_manager)
            
            for account in accounts[:1]:  # Test first account
                account_dict = {
                    'account_id': account.account_id,
                    'money_management_settings': account.money_management_config
                }
                
                try:
                    risk_settings = signal_gen._get_account_risk_settings(account_dict)
                    
                    required_settings = ['max_daily_trades', 'max_open_positions', 'max_pending_orders']
                    missing = [s for s in required_settings if s not in risk_settings]
                    
                    if missing:
                        print(f"❌ Account {account.account_id} missing settings: {missing}")
                        self.test_results['money_management'] = False
                        return
                    else:
                        print(f"✅ Account {account.account_id} has all required settings")
                        print(f"   Max daily trades: {risk_settings['max_daily_trades']}")
                        print(f"   Max open positions: {risk_settings['max_open_positions']}")
                        print(f"   Max pending orders: {risk_settings['max_pending_orders']}")
                        
                except Exception as e:
                    print(f"❌ Error validating {account.account_id}: {e}")
                    self.test_results['money_management'] = False
                    return
            
            self.test_results['money_management'] = True
            
        except Exception as e:
            print(f"❌ Error in money management test: {e}")
            self.test_results['money_management'] = False
        
        print()
    
    async def test_daily_limits(self):
        """Test daily limits enforcement"""
        print("📊 TEST 3: Daily Limits Enforcement")
        print("-" * 40)
        
        try:
            signal_gen = SignalGenerator(self.account_manager)
            
            # Create test account
            test_account = {
                'account_id': 'test_daily_limits',
                'money_management_settings': {
                    'max_daily_trades': 2,
                    'max_open_positions': 1,
                    'max_pending_orders': 1,
                    'max_daily_loss_percent': 2.0
                }
            }
            
            # Test initial state (should allow trading)
            can_trade_initial = signal_gen._check_risk_limits(test_account)
            print(f"✅ Initial state allows trading: {can_trade_initial}")
            
            # Simulate reaching daily trade limit
            today = datetime.now().date()
            daily_key = f"test_daily_limits_{today}"
            signal_gen.daily_trades[daily_key] = 2  # Set to limit
            
            can_trade_at_limit = signal_gen._check_risk_limits(test_account)
            print(f"✅ At limit blocks trading: {not can_trade_at_limit}")
            
            if can_trade_initial and not can_trade_at_limit:
                self.test_results['daily_limits'] = True
            else:
                self.test_results['daily_limits'] = False
                
        except Exception as e:
            print(f"❌ Error in daily limits test: {e}")
            self.test_results['daily_limits'] = False
        
        print()
    
    async def test_position_limits(self):
        """Test position limits checking"""
        print("📈 TEST 4: Position Limits Checking")
        print("-" * 40)
        
        try:
            # This test would require mock MT5 data
            # For now, just test the logic exists
            signal_gen = SignalGenerator(self.account_manager)
            
            test_account = {
                'account_id': 'test_position_limits',
                'money_management_settings': {
                    'max_daily_trades': 5,
                    'max_open_positions': 2,
                    'max_pending_orders': 3
                }
            }
            
            # Test that the method exists and can be called
            risk_settings = signal_gen._get_account_risk_settings(test_account)
            print(f"✅ Position limits method accessible")
            print(f"   Max open positions: {risk_settings['max_open_positions']}")
            print(f"   Max pending orders: {risk_settings['max_pending_orders']}")
            
            self.test_results['position_limits'] = True
            
        except Exception as e:
            print(f"❌ Error in position limits test: {e}")
            self.test_results['position_limits'] = False
        
        print()
    
    async def test_balance_threshold(self):
        """Test balance threshold for multiple TP"""
        print("💵 TEST 5: Balance Threshold for Multiple TP")
        print("-" * 40)
        
        try:
            # Test the balance threshold logic
            balance_threshold = 100.0
            
            test_cases = [
                (50.0, True),   # Below threshold, should force single TP
                (150.0, False)  # Above threshold, should allow multiple TP
            ]
            
            for balance, should_force_single in test_cases:
                force_single_tp = balance < balance_threshold
                result = force_single_tp == should_force_single
                
                print(f"✅ Balance ${balance}: Force single TP = {force_single_tp} (Expected: {should_force_single})")
                
                if not result:
                    self.test_results['balance_threshold'] = False
                    return
            
            self.test_results['balance_threshold'] = True
            
        except Exception as e:
            print(f"❌ Error in balance threshold test: {e}")
            self.test_results['balance_threshold'] = False
        
        print()
    
    async def test_mt5_connection(self):
        """Test MT5 connection and market data"""
        print("🔌 TEST 6: MT5 Connection and Market Data")
        print("-" * 40)
        
        try:
            # Initialize MT5
            if not self.mt5_client.initialize():
                print("❌ Failed to initialize MT5")
                self.test_results['mt5_connection'] = False
                return
            
            print("✅ MT5 initialized successfully")
            
            # Try to get demo account
            accounts = self.account_manager.get_all_accounts()
            demo_account = None
            for account in accounts:
                if account.account_number == ********:
                    demo_account = account
                    break
            
            if not demo_account:
                print("❌ Demo account not found")
                self.test_results['mt5_connection'] = False
                return
            
            # Try to login
            login_success = self.mt5_client.login(demo_account)
            if login_success:
                print(f"✅ Successfully logged into demo account")
                
                # Try to get market data
                market_data = self.mt5_client.get_market_data("EURUSD", "M15", 50)
                if market_data:
                    print(f"✅ Market data retrieved: {len(market_data.get('candles', []))} candles")
                    print(f"   Current price: {market_data.get('current_price', 'N/A')}")
                    print(f"   Spread: {market_data.get('spread', 'N/A')} pips")
                    self.test_results['mt5_connection'] = True
                else:
                    print("❌ Failed to get market data")
                    self.test_results['mt5_connection'] = False
            else:
                print("❌ Failed to login to demo account")
                self.test_results['mt5_connection'] = False
                
        except Exception as e:
            print(f"❌ Error in MT5 connection test: {e}")
            self.test_results['mt5_connection'] = False
        finally:
            self.mt5_client.shutdown()
        
        print()
    
    async def test_strategy_validation(self):
        """Test strategy validation improvements"""
        print("🎯 TEST 7: Strategy Validation")
        print("-" * 40)
        
        try:
            from strategies.trend_following import TrendFollowingStrategy
            from strategies.mean_reversion import MeanReversionStrategy
            from strategies.base_strategy import TradingSignal, MarketData
            
            # Test trend following validation
            tf_strategy = TrendFollowingStrategy({})
            
            # Create test signal and market data
            test_signal = TradingSignal(
                action="BUY",
                entry_price=1.1000,
                stop_loss=1.0950,  # 50 pips risk
                take_profit=1.1150,  # 150 pips reward (1:3 RR)
                confidence=0.85,
                reasoning="Test signal",
                risk_level="MEDIUM"
            )
            
            test_market_data = MarketData(
                symbol="EURUSD",
                timeframe="M15",
                current_price=1.1000,
                spread=1.0,
                pip_size=0.0001,
                candles=[]
            )
            
            # Test validation
            is_valid = tf_strategy.validate_signal(test_signal, test_market_data)
            print(f"✅ Trend following validation: {is_valid}")
            
            # Test mean reversion validation
            mr_strategy = MeanReversionStrategy({})
            
            mr_signal = TradingSignal(
                action="SELL",
                entry_price=1.1000,
                stop_loss=1.1010,  # 10 pips risk
                take_profit=1.0985,  # 15 pips reward (1:1.5 RR)
                confidence=0.90,
                reasoning="Test mean reversion signal",
                risk_level="LOW"
            )
            
            is_valid_mr = mr_strategy.validate_signal(mr_signal, test_market_data)
            print(f"✅ Mean reversion validation: {is_valid_mr}")
            
            self.test_results['strategy_validation'] = is_valid and is_valid_mr
            
        except Exception as e:
            print(f"❌ Error in strategy validation test: {e}")
            self.test_results['strategy_validation'] = False
        
        print()
    
    def print_test_summary(self):
        """Print test summary"""
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print()
        print(f"Overall Result: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED - System is ready for trading!")
        else:
            print("⚠️  Some tests failed - Review issues before trading")
        
        print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

async def main():
    """Main test function"""
    tester = SystemTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
