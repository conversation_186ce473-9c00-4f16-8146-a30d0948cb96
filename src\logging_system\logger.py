"""
Comprehensive Logging System for Trading Application
"""

import os
import sys
from pathlib import Path
from loguru import logger
from datetime import datetime
from typing import Dict, Any, Optional, List

# Configure loguru logger
def setup_logger(name: Optional[str] = None, log_file: Optional[str] = None):
    """Setup comprehensive logging configuration"""
    
    # Remove default handler
    logger.remove()
    
    # Get configuration from environment
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file_path = os.getenv('LOG_FILE_PATH', 'logs/trading_system.log')
    log_rotation = os.getenv('LOG_ROTATION', '1 day')
    log_retention = os.getenv('LOG_RETENTION', '30 days')
    
    # Ensure log directory exists
    log_dir = Path(log_file_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Console handler with colors
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # File handler for general logs
    logger.add(
        log_file_path,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation=log_rotation,
        retention=log_retention,
        compression="zip"
    )
    
    # Separate file for trade-specific logs
    trade_log_path = log_dir / "trades.log"
    logger.add(
        trade_log_path,
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        rotation=log_rotation,
        retention=log_retention,
        compression="zip",
        filter=lambda record: "TRADE" in record["extra"]
    )
    
    # Separate file for AI decision logs
    ai_log_path = log_dir / "ai_decisions.log"
    logger.add(
        ai_log_path,
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        rotation=log_rotation,
        retention=log_retention,
        compression="zip",
        filter=lambda record: "AI" in record["extra"]
    )
    
    # Error log file
    error_log_path = log_dir / "errors.log"
    logger.add(
        error_log_path,
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}\n{exception}",
        rotation=log_rotation,
        retention=log_retention,
        compression="zip"
    )
    
    logger.info("Logging system initialized")
    return logger

def get_logger(name: Optional[str] = None):
    """Get logger instance"""
    return logger.bind(name=name) if name else logger

class TradingLogger:
    """Specialized logger for trading operations"""
    
    def __init__(self):
        self.logger = get_logger("TradingLogger")
    
    def log_trade_signal(
        self, 
        account_id: str, 
        symbol: str, 
        signal: Dict[str, Any],
        strategy: str = None,
        money_management: str = None
    ) -> None:
        """Log trading signal generation"""
        
        message = f"SIGNAL | Account: {account_id} | Symbol: {symbol} | Action: {signal.get('action')} | Confidence: {signal.get('confidence', 0):.2f}"
        
        if strategy:
            message += f" | Strategy: {strategy}"
        if money_management:
            message += f" | MM: {money_management}"
        
        self.logger.bind(TRADE=True).info(message)
    
    def log_trade_execution(
        self,
        account_id: str,
        symbol: str,
        action: str,
        volume: float,
        price: float,
        order_id: Optional[int] = None,
        success: bool = True,
        error: str = None
    ) -> None:
        """Log trade execution"""
        
        status = "SUCCESS" if success else "FAILED"
        message = f"EXECUTION | {status} | Account: {account_id} | {action} {volume} {symbol} @ {price}"
        
        if order_id:
            message += f" | Order ID: {order_id}"
        
        if error:
            message += f" | Error: {error}"
        
        if success:
            self.logger.bind(TRADE=True).info(message)
        else:
            self.logger.bind(TRADE=True).error(message)
    
    def log_trade_modification(
        self,
        account_id: str,
        order_id: int,
        modification_type: str,
        old_value: float,
        new_value: float,
        success: bool = True,
        error: str = None
    ) -> None:
        """Log trade modification (SL/TP changes)"""
        
        status = "SUCCESS" if success else "FAILED"
        message = f"MODIFICATION | {status} | Account: {account_id} | Order: {order_id} | {modification_type}: {old_value} -> {new_value}"
        
        if error:
            message += f" | Error: {error}"
        
        if success:
            self.logger.bind(TRADE=True).info(message)
        else:
            self.logger.bind(TRADE=True).error(message)
    
    def log_trade_close(
        self,
        account_id: str,
        order_id: int,
        symbol: str,
        volume: float,
        entry_price: float,
        exit_price: float,
        profit: float,
        reason: str = "Manual"
    ) -> None:
        """Log trade closure"""
        
        message = f"CLOSE | Account: {account_id} | Order: {order_id} | {symbol} | Volume: {volume} | Entry: {entry_price} | Exit: {exit_price} | Profit: ${profit:.2f} | Reason: {reason}"
        
        self.logger.bind(TRADE=True).info(message)
    
    def log_ai_decision(
        self,
        account_id: str,
        symbol: str,
        strategy: str,
        prompt_length: int,
        response: Dict[str, Any],
        processing_time: float
    ) -> None:
        """Log AI decision making"""
        
        message = f"AI_DECISION | Account: {account_id} | Symbol: {symbol} | Strategy: {strategy} | Prompt Length: {prompt_length} chars | Processing Time: {processing_time:.2f}s | Action: {response.get('action')} | Confidence: {response.get('confidence', 0):.2f}"
        
        self.logger.bind(AI=True).info(message)
    
    def log_ai_error(
        self,
        account_id: str,
        symbol: str,
        error: str,
        prompt_length: int = None
    ) -> None:
        """Log AI processing errors"""
        
        message = f"AI_ERROR | Account: {account_id} | Symbol: {symbol} | Error: {error}"
        
        if prompt_length:
            message += f" | Prompt Length: {prompt_length} chars"
        
        self.logger.bind(AI=True).error(message)
    
    def log_account_status(
        self,
        account_id: str,
        status: str,
        balance: float = None,
        equity: float = None,
        margin_level: float = None
    ) -> None:
        """Log account status changes"""
        
        message = f"ACCOUNT_STATUS | Account: {account_id} | Status: {status}"
        
        if balance is not None:
            message += f" | Balance: ${balance:.2f}"
        if equity is not None:
            message += f" | Equity: ${equity:.2f}"
        if margin_level is not None:
            message += f" | Margin Level: {margin_level:.1f}%"
        
        self.logger.info(message)
    
    def log_system_event(
        self,
        event_type: str,
        description: str,
        severity: str = "INFO"
    ) -> None:
        """Log system events"""

        message = f"SYSTEM | {event_type} | {description}"

        if severity.upper() == "ERROR":
            self.logger.error(message)
        elif severity.upper() == "WARNING":
            self.logger.warning(message)
        else:
            self.logger.info(message)

    def log_market_data_retrieval(
        self,
        account_id: str,
        symbol: str,
        timeframe: str,
        candles_count: int,
        success: bool = True,
        error: str = None,
        processing_time: float = None
    ) -> None:
        """Log market data retrieval operations"""

        status = "SUCCESS" if success else "FAILED"
        message = f"MARKET_DATA | {status} | Account: {account_id} | Symbol: {symbol} | Timeframe: {timeframe} | Candles: {candles_count}"

        if processing_time:
            message += f" | Time: {processing_time:.3f}s"

        if error:
            message += f" | Error: {error}"

        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)

    def log_position_monitoring(
        self,
        account_id: str,
        symbol: str,
        position_id: int,
        current_price: float,
        unrealized_pnl: float,
        action_taken: str = None
    ) -> None:
        """Log position monitoring activities"""

        message = f"POSITION_MONITOR | Account: {account_id} | Symbol: {symbol} | Position: {position_id} | Price: {current_price} | PnL: ${unrealized_pnl:.2f}"

        if action_taken:
            message += f" | Action: {action_taken}"

        self.logger.bind(TRADE=True).info(message)

    def log_signal_validation(
        self,
        account_id: str,
        symbol: str,
        signal: Dict[str, Any],
        validation_result: bool,
        validation_errors: List[str] = None
    ) -> None:
        """Log signal validation results"""

        status = "VALID" if validation_result else "INVALID"
        message = f"SIGNAL_VALIDATION | {status} | Account: {account_id} | Symbol: {symbol} | Action: {signal.get('action')}"

        if validation_errors:
            message += f" | Errors: {', '.join(validation_errors)}"

        if validation_result:
            self.logger.bind(TRADE=True).info(message)
        else:
            self.logger.bind(TRADE=True).warning(message)

    def log_money_management_calculation(
        self,
        account_id: str,
        symbol: str,
        strategy_type: str,
        entry_price: float,
        stop_loss: float,
        calculated_volume: float,
        risk_amount: float,
        risk_percent: float = None
    ) -> None:
        """Log money management calculations"""

        message = f"MONEY_MGMT | Account: {account_id} | Symbol: {symbol} | Strategy: {strategy_type} | Entry: {entry_price} | SL: {stop_loss} | Volume: {calculated_volume} | Risk: ${risk_amount:.2f}"

        if risk_percent:
            message += f" | Risk%: {risk_percent:.2f}%"

        self.logger.bind(TRADE=True).info(message)

    def log_account_balance_update(
        self,
        account_id: str,
        old_balance: float,
        new_balance: float,
        old_equity: float,
        new_equity: float,
        change_reason: str = "Trade"
    ) -> None:
        """Log account balance changes"""

        balance_change = new_balance - old_balance
        equity_change = new_equity - old_equity

        message = f"BALANCE_UPDATE | Account: {account_id} | Balance: ${old_balance:.2f} -> ${new_balance:.2f} ({balance_change:+.2f}) | Equity: ${old_equity:.2f} -> ${new_equity:.2f} ({equity_change:+.2f}) | Reason: {change_reason}"

        self.logger.bind(TRADE=True).info(message)

    def log_pending_order(
        self,
        account_id: str,
        symbol: str,
        order_type: str,
        volume: float,
        price: float,
        stop_loss: float = None,
        take_profit: float = None,
        order_id: int = None,
        success: bool = True,
        error: str = None
    ) -> None:
        """Log pending order placement"""

        status = "SUCCESS" if success else "FAILED"
        message = f"PENDING_ORDER | {status} | Account: {account_id} | {order_type} {volume} {symbol} @ {price}"

        if stop_loss:
            message += f" | SL: {stop_loss}"
        if take_profit:
            message += f" | TP: {take_profit}"
        if order_id:
            message += f" | Order ID: {order_id}"
        if error:
            message += f" | Error: {error}"

        if success:
            self.logger.bind(TRADE=True).info(message)
        else:
            self.logger.bind(TRADE=True).error(message)

    def log_order_cancellation(
        self,
        account_id: str,
        order_id: int,
        symbol: str,
        reason: str,
        success: bool = True,
        error: str = None
    ) -> None:
        """Log order cancellation"""

        status = "SUCCESS" if success else "FAILED"
        message = f"ORDER_CANCEL | {status} | Account: {account_id} | Order: {order_id} | Symbol: {symbol} | Reason: {reason}"

        if error:
            message += f" | Error: {error}"

        if success:
            self.logger.bind(TRADE=True).info(message)
        else:
            self.logger.bind(TRADE=True).error(message)

    def log_session_management(
        self,
        account_id: str,
        action: str,
        success: bool = True,
        details: str = None,
        error: str = None
    ) -> None:
        """Log MT5 session management activities"""

        status = "SUCCESS" if success else "FAILED"
        message = f"SESSION | {status} | Account: {account_id} | Action: {action}"

        if details:
            message += f" | Details: {details}"
        if error:
            message += f" | Error: {error}"

        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)

    def log_strategy_performance(
        self,
        account_id: str,
        strategy_type: str,
        symbol: str,
        timeframe: str,
        total_trades: int,
        winning_trades: int,
        total_profit: float,
        win_rate: float,
        profit_factor: float = None
    ) -> None:
        """Log strategy performance metrics"""

        message = f"STRATEGY_PERF | Account: {account_id} | Strategy: {strategy_type} | Symbol: {symbol} | TF: {timeframe} | Trades: {total_trades} | Wins: {winning_trades} | Profit: ${total_profit:.2f} | WinRate: {win_rate:.1f}%"

        if profit_factor:
            message += f" | PF: {profit_factor:.2f}"

        self.logger.bind(TRADE=True).info(message)

# Global trading logger instance
trading_logger = TradingLogger()
