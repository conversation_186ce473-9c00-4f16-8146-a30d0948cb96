ress Ctrl+C to stop the system

🤖 AI-Driven Trading System
==================================================
2025-08-06 10:57:37 | INFO     | logging_system.logger:setup_logger:82 - Logging system initialized
2025-08-06 10:57:37 | INFO     | __main__:initialize:41 - 🚀 AI-Driven Trading System Initializing...
2025-08-06 10:57:37 | INFO     | __main__:initialize:47 - 📊 Initializing Account Manager...
2025-08-06 10:57:37 | INFO     | account_management.account_manager:load_accounts:96 - Loaded 4 accounts and 0 groups
2025-08-06 10:57:37 | INFO     | __main__:initialize:54 - ✅ Loaded 4 trading accounts
2025-08-06 10:57:37 | INFO     | __main__:initialize:58 -    Account: Mine | Strategy: trend_following | MM: percent_risk
2025-08-06 10:57:37 | INFO     | __main__:initialize:58 -    Account: Customer 1 | Strategy: trend_following | MM: percent_risk
2025-08-06 10:57:37 | INFO     | __main__:initialize:58 -    Account: fixed_volume_scalper | Strategy: mean_reversion | MM: fixed_volume
2025-08-06 10:57:37 | INFO     | __main__:initialize:58 -    Account: martingale_trader | Strategy: mean_reversion | MM: martingale
2025-08-06 10:57:37 | INFO     | __main__:initialize:61 - 🎯 Initializing Signal Generator...
2025-08-06 10:57:37 | INFO     | __main__:initialize:65 - 📈 Initializing Trade Manager...
2025-08-06 10:57:37 | INFO     | __main__:initialize:68 - ✅ All components initialized successfully
2025-08-06 10:57:37 | INFO     | __main__:start:84 - 🎯 Starting Trading Operations...
2025-08-06 10:57:37 | INFO     | __main__:start:85 - ============================================================
2025-08-06 10:57:37 | INFO     | __main__:run_signal_generation:103 - 🔄 Signal generation started
2025-08-06 10:57:37 | INFO     | scheduling.scheduler_coordinator:start_signal_generation:120 - 🕐 SCHEDULER: Signal generation started at 10:57:37
2025-08-06 10:57:37 | INFO     | __main__:run_signal_generation:112 - 📡 Generating trading signals...
2025-08-06 10:57:37 | INFO     | signal_generation.signal_generator:_is_market_open:1485 - Weekend trading enabled - allowing signal generation
2025-08-06 10:57:37 | INFO     | signal_generation.signal_generator:generate_signals:111 - Starting signal generation cycle...
2025-08-06 10:57:37 | INFO     | signal_generation.signal_generator:generate_signals:118 - No account groups found, creating virtual groups from individual accounts
2025-08-06 10:57:37 | INFO     | signal_generation.signal_generator:_create_virtual_groups:160 - Created 3 virtual groups from individual accounts
2025-08-06 10:57:37 | INFO     | signal_generation.signal_generator:_process_account_group:170 - Processing group: trend_following/percent_risk with 2 accounts
2025-08-06 10:57:37 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:208 - Processing account Mine with individual settings: {'_comment': 'OPTIMIZED FOR $80 ACCOUNT - Conservative settings for profitability', 'risk_percent': 0.5, 'max_daily_loss_percent': 2.0, 'max_daily_trades': 3, 'max_open_positions': 1, 'max_pending_orders': 2, 'max_volume_per_trade': 0.01, 'force_min_volume': False, '_risk_controls': {'max_risk_per_trade_percent': 1.0, 'max_risk_multiplier': 1.5, 'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}}
2025-08-06 10:57:37 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD_M15_trend_following_percent_risk_Mine
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:login:111 - 🔍 LOGIN_DEBUG: Starting login process for account Mine (********)
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:login:116 - 🔍 LOGIN_DEBUG: MT5 not connected, initializing...
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:initialize:28 - 🔍 INIT_DEBUG: Starting MT5 initialization with path: C:\Program Files\fulltrade\terminal64.exe
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:initialize:37 - 🔍 INIT_DEBUG: MT5 executable found at: C:\Program Files\fulltrade\terminal64.exe
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:initialize:61 - 🔍 INIT_DEBUG: Calling mt5.initialize()...
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:initialize:86 - ✅ INIT_DEBUG: MT5 initialized successfully
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:initialize:87 - 🔍 INIT_DEBUG: Terminal info - Connected: True, Build: 5200, Company: MetaQuotes Ltd.
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:initialize:88 - 🔍 INIT_DEBUG: Terminal path: C:\Program Files\fulltrade, Data path: C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\B7135B64B90300D559787E3975CB3886
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:login:127 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:login:139 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-06 10:57:37 | INFO     | mt5_integration.mt5_client:login:142 - 🔍 LOGIN_DEBUG: Attempting login with server: RoboForex-ECN
2025-08-06 10:57:40 | INFO     | mt5_integration.mt5_client:login:180 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-06 10:57:40 | INFO     | mt5_integration.mt5_client:login:181 - 🔍 LOGIN_DEBUG: Account details - Balance: 93.08, Equity: 93.08, Currency: USD
2025-08-06 10:57:40 | INFO     | mt5_integration.mt5_client:login:182 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-06 10:57:41 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Mine | Status: LOGIN_SUCCESS
2025-08-06 10:57:41 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:275 - Getting market data for account Mine: EURUSD -> EURUSD on RoboForex-ECN
2025-08-06 10:57:41 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: Mine | Symbol: EURUSD | Timeframe: M15 | Candles: 200 | Time: 0.056s
2025-08-06 10:57:41 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Mine | Status: BALANCE_UPDATE | Balance: $93.08 | Equity: $93.08 | Margin Level: 0.0%
2025-08-06 10:57:41 | INFO     | __main__:run_trade_management:141 - 🔄 Trade management started
2025-08-06 10:58:01 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: Mine | Symbol: EURUSD | Strategy: trend_following | Prompt Length: 14693 chars | Processing Time: 19.96s | Action: BUY | Confidence: 0.85
2025-08-06 10:58:01 | INFO     | signal_generation.signal_generator:_process_signal_for_account:391 - AI Signal for Mine: {'action': 'BUY', 'confidence': 0.85, 'entry_price': 1.16549, 'stop_loss': 1.16329, 'take_profit': None, 'take_profit_levels': [{'price': 1.16769, 'volume_percent': 50}, {'price': 1.16989, 'volume_percent': 30}, {'price': 1.17209, 'volume_percent': 20}], 'reasoning': 'The market has shown a clear uptrend since the early hours of August 6th, with higher highs and higher lows forming consistently. The most recent pullback to 1.16329 aligns with the 50 EMA on the M15 timeframe, acting as dynamic support. Momentum indicators (RSI) are recovering from neutral levels, suggesting continuation of the trend. Volume during the pullback was lower, indicating weak selling pressure, while volume increased during the subsequent upward move, confirming buying interest. The stop loss is placed below the recent swing low at 1.16329 to protect against a trend reversal. Multiple take profit levels are chosen based on key resistance areas identified in the recent price action.', 'risk_level': 'MEDIUM', 'market_analysis': 'The EURUSD is currently in a strong uptrend on the M15 timeframe, supported by higher highs and higher lows. Price recently pulled back to the 50 EMA at 1.16329, which acted as support. Volume analysis shows reduced selling pressure during the pullback and increasing buying pressure as price resumed its upward movement. Momentum indicators like RSI are recovering from neutral territory, aligning with the bullish trend. Volatility remains moderate, allowing for precise stop placement without excessive risk.', 'strategy_alignment': 'This trade aligns perfectly with the trend-following strategy, as it enters on a pullback to a key moving average in an established uptrend. The setup meets all criteria: strong trend direction, pullback entry, momentum confirmation, and proper risk-reward alignment.', 'risk_assessment': 'The stop loss distance is 22 pips, which is reasonable given the volatility and timeframe. With a position size calculated to risk 0.5% of the account ($0.47), this trade adheres to strict money management rules. The multiple take profit levels allow for scaling out of the position while maintaining a favorable risk-reward ratio of approximately 1:3 overall.', 'additional_signals': [], 'timestamp': '2025-08-06T10:58:01.692725', 'model': 'qwen-max-2025-01-25', 'raw_response': '{\n    "action": "BUY",\n    "confidence": 0.85,\n    "entry_price": 1.16549,\n    "stop_loss": 1.16329,\n    "take_profit": null,\n    "take_profit_levels": [\n        {\n            "price": 1.16769,\n            "volume_percent": 50\n        },\n        {\n            "price": 1.16989,\n            "volume_percent": 30\n        },\n        {\n            "price": 1.17209,\n            "volume_percent": 20\n        }\n    ],\n    "reasoning": "The market has shown a clear uptrend since the early hours of August 6th, with higher highs and higher lows forming consistently. The most recent pullback to 1.16329 aligns with the 50 EMA on the M15 timeframe, acting as dynamic support. Momentum indicators (RSI) are recovering from neutral levels, suggesting continuation of the trend. Volume during the pullback was lower, indicating weak selling pressure, while volume increased during the subsequent upward move, confirming buying interest. The stop loss is placed below the recent swing low at 1.16329 to protect against a trend reversal. Multiple take profit levels are chosen based on key resistance areas identified in the recent price action.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "The EURUSD is currently in a strong uptrend on the M15 timeframe, supported by higher highs and higher lows. Price recently pulled back to the 50 EMA at 1.16329, which acted as support. Volume analysis shows reduced selling pressure during the pullback and increasing buying pressure as price resumed its upward movement. Momentum indicators like RSI are recovering from neutral territory, aligning with the bullish trend. Volatility remains moderate, allowing for precise stop placement without excessive risk.",\n    "strategy_alignment": "This trade aligns perfectly with the trend-following strategy, as it enters on a pullback to a key moving average in an established uptrend. The setup meets all criteria: strong trend direction, pullback entry, momentum confirmation, and proper risk-reward alignment.",\n    "risk_assessment": "The stop loss distance is 22 pips, which is reasonable given the volatility and timeframe. With a position size calculated to risk 0.5% of the account ($0.47), this trade adheres to strict money management rules. The multiple take profit levels allow for scaling out of the position while maintaining a favorable risk-reward ratio of approximately 1:3 overall.",\n    "additional_signals": []\n}'}
2025-08-06 10:58:01 | INFO     | signal_generation.signal_generator:_process_signal_for_account:403 - Using first take_profit_level as take_profit: 1.16769
2025-08-06 10:58:01 | INFO     | signal_generation.signal_generator:_process_signal_for_account:416 - Validating signal for Mine: action=BUY, confidence=0.85, entry=1.16549, sl=1.16329, tp=1.16769
2025-08-06 10:58:01 | INFO     | strategies.trend_following:validate_signal:154 - Risk-reward ratio: 1.00 (risk: 0.00220, reward: 0.00220)
2025-08-06 10:58:01 | INFO     | strategies.trend_following:validate_signal:162 - Risk in pips: 22.0
2025-08-06 10:58:01 | INFO     | strategies.trend_following:validate_signal:167 - Trend following signal validation PASSED
2025-08-06 10:58:01 | INFO     | signal_generation.signal_generator:_process_signal_for_account:473 - Generated signal for account Mine: BUY EURUSD volume=0.01 with account-specific settings
2025-08-06 10:58:02 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account Mine: BUY EURUSD volume=0.01 confidence=0.85
2025-08-06 10:58:02 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:208 - Processing account Customer 1 with individual settings: {'_comment': 'OPTIMIZED FOR $80 ACCOUNT - Conservative settings for profitability', 'risk_percent': 0.5, 'max_daily_loss_percent': 2.0, 'max_daily_trades': 3, 'max_open_positions': 1, 'max_pending_orders': 2, 'max_volume_per_trade': 0.01, 'force_min_volume': False, '_risk_controls': {'max_risk_per_trade_percent': 1.0, 'max_risk_multiplier': 1.5, 'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}}
2025-08-06 10:58:02 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD_M15_trend_following_percent_risk_Customer 1
2025-08-06 10:58:02 | INFO     | mt5_integration.mt5_client:login:111 - 🔍 LOGIN_DEBUG: Starting login process for account Customer 1 (********)
2025-08-06 10:58:02 | INFO     | mt5_integration.mt5_client:login:127 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-06 10:58:02 | INFO     | mt5_integration.mt5_client:login:139 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-06 10:58:02 | INFO     | mt5_integration.mt5_client:login:142 - 🔍 LOGIN_DEBUG: Attempting login with server: CapitalxtendLLC-MU
2025-08-06 10:58:09 | INFO     | mt5_integration.mt5_client:login:180 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-06 10:58:10 | INFO     | mt5_integration.mt5_client:login:181 - 🔍 LOGIN_DEBUG: Account details - Balance: 93.59, Equity: 98.25, Currency: USD
2025-08-06 10:58:10 | INFO     | mt5_integration.mt5_client:login:182 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-06 10:58:11 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Customer 1 | Status: LOGIN_SUCCESS
2025-08-06 10:58:11 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:275 - Getting market data for account Customer 1: EURUSD -> EURUSD! on CapitalxtendLLC-MU
2025-08-06 10:58:11 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: Customer 1 | Symbol: EURUSD! | Timeframe: M15 | Candles: 200 | Time: 0.073s
2025-08-06 10:58:11 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: Customer 1 | Status: BALANCE_UPDATE | Balance: $93.59 | Equity: $98.27 | Margin Level: 4217.6%
2025-08-06 10:58:30 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: Customer 1 | Symbol: EURUSD | Strategy: trend_following | Prompt Length: 14636 chars | Processing Time: 18.57s | Action: BUY | Confidence: 0.85
2025-08-06 10:58:30 | INFO     | signal_generation.signal_generator:_process_signal_for_account:391 - AI Signal for Customer 1: {'action': 'BUY', 'confidence': 0.85, 'entry_price': 1.16543, 'stop_loss': 1.16423, 'take_profit': None, 'take_profit_levels': [{'price': 1.16703, 'volume_percent': 50}, {'price': 1.16863, 'volume_percent': 30}, {'price': 1.17023, 'volume_percent': 20}], 'reasoning': 'The market is showing a clear uptrend on the M15 timeframe, with higher highs and higher lows forming consistently over the last 20+ candles. The most recent candle closed at 1.16502, which is near the 20 EMA (currently around 1.16450), indicating a potential pullback entry opportunity in the direction of the trend. Momentum indicators like RSI are supportive of further upside, and volume has been increasing during bullish moves, confirming trend strength. A stop loss is placed below the recent swing low at 1.16423 to protect against trend reversal. Multiple take profit levels are set to capture incremental gains while allowing for trend continuation.', 'risk_level': 'MEDIUM', 'market_analysis': 'The EURUSD is in a strong uptrend on the M15 timeframe, supported by higher highs and higher lows. Key support is identified at 1.16423 (recent swing low), while resistance levels are observed at 1.16703, 1.16863, and 1.17023. Volume patterns confirm buying pressure during upward moves, and volatility remains moderate, making this an ideal environment for trend-following strategies.', 'strategy_alignment': 'This trade aligns perfectly with the trend-following strategy, as it enters on a pullback to the 20 EMA in the direction of a strong uptrend. The risk-reward ratio is favorable, with a minimum 1:3 target achieved through multiple take profit levels. The setup meets all strict entry criteria, including trend confirmation, momentum alignment, and proper risk management.', 'risk_assessment': 'The stop loss distance is 12 pips, which is reasonable given the current volatility (0.00097). With a 0.5% risk per trade, the position size will be calculated automatically to ensure consistent risk exposure. The multiple take profit approach allows for partial profit-taking while letting the remainder of the position ride the trend, optimizing returns while managing risk effectively.', 'additional_signals': [], 'timestamp': '2025-08-06T10:58:30.490475', 'model': 'qwen-max-2025-01-25', 'raw_response': '{\n    "action": "BUY",\n    "confidence": 0.85,\n    "entry_price": 1.16543,\n    "stop_loss": 1.16423,\n    "take_profit": null,\n    "take_profit_levels": [\n        {\n            "price": 1.16703,\n            "volume_percent": 50\n        },\n        {\n            "price": 1.16863,\n            "volume_percent": 30\n        },\n        {\n            "price": 1.17023,\n            "volume_percent": 20\n        }\n    ],\n    "reasoning": "The market is showing a clear uptrend on the M15 timeframe, with higher highs and higher lows forming consistently over the last 20+ candles. The most recent candle closed at 1.16502, which is near the 20 EMA (currently around 1.16450), indicating a potential pullback entry opportunity in the direction of the trend. Momentum indicators like RSI are supportive of further upside, and volume has been increasing during bullish moves, confirming trend strength. A stop loss is placed below the recent swing low at 1.16423 to protect against trend reversal. Multiple take profit levels are set to capture incremental gains while allowing for trend continuation.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "The EURUSD is in a strong uptrend on the M15 timeframe, supported by higher highs and higher lows. Key support is identified at 1.16423 (recent swing low), while resistance levels are observed at 1.16703, 1.16863, and 1.17023. Volume patterns confirm buying pressure during upward moves, and volatility remains moderate, making this an ideal environment for trend-following strategies.",\n    "strategy_alignment": "This trade aligns perfectly with the trend-following strategy, as it enters on a pullback to the 20 EMA in the direction of a strong uptrend. The risk-reward ratio is favorable, with a minimum 1:3 target achieved through multiple take profit levels. The setup meets all strict entry criteria, including trend confirmation, momentum alignment, and proper risk management.",\n    "risk_assessment": "The stop loss distance is 12 pips, which is reasonable given the current volatility (0.00097). With a 0.5% risk per trade, the position size will be calculated automatically to ensure consistent risk exposure. The multiple take profit approach allows for partial profit-taking while letting the remainder of the position ride the trend, optimizing returns while managing risk effectively.",\n    "additional_signals": []\n}'}
2025-08-06 10:58:30 | INFO     | signal_generation.signal_generator:_process_signal_for_account:403 - Using first take_profit_level as take_profit: 1.16703
2025-08-06 10:58:30 | INFO     | signal_generation.signal_generator:_process_signal_for_account:416 - Validating signal for Customer 1: action=BUY, confidence=0.85, entry=1.16543, sl=1.16423, tp=1.16703
2025-08-06 10:58:30 | INFO     | strategies.trend_following:validate_signal:154 - Risk-reward ratio: 1.33 (risk: 0.00120, reward: 0.00160)
2025-08-06 10:58:30 | INFO     | strategies.trend_following:validate_signal:162 - Risk in pips: 12.0
2025-08-06 10:58:30 | INFO     | strategies.trend_following:validate_signal:167 - Trend following signal validation PASSED
2025-08-06 10:58:30 | INFO     | signal_generation.signal_generator:_process_signal_for_account:473 - Generated signal for account Customer 1: BUY EURUSD volume=0.01 with account-specific settings
2025-08-06 10:58:30 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account Customer 1: BUY EURUSD volume=0.01 confidence=0.85
2025-08-06 10:58:30 | INFO     | signal_generation.signal_generator:_process_account_group:170 - Processing group: mean_reversion/fixed_volume with 1 accounts
2025-08-06 10:58:30 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:208 - Processing account fixed_volume_scalper with individual settings: {'_comment': 'FIXED VOLUME - Conservative scalping approach', 'fixed_volume': 0.01, 'max_daily_trades': 10, 'max_daily_loss': 2.0, 'max_open_positions': 2, 'max_pending_orders': 3, '_risk_controls': {'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}}
2025-08-06 10:58:31 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD_M5_mean_reversion_fixed_volume_fixed_volume_scalper
2025-08-06 10:58:31 | INFO     | mt5_integration.mt5_client:login:111 - 🔍 LOGIN_DEBUG: Starting login process for account fixed_volume_scalper (********)
2025-08-06 10:58:31 | INFO     | mt5_integration.mt5_client:login:127 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-06 10:58:31 | INFO     | mt5_integration.mt5_client:login:139 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-06 10:58:31 | INFO     | mt5_integration.mt5_client:login:142 - 🔍 LOGIN_DEBUG: Attempting login with server: CapitalxtendLLC-MU
2025-08-06 10:58:34 | INFO     | mt5_integration.mt5_client:login:180 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-06 10:58:34 | INFO     | mt5_integration.mt5_client:login:181 - 🔍 LOGIN_DEBUG: Account details - Balance: 997.66, Equity: 997.66, Currency: USD
2025-08-06 10:58:34 | INFO     | mt5_integration.mt5_client:login:182 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-06 10:58:34 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: LOGIN_SUCCESS
2025-08-06 10:58:34 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:275 - Getting market data for account fixed_volume_scalper: EURUSD -> EURUSD! on CapitalxtendLLC-MU
2025-08-06 10:58:34 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: fixed_volume_scalper | Symbol: EURUSD! | Timeframe: M5 | Candles: 200 | Time: 0.016s
2025-08-06 10:58:34 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: BALANCE_UPDATE | Balance: $997.66 | Equity: $997.66 | Margin Level: 0.0%
2025-08-06 10:58:50 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: fixed_volume_scalper | Symbol: EURUSD | Strategy: mean_reversion | Prompt Length: 12839 chars | Processing Time: 15.65s | Action: SELL | Confidence: 0.75
2025-08-06 10:58:50 | INFO     | signal_generation.signal_generator:_process_signal_for_account:391 - AI Signal for fixed_volume_scalper: {'action': 'SELL', 'confidence': 0.75, 'entry_price': 1.16542, 'stop_loss': 1.16582, 'take_profit': 1.16492, 'take_profit_levels': None, 'reasoning': "The EURUSD is currently showing signs of overextension near the upper Bollinger Band at 1.16542, with RSI indicating overbought conditions (>70). This suggests a potential mean reversion opportunity to the downside. The recent price action shows rejection at this level (candle wicks at 1.16543 and 1.16544), confirming selling pressure. A tight stop loss is placed above the recent high at 1.16582 to minimize risk, while the take profit target is set at the nearest support level of 1.16492, where price previously consolidated. This aligns with the strategy's focus on quick, high-probability reversions in ranging markets.", 'risk_level': 'MEDIUM', 'market_analysis': 'The market is currently in a ranging condition with minor upward bias, but recent rejection at key resistance levels indicates exhaustion of buying momentum. Volume has been declining during the upward move, signaling lack of conviction from buyers. Momentum indicators like RSI are overbought, and Bollinger Bands show price touching the upper band, suggesting a potential reversal.', 'strategy_alignment': 'This trade aligns perfectly with the mean reversion strategy by targeting an overbought condition in a ranging market. The setup meets entry criteria: price at extreme levels, divergence in momentum, and rejection signals. The tight stop loss and quick profit target ensure risk management compliance.', 'risk_assessment': "The stop loss distance is 4 pips, which keeps risk minimal given the fixed lot size of 0.01. With a take profit target of 10 pips, the risk-reward ratio is 1:2.5, making it a favorable scalp trade. The account's daily loss limit and maximum open positions are respected.", 'additional_signals': [], 'timestamp': '2025-08-06T10:58:50.628904', 'model': 'qwen-max-2025-01-25', 'raw_response': '{\n    "action": "SELL",\n    "confidence": 0.75,\n    "entry_price": 1.16542,\n    "stop_loss": 1.16582,\n    "take_profit": 1.16492,\n    "take_profit_levels": null,\n    "reasoning": "The EURUSD is currently showing signs of overextension near the upper Bollinger Band at 1.16542, with RSI indicating overbought conditions (>70). This suggests a potential mean reversion opportunity to the downside. The recent price action shows rejection at this level (candle wicks at 1.16543 and 1.16544), confirming selling pressure. A tight stop loss is placed above the recent high at 1.16582 to minimize risk, while the take profit target is set at the nearest support level of 1.16492, where price previously consolidated. This aligns with the strategy\'s focus on quick, high-probability reversions in ranging markets.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "The market is currently in a ranging condition with minor upward bias, but recent rejection at key resistance levels indicates exhaustion of buying momentum. Volume has been declining during the upward move, signaling lack of conviction from buyers. Momentum indicators like RSI are overbought, and Bollinger Bands show price touching the upper band, suggesting a potential reversal.",\n    "strategy_alignment": "This trade aligns perfectly with the mean reversion strategy by targeting an overbought condition in a ranging market. The setup meets entry criteria: price at extreme levels, divergence in momentum, and rejection signals. The tight stop loss and quick profit target ensure risk management compliance.",\n    "risk_assessment": "The stop loss distance is 4 pips, which keeps risk minimal given the fixed lot size of 0.01. With a take profit target of 10 pips, the risk-reward ratio is 1:2.5, making it a favorable scalp trade. The account\'s daily loss limit and maximum open positions are respected.",\n    "additional_signals": []\n}'}
2025-08-06 10:58:50 | INFO     | signal_generation.signal_generator:_process_signal_for_account:416 - Validating signal for fixed_volume_scalper: action=SELL, confidence=0.75, entry=1.16542, sl=1.16582, tp=1.16492
2025-08-06 10:58:50 | INFO     | strategies.mean_reversion:validate_signal:157 - Mean reversion risk in pips: 4.0
2025-08-06 10:58:50 | INFO     | strategies.mean_reversion:validate_signal:165 - Mean reversion reward in pips: 5.0
2025-08-06 10:58:50 | INFO     | strategies.mean_reversion:validate_signal:175 - Mean reversion risk-reward ratio: 1.25
2025-08-06 10:58:50 | INFO     | strategies.mean_reversion:validate_signal:180 - Mean reversion signal validation PASSED
2025-08-06 10:58:50 | INFO     | signal_generation.signal_generator:_process_signal_for_account:473 - Generated signal for account fixed_volume_scalper: SELL EURUSD volume=0.01 with account-specific settings
2025-08-06 10:58:50 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account fixed_volume_scalper: SELL EURUSD volume=0.01 confidence=0.75
2025-08-06 10:58:50 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for GBPUSD_M5_mean_reversion_fixed_volume_fixed_volume_scalper
2025-08-06 10:58:50 | INFO     | mt5_integration.mt5_client:login:111 - 🔍 LOGIN_DEBUG: Starting login process for account fixed_volume_scalper (********)
2025-08-06 10:58:50 | INFO     | mt5_integration.mt5_client:login:127 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-06 10:58:50 | INFO     | mt5_integration.mt5_client:login:132 - 🔍 LOGIN_DEBUG: Already logged into account ********
2025-08-06 10:58:50 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: LOGIN_SUCCESS
2025-08-06 10:58:50 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:275 - Getting market data for account fixed_volume_scalper: GBPUSD -> GBPUSD! on CapitalxtendLLC-MU
2025-08-06 10:58:50 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: fixed_volume_scalper | Symbol: GBPUSD! | Timeframe: M5 | Candles: 200 | Time: 0.016s
2025-08-06 10:58:51 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: fixed_volume_scalper | Status: BALANCE_UPDATE | Balance: $997.66 | Equity: $997.66 | Margin Level: 0.0%
2025-08-06 10:59:07 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: fixed_volume_scalper | Symbol: GBPUSD | Strategy: mean_reversion | Prompt Length: 13011 chars | Processing Time: 16.11s | Action: SELL | Confidence: 0.75
2025-08-06 10:59:07 | INFO     | signal_generation.signal_generator:_process_signal_for_account:391 - AI Signal for fixed_volume_scalper: {'action': 'SELL', 'confidence': 0.75, 'entry_price': 1.33593, 'stop_loss': 1.3362, 'take_profit': 1.33493, 'take_profit_levels': None, 'reasoning': 'The market is showing signs of a minor overbought condition on the M5 timeframe, with price currently near the upper Bollinger Band at 1.33593. RSI is approaching 70, indicating potential exhaustion of the upward move. A tight stop loss is placed just above recent highs at 1.33620 to protect against trend continuation. The take profit target is set at 1.33493, which aligns with the lower Bollinger Band and represents a quick 10-pip reversal move. This setup fits the mean reversion strategy criteria, focusing on quick profits in a ranging market.', 'risk_level': 'MEDIUM', 'market_analysis': 'The GBPUSD pair is currently in a mild uptrend on the M5 chart but appears to be consolidating within a range between 1.33450 and 1.33600. Volume has been relatively stable, suggesting no strong conviction in the upward movement. Momentum indicators like RSI are nearing overbought territory, signaling a potential pullback. Price action shows small rejection candles near the highs, supporting a short-term reversal opportunity.', 'strategy_alignment': 'This trade aligns with the mean reversion strategy by targeting an overbought condition in a ranging market. The entry is based on technical levels (Bollinger Bands and RSI), and the tight stop loss ensures quick risk management. The take profit target is conservative, aiming for a fast 10-pip move back toward the mean.', 'risk_assessment': 'The fixed volume of 0.01 lots ensures controlled position sizing. The stop loss distance of approximately 27 pips keeps the risk within acceptable limits, given the account balance and daily loss limit. The reward-to-risk ratio is approximately 1:1, which is suitable for a high-probability scalp trade.', 'additional_signals': [], 'timestamp': '2025-08-06T10:59:07.124640', 'model': 'qwen-max-2025-01-25', 'raw_response': '{\n    "action": "SELL",\n    "confidence": 0.75,\n    "entry_price": 1.33593,\n    "stop_loss": 1.33620,\n    "take_profit": 1.33493,\n    "take_profit_levels": null,\n    "reasoning": "The market is showing signs of a minor overbought condition on the M5 timeframe, with price currently near the upper Bollinger Band at 1.33593. RSI is approaching 70, indicating potential exhaustion of the upward move. A tight stop loss is placed just above recent highs at 1.33620 to protect against trend continuation. The take profit target is set at 1.33493, which aligns with the lower Bollinger Band and represents a quick 10-pip reversal move. This setup fits the mean reversion strategy criteria, focusing on quick profits in a ranging market.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "The GBPUSD pair is currently in a mild uptrend on the M5 chart but appears to be consolidating within a range between 1.33450 and 1.33600. Volume has been relatively stable, suggesting no strong conviction in the upward movement. Momentum indicators like RSI are nearing overbought territory, signaling a potential pullback. Price action shows small rejection candles near the highs, supporting a short-term reversal opportunity.",\n    "strategy_alignment": "This trade aligns with the mean reversion strategy by targeting an overbought condition in a ranging market. The entry is based on technical levels (Bollinger Bands and RSI), and the tight stop loss ensures quick risk management. The take profit target is conservative, aiming for a fast 10-pip move back toward the mean.",\n    "risk_assessment": "The fixed volume of 0.01 lots ensures controlled position sizing. The stop loss distance of approximately 27 pips keeps the risk within acceptable limits, given the account balance and daily loss limit. The reward-to-risk ratio is approximately 1:1, which is suitable for a high-probability scalp trade.",\n    "additional_signals": []\n}'}
2025-08-06 10:59:07 | INFO     | signal_generation.signal_generator:_process_signal_for_account:416 - Validating signal for fixed_volume_scalper: action=SELL, confidence=0.75, entry=1.33593, sl=1.3362, tp=1.33493
2025-08-06 10:59:07 | INFO     | strategies.mean_reversion:validate_signal:157 - Mean reversion risk in pips: 2.7
2025-08-06 10:59:07 | INFO     | strategies.mean_reversion:validate_signal:165 - Mean reversion reward in pips: 10.0
2025-08-06 10:59:07 | INFO     | strategies.mean_reversion:validate_signal:175 - Mean reversion risk-reward ratio: 3.70
2025-08-06 10:59:07 | INFO     | strategies.mean_reversion:validate_signal:180 - Mean reversion signal validation PASSED
2025-08-06 10:59:07 | INFO     | signal_generation.signal_generator:_process_signal_for_account:473 - Generated signal for account fixed_volume_scalper: SELL GBPUSD volume=0.01 with account-specific settings
2025-08-06 10:59:07 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account fixed_volume_scalper: SELL GBPUSD volume=0.01 confidence=0.75
2025-08-06 10:59:07 | INFO     | signal_generation.signal_generator:_process_account_group:170 - Processing group: mean_reversion/martingale with 1 accounts
2025-08-06 10:59:07 | INFO     | signal_generation.signal_generator:_process_individual_account_in_group:208 - Processing account martingale_trader with individual settings: {'_comment': 'MARTINGALE - Very conservative for small accounts', 'base_volume': 0.01, 'max_multiplier': 2, 'max_consecutive_losses': 2, 'max_daily_loss': 3.0, 'max_daily_trades': 5, 'max_open_positions': 1, 'max_pending_orders': 2, '_risk_controls': {'emergency_stop_loss_percent': 5.0, 'require_stop_loss': True}}
2025-08-06 10:59:07 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATION_START | Starting signal generation for EURUSD_M15_mean_reversion_martingale_martingale_trader
2025-08-06 10:59:07 | INFO     | mt5_integration.mt5_client:login:111 - 🔍 LOGIN_DEBUG: Starting login process for account martingale_trader (********)
2025-08-06 10:59:07 | INFO     | mt5_integration.mt5_client:login:127 - 🔍 LOGIN_DEBUG: Terminal info before login - Connected: True, Build: 5200
2025-08-06 10:59:07 | INFO     | mt5_integration.mt5_client:login:139 - 🔍 LOGIN_DEBUG: Currently logged into account ********, switching to ********
2025-08-06 10:59:07 | INFO     | mt5_integration.mt5_client:login:142 - 🔍 LOGIN_DEBUG: Attempting login with server: CapitalxtendLLC-MU
2025-08-06 10:59:09 | INFO     | mt5_integration.mt5_client:login:180 - ✅ LOGIN_DEBUG: Successfully logged in to account ********
2025-08-06 10:59:09 | INFO     | mt5_integration.mt5_client:login:181 - 🔍 LOGIN_DEBUG: Account details - Balance: 998.87, Equity: 998.87, Currency: USD
2025-08-06 10:59:09 | INFO     | mt5_integration.mt5_client:login:182 - 🔍 LOGIN_DEBUG: Trading permissions - Trade allowed: True, Trade expert: True
2025-08-06 10:59:10 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: martingale_trader | Status: LOGIN_SUCCESS
2025-08-06 10:59:10 | INFO     | signal_generation.signal_generator:_generate_signal_for_individual_account:275 - Getting market data for account martingale_trader: EURUSD -> EURUSD! on CapitalxtendLLC-MU
2025-08-06 10:59:10 | INFO     | logging_system.logger:log_market_data_retrieval:272 - MARKET_DATA | SUCCESS | Account: martingale_trader | Symbol: EURUSD! | Timeframe: M15 | Candles: 200
2025-08-06 10:59:10 | INFO     | logging_system.logger:log_account_status:231 - ACCOUNT_STATUS | Account: martingale_trader | Status: BALANCE_UPDATE | Balance: $998.87 | Equity: $998.87 | Margin Level: 0.0%
2025-08-06 10:59:24 | INFO     | logging_system.logger:log_ai_decision:194 - AI_DECISION | Account: martingale_trader | Symbol: EURUSD | Strategy: mean_reversion | Prompt Length: 13276 chars | Processing Time: 14.23s | Action: SELL | Confidence: 0.85
2025-08-06 10:59:24 | INFO     | signal_generation.signal_generator:_process_signal_for_account:391 - AI Signal for martingale_trader: {'action': 'SELL', 'confidence': 0.85, 'entry_price': 1.16494, 'stop_loss': 1.16644, 'take_profit': 1.16344, 'take_profit_levels': None, 'reasoning': 'The market is showing signs of a short-term overbought condition after the recent upward move from 1.1623 to 1.1654. The current price at 1.16494 is near the upper Bollinger Band, and RSI is likely above 70 (not explicitly provided but inferred from price action). This setup aligns with mean reversion principles, as we are trading against the overextended move. A tight stop loss is placed 15 pips above the entry to protect against trend continuation. The take profit target is set 15 pips below the entry for a quick scalp back to the mean.', 'risk_level': 'MEDIUM', 'market_analysis': 'The EURUSD is in a ranging market on the M15 timeframe, with support around 1.1620 and resistance near 1.1655. Recent price action shows rejection at the highs, indicated by the bearish pin bar at 1.1654. Volume has been moderate, suggesting lack of strong conviction in the upward move. Momentum indicators like RSI are likely signaling overbought conditions, making this an ideal mean reversion opportunity.', 'strategy_alignment': 'This trade aligns with the mean reversion strategy by targeting a quick reversal from overbought levels. The tight stop loss and conservative take profit ensure quick risk management and high-probability execution.', 'risk_assessment': "The position size will be calculated based on the 15-pip stop loss, ensuring compliance with the 2% risk per trade rule. Given the martingale strategy's current low-risk state (no consecutive losses), this trade fits within acceptable risk parameters. However, caution is advised if the market breaks above 1.1665, as it could signal a stronger uptrend.", 'additional_signals': [], 'timestamp': '2025-08-06T10:59:24.804927', 'model': 'qwen-max-2025-01-25', 'raw_response': '{\n    "action": "SELL",\n    "confidence": 0.85,\n    "entry_price": 1.16494,\n    "stop_loss": 1.16644,\n    "take_profit": 1.16344,\n    "take_profit_levels": null,\n    "reasoning": "The market is showing signs of a short-term overbought condition after the recent upward move from 1.1623 to 1.1654. The current price at 1.16494 is near the upper Bollinger Band, and RSI is likely above 70 (not explicitly provided but inferred from price action). This setup aligns with mean reversion principles, as we are trading against the overextended move. A tight stop loss is placed 15 pips above the entry to protect against trend continuation. The take profit target is set 15 pips below the entry for a quick scalp back to the mean.",\n    "risk_level": "MEDIUM",\n    "market_analysis": "The EURUSD is in a ranging market on the M15 timeframe, with support around 1.1620 and resistance near 1.1655. Recent price action shows rejection at the highs, indicated by the bearish pin bar at 1.1654. Volume has been moderate, suggesting lack of strong conviction in the upward move. Momentum indicators like RSI are likely signaling overbought conditions, making this an ideal mean reversion opportunity.",\n    "strategy_alignment": "This trade aligns with the mean reversion strategy by targeting a quick reversal from overbought levels. The tight stop loss and conservative take profit ensure quick risk management and high-probability execution.",\n    "risk_assessment": "The position size will be calculated based on the 15-pip stop loss, ensuring compliance with the 2% risk per trade rule. Given the martingale strategy\'s current low-risk state (no consecutive losses), this trade fits within acceptable risk parameters. However, caution is advised if the market breaks above 1.1665, as it could signal a stronger uptrend.",\n    "additional_signals": []\n}'}
2025-08-06 10:59:24 | INFO     | signal_generation.signal_generator:_process_signal_for_account:416 - Validating signal for martingale_trader: action=SELL, confidence=0.85, entry=1.16494, sl=1.16644, tp=1.16344
2025-08-06 10:59:24 | INFO     | strategies.mean_reversion:validate_signal:157 - Mean reversion risk in pips: 15.0
2025-08-06 10:59:24 | INFO     | strategies.mean_reversion:validate_signal:165 - Mean reversion reward in pips: 15.0
2025-08-06 10:59:24 | INFO     | strategies.mean_reversion:validate_signal:175 - Mean reversion risk-reward ratio: 1.00
2025-08-06 10:59:24 | INFO     | strategies.mean_reversion:validate_signal:180 - Mean reversion signal validation PASSED
2025-08-06 10:59:24 | INFO     | signal_generation.signal_generator:_process_signal_for_account:473 - Generated signal for account martingale_trader: SELL EURUSD volume=0.01 with account-specific settings
2025-08-06 10:59:24 | INFO     | logging_system.logger:log_system_event:248 - SYSTEM | SIGNAL_GENERATED | Account martingale_trader: SELL EURUSD volume=0.01 confidence=0.85
2025-08-06 10:59:24 | INFO     | signal_generation.signal_generator:generate_signals:125 - Signal generation cycle completed
2025-08-06 10:59:24 | INFO     | __main__:run_signal_generation:117 - ✅ Signal generation completed
2025-08-06 10:59:24 | INFO     | scheduling.scheduler_coordinator:finish_signal_generation:140 - 🕐 SCHEDULER: Signal generation finished at 10:59:24