#!/usr/bin/env python3
"""
Test Error Fixes for MT5 Integration
Tests the fixes for Error 10016 (Invalid Stops) and Error 10015 (Invalid Price)
"""

import sys
import os
import asyncio
from datetime import datetime
from typing import Dict, Any

# Add src to path
sys.path.append('src')

def test_mt5_connection():
    """Test MT5 connection and basic functionality"""
    print("=== TESTING MT5 CONNECTION ===")
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5 initialization failed")
            return False
            
        print("✅ MT5 initialized successfully")
        
        # Test symbol info
        symbol = 'EURUSD!'
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            print(f"✅ Symbol {symbol} found")
            print(f"   Digits: {symbol_info.digits}")
            print(f"   Point: {symbol_info.point}")
            print(f"   Stops level: {symbol_info.trade_stops_level}")
        else:
            print(f"❌ Symbol {symbol} not found")
            
        # Test tick data
        tick = mt5.symbol_info_tick(symbol)
        if tick:
            print(f"✅ Tick data available - Bid: {tick.bid}, Ask: {tick.ask}")
        else:
            print(f"❌ No tick data for {symbol}")
            
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ MT5 test failed: {e}")
        return False

def test_price_normalization():
    """Test price normalization fixes"""
    print("\n=== TESTING PRICE NORMALIZATION FIXES ===")
    try:
        from mt5_integration.mt5_client import MT5Client
        
        client = MT5Client()
        
        # Test cases for price normalization
        test_cases = [
            ("EURUSD!", 1.123456789, "Normal price"),
            ("EURUSD!", 0.0, "Zero price"),
            ("EURUSD!", -1.0, "Negative price"),
            ("EURUSD!", None, "None price"),
            ("USDJPY!", 150.123456, "JPY pair price"),
        ]
        
        for symbol, price, description in test_cases:
            try:
                normalized = client._normalize_price(symbol, price)
                print(f"✅ {description}: {price} -> {normalized}")
            except Exception as e:
                print(f"❌ {description}: {price} -> Error: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Price normalization test failed: {e}")
        return False

def test_trade_validation():
    """Test trade validation fixes"""
    print("\n=== TESTING TRADE VALIDATION FIXES ===")
    try:
        from validation.trade_validator import TradeValidator
        
        validator = TradeValidator()
        
        # Test cases for trade validation
        test_cases = [
            {
                "name": "Valid signal",
                "params": {
                    "symbol": "EURUSD!",
                    "action": "BUY",
                    "volume": 0.01,
                    "price": 1.1000,
                    "stop_loss": 1.0950,
                    "take_profit": 1.1050
                }
            },
            {
                "name": "Stop loss too close",
                "params": {
                    "symbol": "EURUSD!",
                    "action": "BUY", 
                    "volume": 0.01,
                    "price": 1.1000,
                    "stop_loss": 1.0999,  # Very close
                    "take_profit": 1.1050
                }
            },
            {
                "name": "Invalid price format",
                "params": {
                    "symbol": "EURUSD!",
                    "action": "BUY",
                    "volume": 0.01,
                    "price": 1.123456789,  # Too many decimals
                    "stop_loss": 1.0950,
                    "take_profit": 1.1050
                }
            }
        ]
        
        for test_case in test_cases:
            print(f"\n  Testing: {test_case['name']}")
            try:
                result = validator.validate_signal_execution(**test_case['params'])
                print(f"    Result: {result.result.value}")
                if result.corrected_params:
                    print(f"    Corrections: {result.corrected_params}")
                if result.errors:
                    for error in result.errors:
                        print(f"    Error: {error.error_type} - {error.message}")
            except Exception as e:
                print(f"    Exception: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Trade validation test failed: {e}")
        return False

def test_signal_validators():
    """Test signal generator validators"""
    print("\n=== TESTING SIGNAL GENERATOR VALIDATORS ===")
    try:
        from signal_generation.signal_generator import SignalGenerator
        from account_management.account_manager import AccountManager
        
        signal_gen = SignalGenerator(AccountManager())
        
        # Test signal structure validation
        test_signals = [
            {"action": "BUY", "confidence": 0.8, "entry_price": 1.1000},
            {"action": "INVALID", "confidence": 0.8},  # Invalid action
            {"confidence": 0.8},  # Missing action
            {"action": "BUY", "confidence": 1.5},  # Invalid confidence
        ]
        
        print("  Signal structure validation:")
        for i, signal in enumerate(test_signals, 1):
            is_valid = signal_gen._validate_signal(signal)
            print(f"    Signal {i}: Valid = {is_valid}")
            
        # Test TP levels validation
        tp_test_cases = [
            [
                {"price": 1.1020, "volume_percent": 50},
                {"price": 1.1040, "volume_percent": 30},
                {"price": 1.1060, "volume_percent": 20}
            ],
            [
                {"price": 1.1020, "volume_percent": 60},
                {"price": 1.1040, "volume_percent": 50}  # Total = 110%
            ]
        ]
        
        print("  TP levels validation:")
        for i, tp_levels in enumerate(tp_test_cases, 1):
            is_valid = signal_gen._validate_tp_levels(tp_levels)
            print(f"    TP levels {i}: Valid = {is_valid}")
            
        return True
        
    except Exception as e:
        print(f"❌ Signal validator test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("TESTING ERROR FIXES FOR MT5 INTEGRATION")
    print("=" * 60)
    
    tests = [
        test_mt5_connection,
        test_price_normalization,
        test_trade_validation,
        test_signal_validators
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print(f"\n=== TEST RESULTS ===")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✅ All tests passed! Error fixes are working correctly.")
    else:
        print("❌ Some tests failed. Please check the output above.")

if __name__ == "__main__":
    main()
