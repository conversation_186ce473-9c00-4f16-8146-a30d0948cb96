#!/usr/bin/env python3
"""
Test script to demonstrate enhanced AI strategy selection logging
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from strategy_selection.dynamic_strategy_selector import dynamic_strategy_selector
from strategies.base_strategy import MarketData
from money_management.base_strategy import AccountInfo
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

def test_ai_strategy_selection():
    """Test AI strategy selection with detailed logging"""
    print("🤖 TESTING AI STRATEGY SELECTION WITH ENHANCED LOGGING")
    print("=" * 70)
    
    # Test Case 1: Dynamic mode enabled - AI should select strategy
    print("\n📊 TEST CASE 1: AI-Driven Dynamic Strategy Selection")
    print("-" * 50)
    
    account_config_dynamic = {
        'account_id': 'test_account_1',
        'strategy': 'trend_following',  # Default from config
        'strategy_selection': {
            'mode': 'dynamic',  # AI selection enabled
            'available_strategies': ['trend_following', 'mean_reversion', 'breakout'],
            'selection_criteria': {
                'volatility_threshold': 0.01,
                'volume_threshold': 1000,
                'trend_strength_threshold': 0.7
            }
        }
    }
    
    # Simulate trending market conditions
    market_data_trending = MarketData(
        symbol="EURUSD",
        timeframe="M15",
        candles=[],  # Not needed for strategy selection
        current_price=1.0850,
        spread=1.2,
        volume=1500,
        volatility=0.008,  # Moderate volatility
        pip_size=0.0001,
        pip_value=1.0,
        min_volume=0.01,
        max_volume=100.0,
        trend_strength=0.75  # Strong trend
    )
    
    account_info = AccountInfo(
        balance=10000.0,
        equity=10200.0,
        margin=500.0,
        free_margin=9700.0,
        margin_level=2040.0,  # High margin level
        currency="USD",
        leverage=100
    )
    
    selected_strategy = dynamic_strategy_selector.select_strategy(
        account_config_dynamic, market_data_trending, account_info
    )
    
    print(f"✅ AI Selected Strategy: {selected_strategy}")
    
    # Test Case 2: Static mode - should use config strategy
    print("\n📋 TEST CASE 2: Static Strategy Mode (No AI Selection)")
    print("-" * 50)
    
    account_config_static = {
        'account_id': 'test_account_2',
        'strategy': 'mean_reversion',  # Hardcoded strategy
        'strategy_selection': {
            'mode': 'static'  # AI selection disabled
        }
    }
    
    selected_strategy_static = dynamic_strategy_selector.select_strategy(
        account_config_static, market_data_trending, account_info
    )
    
    print(f"📋 Static Strategy Used: {selected_strategy_static}")
    
    # Test Case 3: Volatile market conditions
    print("\n🌪️  TEST CASE 3: Volatile Market Conditions")
    print("-" * 50)
    
    market_data_volatile = MarketData(
        symbol="GBPUSD",
        timeframe="M15",
        candles=[],
        current_price=1.2650,
        spread=2.1,
        volume=2500,
        volatility=0.015,  # High volatility
        pip_size=0.0001,
        pip_value=1.0,
        min_volume=0.01,
        max_volume=100.0,
        trend_strength=0.45  # Moderate trend in volatile market
    )
    
    selected_strategy_volatile = dynamic_strategy_selector.select_strategy(
        account_config_dynamic, market_data_volatile, account_info
    )
    
    print(f"🌪️  AI Selected Strategy for Volatile Market: {selected_strategy_volatile}")
    
    # Test Case 4: Low margin level (high risk)
    print("\n⚠️  TEST CASE 4: High Risk Account (Low Margin Level)")
    print("-" * 50)
    
    account_info_high_risk = AccountInfo(
        balance=5000.0,
        equity=4800.0,
        margin=4600.0,
        free_margin=200.0,
        margin_level=104.3,  # Very low margin level
        currency="USD",
        leverage=100
    )
    
    selected_strategy_high_risk = dynamic_strategy_selector.select_strategy(
        account_config_dynamic, market_data_trending, account_info_high_risk
    )
    
    print(f"⚠️  AI Selected Strategy for High Risk Account: {selected_strategy_high_risk}")
    
    print("\n" + "=" * 70)
    print("✅ AI STRATEGY SELECTION LOGGING TEST COMPLETED")
    print("📊 Check the logs above to see detailed AI reasoning and decision process")
    print("🤖 The system is using AI-driven strategy selection, NOT hardcoded values from accounts.json")

if __name__ == "__main__":
    test_ai_strategy_selection()
